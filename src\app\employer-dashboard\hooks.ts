import React, { useState, useEffect, useCallback, useMemo } from "react";
import { api } from "@/trpc/react";
import { useDebouncedCallback } from "use-debounce";
import {
	EMPLOYER_CACHE_CONFIG,
	CACHE_INVALIDATION_STRATEGIES,
	PREFETCH_STRATEGIES,
	getMissingRequiredDocuments,
	calculateDocumentCompletionPercentage
} from "./cache-config";
import { toast } from "sonner";

export const useEmployerUsers = (page: number, employerId: string) => {
	const utils = api.useContext();
	const cacheConfig = EMPLOYER_CACHE_CONFIG.users;

	const { data, isLoading, error } = api.user.getAll.useQuery({
		page,
		limit: 10,
		employerId,
	}, cacheConfig);

	// Enhanced prefetching with cache awareness
	React.useEffect(() => {
		PREFETCH_STRATEGIES.prefetchNextPage(
			utils,
			utils.user.getAll,
			data,
			page,
			{ page, limit: 10, employerId },
			cacheConfig
		);
	}, [data, page, employerId, utils, cacheConfig]);

	const createUserMutation = api.user.create.useMutation({
		onSuccess: (newUser) => {
			// Smart invalidation using centralized strategy
			CACHE_INVALIDATION_STRATEGIES.userOperation(utils, employerId, newUser.id);
			console.log("User created successfully");
		},
		// Optimistic updates for better UX
		onMutate: async (newUserData) => {
			// Cancel outgoing refetches
			await utils.user.getAll.cancel({ page, limit: 10, employerId });

			// Snapshot previous value
			const previousUsers = utils.user.getAll.getData({ page, limit: 10, employerId });

			// Optimistically update cache
			if (previousUsers) {
				utils.user.getAll.setData({ page, limit: 10, employerId }, {
					...previousUsers,
					users: [newUserData as any, ...previousUsers.users],
					totalCount: previousUsers.totalCount + 1,
				});
			}

			return { previousUsers };
		},
		onError: (err, newUserData, context) => {
			// Rollback on error
			if (context?.previousUsers) {
				utils.user.getAll.setData({ page, limit: 10, employerId }, context.previousUsers);
			}
		},
	});

	return {
		users: data?.users,
		totalPages: data?.pageCount,
		totalCount: data?.totalCount,
		isLoading,
		error,
		createUser: createUserMutation.mutate,
		isCreating: createUserMutation.isPending,
	};
};

export const useEmployerAuditLogs = (
	page: number,
	actionType: "all" | "create" | "update" | "delete" = "all",
	employerId: string,
) => {
	const utils = api.useContext();
	const cacheConfig = EMPLOYER_CACHE_CONFIG.auditLogs;

	const { data, isLoading, error } = api.auditLog.getLogs.useQuery({
		page,
		limit: 10,
		actionType,
		employerId,
	}, cacheConfig);

	// Smart prefetching for audit logs
	React.useEffect(() => {
		PREFETCH_STRATEGIES.prefetchNextPage(
			utils,
			utils.auditLog.getLogs,
			data,
			page,
			{ page, limit: 10, actionType, employerId },
			cacheConfig
		);
	}, [data, page, actionType, employerId, utils, cacheConfig]);

	return {
		logs: data?.logs,
		totalPages: data?.pageCount,
		totalCount: data?.totalCount,
		isLoading,
		error,
	};
};

export const useEmployerEmployees = (
	employerId: string,
	page: number = 1,
	search?: string,
) => {
	const utils = api.useContext();
	const cacheConfig = EMPLOYER_CACHE_CONFIG.employees;

	const { data, isLoading, error } = api.employer.getEmployees.useQuery(
		{
			employerId,
			page,
			limit: 10,
			search
		},
		{
			enabled: !!employerId,
			...cacheConfig,
		}
	);

	// Enhanced prefetching with cache awareness
	React.useEffect(() => {
		PREFETCH_STRATEGIES.prefetchNextPage(
			utils,
			utils.employer.getEmployees,
			data,
			page,
			{ employerId, page, limit: 10, search },
			cacheConfig
		);
	}, [data, page, employerId, search, utils, cacheConfig]);

	return {
		employees: data?.employees || [],
		totalPages: data?.pageCount || 0,
		totalCount: data?.totalCount || 0,
		isLoading,
		error,
	};
};

// Enhanced hook with better caching and debouncing
export const useEmployerEmployeesWithDebounce = (
	employerId: string,
	initialPage: number = 1,
) => {
	const [page, setPage] = React.useState(initialPage);
	const [search, setSearch] = React.useState("");
	const [debouncedSearch, setDebouncedSearch] = React.useState("");

	const utils = api.useContext();
	const cacheConfig = EMPLOYER_CACHE_CONFIG.employeeSearch;

	// Debounce search input with cache-aware logic
	const debouncedSetSearch = useDebouncedCallback(
		(value: string) => {
			// Only search if 2+ characters or empty (to show all)
			if (value.length === 0 || value.length >= 2) {
				setDebouncedSearch(value);
				setPage(1); // Reset to first page when searching
			}
		},
		300 // 300ms delay
	);

	// Update debounced search when search changes
	React.useEffect(() => {
		debouncedSetSearch(search);
	}, [search, debouncedSetSearch]);

	const { data, isLoading, error, isFetching } = api.employer.getEmployees.useQuery(
		{
			employerId,
			page,
			limit: 10,
			search: debouncedSearch || undefined
		},
		{
			enabled: !!employerId,
			...cacheConfig,
		}
	);

	// Smart prefetching for search results
	React.useEffect(() => {
		PREFETCH_STRATEGIES.prefetchNextPage(
			utils,
			utils.employer.getEmployees,
			data,
			page,
			{ employerId, page, limit: 10, search: debouncedSearch || undefined },
			cacheConfig
		);
	}, [data, page, employerId, debouncedSearch, utils, cacheConfig]);

	const handleSearchChange = React.useCallback((value: string) => {
		setSearch(value);
	}, []);

	return {
		employees: data?.employees || [],
		totalPages: data?.pageCount || 0,
		totalCount: data?.totalCount || 0,
		isLoading,
		isFetching,
		error,
		search,
		setSearch: handleSearchChange,
		page,
		setPage,
	};
};

export const useEmployeeDetails = (employeeId: string) => {
	const utils = api.useContext();
	const cacheConfig = EMPLOYER_CACHE_CONFIG.employeeDetails;

	const { data, isLoading, error } = api.employee.getById.useQuery(
		{ id: employeeId },
		{
			enabled: !!employeeId,
			...cacheConfig,
		}
	);

	// Prefetch related employee data for better UX
	React.useEffect(() => {
		if (employeeId) {
			PREFETCH_STRATEGIES.prefetchEmployeeRelatedData(utils, employeeId);
		}
	}, [employeeId, utils]);

	return {
		employee: data,
		isLoading,
		error,
	};
};

export const useUpdateEmployee = () => {
	const utils = api.useContext();
	const mutation = api.employee.update.useMutation({
		onSuccess: (updatedEmployee) => {
			// Smart invalidation using centralized strategy
			CACHE_INVALIDATION_STRATEGIES.employeeOperation(
				utils,
				updatedEmployee.employee.employerId,
				updatedEmployee.employee.id
			);
		},
		// Simplified optimistic updates to avoid type issues
		onMutate: async (updateData) => {
			const employeeId = updateData.id;

			// Cancel outgoing refetches
			await utils.employee.getById.cancel({ id: employeeId });

			// Snapshot previous value for rollback
			const previousEmployee = utils.employee.getById.getData({ id: employeeId });

			return { previousEmployee, employeeId };
		},
		onError: (err, updateData, context) => {
			// Rollback on error
			if (context?.previousEmployee && context?.employeeId) {
				utils.employee.getById.setData({ id: context.employeeId }, context.previousEmployee);
			}
		},
	});

	return {
		updateEmployee: mutation.mutate,
		isUpdating: mutation.isPending,
		error: mutation.error,
	};
};

export const useEmployeePayslips = (employeeId: string, page: number = 1) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.payslips;

	const { data, isLoading, error } = api.employee.getPayslips.useQuery(
		{
			employeeId,
			page,
			limit: 12
		},
		{
			enabled: !!employeeId,
			...cacheConfig,
		}
	);

	return {
		payslips: data?.payslips || [],
		totalPages: data?.pageCount || 0,
		totalCount: data?.totalCount || 0,
		isLoading,
		error,
	};
};

export const useEmployeeSalaryTransactions = (
	employeeId: string,
	filters?: { year?: number; month?: number },
	page: number = 1
) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.salaryTransactions;

	const { data, isLoading, error } = api.employee.getSalaryTransactions.useQuery(
		{
			employeeId,
			year: filters?.year,
			month: filters?.month,
			page,
			limit: 20
		},
		{
			enabled: !!employeeId,
			...cacheConfig,
		}
	);

	return {
		transactions: data?.transactions || [],
		totalPages: data?.pageCount || 0,
		totalCount: data?.totalCount || 0,
		isLoading,
		error,
	};
};

export const usePayslipDetails = (payslipId: string) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.payslipDetails;

	const { data, isLoading, error } = api.employee.getPayslipDetails.useQuery(
		{ payslipId },
		{
			enabled: !!payslipId,
			...cacheConfig,
		}
	);

	return {
		payslip: data,
		isLoading,
		error,
	};
};

export const useCreateEmployee = (employerId: string) => {
	const utils = api.useContext();
	const mutation = api.employee.create.useMutation({
		onSuccess: (newEmployee) => {
			// Smart invalidation using centralized strategy
			CACHE_INVALIDATION_STRATEGIES.employeeOperation(utils, employerId);
		},
		// Optimistic updates for employee creation
		onMutate: async (newEmployeeData) => {
			// Cancel outgoing refetches for employee lists
			await utils.employer.getEmployees.cancel({ employerId, page: 1, limit: 10 });

			// Snapshot previous value
			const previousEmployees = utils.employer.getEmployees.getData({
				employerId,
				page: 1,
				limit: 10
			});

			// Optimistically update cache
			if (previousEmployees) {
				utils.employer.getEmployees.setData({ employerId, page: 1, limit: 10 }, {
					...previousEmployees,
					employees: [newEmployeeData as any, ...previousEmployees.employees],
					totalCount: previousEmployees.totalCount + 1,
				});
			}

			return { previousEmployees };
		},
		onError: (err, newEmployeeData, context) => {
			// Rollback on error
			if (context?.previousEmployees) {
				utils.employer.getEmployees.setData({
					employerId,
					page: 1,
					limit: 10
				}, context.previousEmployees);
			}
		},
	});

	const createEmployee = async (data: {
		fullName: string;
		nationalId: string;
		email?: string;
		phone?: string;
		address?: string;
		startDate: string;
		baseSalary: string;
		position: string;
		department?: string;
		bankAccount?: string;
		bankBranch?: string;
		bankName?: string;
		notes?: string;
	}): Promise<void> => {
		await mutation.mutateAsync({
			...data,
			employerId,
		});
	};

	return {
		createEmployee,
		isCreating: mutation.isPending,
		error: mutation.error,
	};
};

// Employee document management hooks
export const useEmployeeDocuments = (
	employeeId: string,
	category?: string,
	page: number = 1
) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.employeeDetails;

	const { data, isLoading, error, refetch } = api.employee.getDocuments.useQuery(
		{
			employeeId,
			category,
			page,
			limit: 20
		},
		{
			enabled: !!employeeId,
			...cacheConfig
		}
	);

	// Always call useEffect unconditionally
	React.useEffect(() => {
		// Handle API errors
		if (error) {
			console.error('Error fetching documents:', error);
			toast.error("שגיאה בטעינת מסמכים");
		}
	}, [error]);

	// Safe access to data properties
	const safeData = React.useMemo(() => {
		// Handle case when the server returns an error response
		if (data && 'error' in data) {
			React.useEffect(() => {
				toast.error((data as any).error || "שגיאה בטעינת מסמכים");
			}, [(data as any).error]);

			return {
				documents: [],
				totalCount: 0,
				pageCount: 0,
				currentPage: 1
			};
		}

		return data || {
			documents: [],
			totalCount: 0,
			pageCount: 0,
			currentPage: 1
		};
	}, [data]);

	return {
		documents: safeData.documents || [],
		totalPages: safeData.pageCount || 0,
		totalCount: safeData.totalCount || 0,
		currentPage: safeData.currentPage || 1,
		error,
		isLoading,
		refetch
	};
};

export const useEmployeeForm101Documents = (employeeId: string, year?: number) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.payslips; // Form 101 is static like payslips

	const { data, isLoading, error } = api.employee.getForm101Documents.useQuery(
		{
			employeeId,
			year
		},
		{
			enabled: !!employeeId,
			...cacheConfig
		}
	);

	// Always call useEffect unconditionally
	React.useEffect(() => {
		// Handle API errors
		if (error) {
			console.error('Error fetching Form 101 documents:', error);
			toast.error("שגיאה בטעינת טופסי 101");
		}
	}, [error]);

	// Safe access to data properties
	const safeData = React.useMemo(() => {
		// Handle case when the server returns an error response
		if (data && 'error' in data) {
			React.useEffect(() => {
				toast.error((data as any).error || "שגיאה בטעינת טפסי 101");
			}, [(data as any).error]);

			return {
				documents: [],
				success: false
			};
		}

		return data || { documents: [], success: true };
	}, [data]);

	// Default values for documentsByYear and availableYears
	const documentsByYear = React.useMemo(() => {
		if (!safeData.documents || !safeData.documents.length) return {};

		// Group documents by year
		return safeData.documents.reduce((acc, doc) => {
			const year = (doc.metadata as any)?.year || new Date(doc.uploadedAt).getFullYear();
			if (!acc[year]) {
				acc[year] = [];
			}
			acc[year].push(doc);
			return acc;
		}, {} as Record<number, any[]>);
	}, [safeData.documents]);

	const availableYears = React.useMemo(() => {
		return Object.keys(documentsByYear).map(Number).sort((a, b) => b - a);
	}, [documentsByYear]);

	return {
		documents: safeData.documents || [],
		documentsByYear,
		availableYears,
		isLoading,
		error
	};
};

export const useEmployeeIdCardDocuments = (employeeId: string) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.employeeDetails;

	const { data, isLoading, error } = api.employee.getIdCardDocuments.useQuery(
		{ employeeId },
		{
			enabled: !!employeeId,
			...cacheConfig
		}
	);

	// Always call useEffect unconditionally
	React.useEffect(() => {
		// Handle API errors
		if (error) {
			console.error('Error fetching ID card documents:', error);
			toast.error("שגיאה בטעינת מסמכי תעודת זהות");
		}
	}, [error]);

	// Safe access to data properties
	const safeData = React.useMemo(() => {
		// Handle case when the server returns an error response
		if (data && 'error' in data) {
			React.useEffect(() => {
				toast.error((data as any).error || "שגיאה בטעינת מסמכי תעודת זהות");
			}, [(data as any).error]);

			return {
				documents: [],
				employee: undefined
			};
		}

		return data || { documents: [], employee: undefined };
	}, [data]);

	return {
		employee: safeData.employee,
		documents: safeData.documents || [],
		isLoading,
		error
	};
};

export const useUploadEmployeeDocument = (employeeId: string) => {
	const utils = api.useContext();

	const getUploadUrlMutation = api.employee.getDocumentUploadUrl.useMutation();
	const registerDocumentMutation = api.employee.registerDocument.useMutation({
		onSuccess: () => {
			// Invalidate employee documents cache
			void utils.employee.getDocuments.invalidate({ employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId });
			void utils.employee.getIdCardDocuments.invalidate({ employeeId });
		}
	});

	const uploadDocument = async (
		file: File,
		category: string = "general",
		options?: {
			title?: string;
			description?: string;
			year?: number;
		}
	) => {
		try {
			// Get signed upload URL
			const uploadUrlData = await getUploadUrlMutation.mutateAsync({
				employeeId,
				fileName: file.name,
				fileType: file.type,
				category
			});

			// Upload file to S3
			const uploadResponse = await fetch(uploadUrlData.signedUrl, {
				method: 'PUT',
				body: file,
				headers: {
					'Content-Type': file.type,
				},
			});

			if (!uploadResponse.ok) {
				throw new Error('Failed to upload file to S3');
			}

			// Register document in database
			const documentData = await registerDocumentMutation.mutateAsync({
				employeeId,
				title: options?.title,
				fileName: file.name,
				fileType: file.type,
				fileSize: file.size,
				fileKey: uploadUrlData.fileKey,
				category,
				year: options?.year,
				description: options?.description
			});

			return documentData;
		} catch (error) {
			console.error('Error uploading document:', error);
			throw error;
		}
	};

	return {
		uploadDocument,
		isUploading: getUploadUrlMutation.isPending || registerDocumentMutation.isPending,
		error: getUploadUrlMutation.error || registerDocumentMutation.error
	};
};

export const useDeleteEmployeeDocument = (employeeId: string) => {
	const utils = api.useContext();

	const mutation = api.employee.deleteDocument.useMutation({
		onSuccess: () => {
			// Invalidate employee documents cache
			void utils.employee.getDocuments.invalidate({ employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId });
			void utils.employee.getIdCardDocuments.invalidate({ employeeId });
		},
		// Optimistic updates
		onMutate: async ({ documentId }) => {
			// Cancel outgoing refetches
			await utils.employee.getDocuments.cancel({ employeeId });

			// Snapshot previous value
			const previousDocuments = utils.employee.getDocuments.getData({ employeeId });

			// Optimistically remove document from cache
			if (previousDocuments) {
				utils.employee.getDocuments.setData({ employeeId }, {
					...previousDocuments,
					documents: previousDocuments.documents.filter(doc => doc.id !== documentId),
					totalCount: previousDocuments.totalCount - 1
				});
			}

			return { previousDocuments };
		},
		onError: (err, variables, context) => {
			// Rollback on error
			if (context?.previousDocuments) {
				utils.employee.getDocuments.setData({ employeeId }, context.previousDocuments);
			}
		}
	});

	return {
		deleteDocument: mutation.mutate,
		isDeleting: mutation.isPending,
		error: mutation.error
	};
};

export const useDownloadEmployeeDocument = () => {
	const mutation = api.employee.getDocumentDownloadUrl.useMutation();

	const downloadDocument = async (documentId: string, employeeId: string) => {
		try {
			const { signedUrl, fileName } = await mutation.mutateAsync({
				documentId,
				employeeId
			});

			// Create a temporary link and trigger download
			const link = document.createElement('a');
			link.href = signedUrl;
			link.download = fileName;
			link.target = '_blank';
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
		} catch (error) {
			console.error('Error downloading document:', error);
			throw error;
		}
	};

	return {
		downloadDocument,
		isDownloading: mutation.isPending,
		error: mutation.error
	};
};

export const useUpdateEmployeeDocumentMetadata = (employeeId: string) => {
  const utils = api.useContext();

  const mutation = api.employee.updateDocumentMetadata.useMutation({
    onSuccess: () => {
      // Invalidate employee documents cache
      void utils.employee.getDocuments.invalidate({ employeeId });
      void utils.employee.getForm101Documents.invalidate({ employeeId });
      void utils.employee.getIdCardDocuments.invalidate({ employeeId });
    }
  });

  return {
    updateDocumentMetadata: mutation.mutate,
    isUpdating: mutation.isPending,
    error: mutation.error
  };
};

// Hook לקבלת מסמכים חסרים ואחוז השלמה
export const useEmployeeDocumentStatus = (employeeId: string) => {
  const { employee } = useEmployeeDetails(employeeId);
  const { documents, isLoading } = useEmployeeDocuments(employeeId);

  const employeeStartYear = employee?.startDate ? new Date(employee.startDate).getFullYear() : undefined;

  // Always call hooks in the same order, unconditionally
  const missingDocuments = React.useMemo(() => {
    if (!documents) return [];
    return getMissingRequiredDocuments(documents, employeeStartYear);
  }, [documents, employeeStartYear]);

  const completionPercentage = React.useMemo(() => {
    if (!documents) return 0;
    return calculateDocumentCompletionPercentage(documents, employeeStartYear);
  }, [documents, employeeStartYear]);

  const criticalMissingData = React.useMemo(() => {
    return missingDocuments.filter((doc: any) => doc.color === 'red');
  }, [missingDocuments]);

  const optionalMissingData = React.useMemo(() => {
    return missingDocuments.filter((doc: any) => doc.color === 'blue');
  }, [missingDocuments]);

  return {
    missingDocuments,
    completionPercentage,
    criticalMissing: criticalMissingData,
    optionalMissing: optionalMissingData,
    isComplete: criticalMissingData.length === 0, // השלמה = אין מסמכים קריטיים חסרים
    hasCriticalMissing: criticalMissingData.length > 0,
    isLoading
  };
};

export const useForm101Data = (employeeId: string, year: number) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.payslips; // Form 101 is static like payslips

	const { data, isLoading, error, refetch } = api.employee.getForm101.useQuery(
		{
			employeeId,
			year
		},
		{
			enabled: !!employeeId,
			...cacheConfig,
		}
	);

	return {
		form101: data,
		isLoading,
		error,
		refetch
	};
};

export const useAllForm101Data = (year: number) => {
	const cacheConfig = EMPLOYER_CACHE_CONFIG.payslips;

	const { data, isLoading, error, refetch } = api.employee.getAllForm101.useQuery(
		{ year },
		{ ...cacheConfig }
	);

	return {
		forms: data,
		isLoading,
		error,
		refetch
	};
};

export const useCreateForm101 = () => {
	const utils = api.useContext();

	const mutation = api.employee.createForm101.useMutation({
		onSuccess: (data) => {
			// Invalidate related queries
			void utils.employee.getForm101.invalidate({ employeeId: data.employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId: data.employeeId });
			void utils.employee.getAllForm101.invalidate();
		}
	});

	return {
		createForm101: mutation.mutateAsync,
		isCreating: mutation.isPending,
		error: mutation.error
	};
};

export const useSyncForm101WithDocuments = () => {
	const utils = api.useContext();

	const mutation = api.employee.syncForm101WithDocuments.useMutation({
		onSuccess: (data) => {
			// Invalidate related queries to refresh data
			void utils.employee.getForm101.invalidate({ employeeId: data.form101Record?.employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId: data.form101Record?.employeeId });
			void utils.employee.getAllForm101.invalidate();
		}
	});

	return {
		syncForm101: mutation.mutateAsync,
		isSyncing: mutation.isPending,
		error: mutation.error,
		syncResult: mutation.data
	};
};

export const useUpdatePayslip = () => {
	const utils = api.useUtils();

	const mutation = api.employee.updatePayslip.useMutation({
		onSuccess: (data: any) => {
			// Invalidate related queries
			void utils.employee.getPayslips.invalidate({ employeeId: data.employeeId });
			void utils.employee.getPayslipDetails.invalidate({ payslipId: data.id });
		}
	});

	return {
		updatePayslip: mutation.mutateAsync,
		isUpdating: mutation.isPending,
		error: mutation.error
	};
};

export const useApprovePayslip = () => {
	const utils = api.useUtils();

	const mutation = api.employee.approvePayslip.useMutation({
		onSuccess: (data: any) => {
			// Invalidate related queries
			void utils.employee.getPayslips.invalidate({ employeeId: data.employeeId });
			void utils.employee.getPayslipDetails.invalidate({ payslipId: data.id });
		}
	});

	return {
		approvePayslip: mutation.mutateAsync,
		isApproving: mutation.isPending,
		error: mutation.error
	};
};

export const useGeneratePayslipPDF = () => {
	const mutation = api.employee.generatePayslipPDF.useMutation();

	return {
		generatePDF: mutation.mutateAsync,
		isGenerating: mutation.isPending,
		error: mutation.error
	};
};

export const useSendPayslipToEmployee = () => {
	const utils = api.useUtils();

	const mutation = api.employee.sendPayslipToEmployee.useMutation({
		onSuccess: (data: any) => {
			// Invalidate related queries
			void utils.employee.getPayslips.invalidate({ employeeId: data.employeeId });
			void utils.employee.getPayslipDetails.invalidate({ payslipId: data.id });
		}
	});

	return {
		sendToEmployee: mutation.mutateAsync,
		isSending: mutation.isPending,
		error: mutation.error
	};
};

export const useUpdateForm101 = () => {
	const utils = api.useContext();

	const mutation = api.employee.updateForm101.useMutation({
		onSuccess: (data) => {
			// Invalidate related queries
			void utils.employee.getForm101.invalidate({ employeeId: data.employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId: data.employeeId });
			void utils.employee.getAllForm101.invalidate();
		}
	});

	return {
		updateForm101: mutation.mutateAsync,
		isUpdating: mutation.isPending,
		error: mutation.error
	};
};

export const useSendForm101ForSignature = () => {
	const utils = api.useContext();

	const mutation = api.employee.sendForm101ForSignature.useMutation({
		onSuccess: (data) => {
			// Invalidate related queries
			void utils.employee.getForm101.invalidate({ employeeId: data.employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId: data.employeeId });
		}
	});

	return {
		sendForSignature: mutation.mutateAsync,
		isSending: mutation.isPending,
		error: mutation.error
	};
};

export const useCheckForm101SignatureStatus = (form101Id: string) => {
	const { data, isLoading, error, refetch } = api.employee.checkForm101SignatureStatus.useQuery(
		{ form101Id },
		{
			// Only run query if form101Id is provided
			enabled: !!form101Id,
			// Check status every minute
			refetchInterval: 60 * 1000,
			// Don't refetch on window focus to avoid unnecessary requests
			refetchOnWindowFocus: false
		}
	);

	return {
		signatureStatus: data,
		isLoading,
		error,
		refetch
	};
};

export const useCancelForm101SignatureRequest = () => {
	const utils = api.useContext();

	const mutation = api.employee.cancelForm101SignatureRequest.useMutation({
		onSuccess: (data) => {
			// Invalidate related queries
			void utils.employee.getForm101.invalidate({ employeeId: data.employeeId });
			void utils.employee.getForm101Documents.invalidate({ employeeId: data.employeeId });
		}
	});

	return {
		cancelSignatureRequest: mutation.mutateAsync,
		isCancelling: mutation.isPending,
		error: mutation.error
	};
};

export const useGenerateForm101Preview = () => {
	const mutation = api.employee.generateForm101Preview.useMutation();

	return {
		generatePreview: mutation.mutateAsync,
		isGenerating: mutation.isPending,
		error: mutation.error
	};
};
