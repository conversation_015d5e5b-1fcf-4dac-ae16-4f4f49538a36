"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { useDeleteAssociation, formatDateString, useAssociationHistory } from "../hooks";
import type { Association } from "../hooks";
import { AssociationFormModal } from "./association-form-modal";
import { AssociationHistoryModal } from "../components/association-history-modal";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";

// Helper to translate association types to Hebrew
const associationTypeToHebrew = {
  DEPARTMENT: "מחלקה",
  ROLE: "תפקיד",
  SALARY_TEMPLATE: "תבנית שכר",
  SALARY_AGREEMENT: "הסכם שכר",
};

type SortField = 'employeeName' | 'employeeIdentifier' | 'associationType' | 'associatedEntityName' | 'startDate' | 'endDate';
type SortDirection = 'asc' | 'desc';

export interface AssociationsTableProps {
  associations: Association[];
  isLoading: boolean;
  page: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  employerId: string;
  onRefresh: () => void;
}

export function AssociationsTable({
  associations,
  isLoading,
  page,
  totalPages,
  onPageChange,
  employerId,
  onRefresh,
}: AssociationsTableProps) {
  const [selectedAssociation, setSelectedAssociation] = useState<Association | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const { mutate: deleteAssociation, isPending: isDeleting } = useDeleteAssociation(employerId);
  
  // Fetch history data when needed
  const { data: historyData, isLoading: isLoadingHistory } = useAssociationHistory(
    selectedAssociation?.id || "",
    employerId
  );

  const handleEdit = (association: Association) => {
    setSelectedAssociation(association);
    setIsEditModalOpen(true);
  };

  const handleDelete = (association: Association) => {
    setSelectedAssociation(association);
    setIsDeleteDialogOpen(true);
  };
  
  const handleViewHistory = (association: Association) => {
    setSelectedAssociation(association);
    setIsHistoryModalOpen(true);
  };

  const confirmDelete = () => {
    if (!selectedAssociation) return;
    
    deleteAssociation(selectedAssociation.id, {
      onSuccess: () => {
        toast.success("השיוך נמחק בהצלחה");
        setIsDeleteDialogOpen(false);
        onRefresh();
      },
      onError: (error) => {
        toast.error("אירעה שגיאה במחיקת השיוך");
        console.error(error);
      },
    });
  };

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedAssociations = [...associations].sort((a, b) => {
    if (!sortField) return 0;
    
    let aValue: any = a[sortField];
    let bValue: any = b[sortField];
    
    // המרת תאריכים למספרים לצורך השוואה
    if (sortField === 'startDate' || sortField === 'endDate') {
      aValue = aValue ? new Date(aValue).getTime() : 0;
      bValue = bValue ? new Date(bValue).getTime() : 0;
    }
    
    // טיפול בערכים null
    if (aValue === null || aValue === undefined) aValue = '';
    if (bValue === null || bValue === undefined) bValue = '';
    
    // השוואה
    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortDirection === 'asc' ? 
      <ArrowUp className="h-4 w-4" /> : 
      <ArrowDown className="h-4 w-4" />;
  };

  if (isLoading) {
    return <AssociationsTableSkeleton />;
  }

  if (associations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <p className="text-gray-500 mb-4">לא נמצאו שיוכים</p>
        <p className="text-sm text-gray-400">נסה לשנות את הסינון או להוסיף שיוכים חדשים</p>
      </div>
    );
  }

  return (
    <>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort('employeeName')}
              >
                <div className="flex items-center gap-1">
                  שם עובד
                  <SortIcon field="employeeName" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort('employeeIdentifier')}
              >
                <div className="flex items-center gap-1">
                  מספר זיהוי
                  <SortIcon field="employeeIdentifier" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort('associationType')}
              >
                <div className="flex items-center gap-1">
                  סוג שיוך
                  <SortIcon field="associationType" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort('associatedEntityName')}
              >
                <div className="flex items-center gap-1">
                  ישות משויכת
                  <SortIcon field="associatedEntityName" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort('startDate')}
              >
                <div className="flex items-center gap-1">
                  תאריך התחלה
                  <SortIcon field="startDate" />
                </div>
              </TableHead>
              <TableHead 
                className="cursor-pointer hover:bg-gray-50"
                onClick={() => handleSort('endDate')}
              >
                <div className="flex items-center gap-1">
                  תאריך סיום
                  <SortIcon field="endDate" />
                </div>
              </TableHead>
              <TableHead>פעולות</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedAssociations.map((association) => (
              <TableRow key={association.id}>
                <TableCell>{association.employeeName}</TableCell>
                <TableCell>{association.employeeIdentifier}</TableCell>
                <TableCell>
                  {associationTypeToHebrew[association.associationType] || association.associationType}
                </TableCell>
                <TableCell>{association.associatedEntityName}</TableCell>
                <TableCell>{formatDateString(association.startDate)}</TableCell>
                <TableCell>
                  {association.endDate ? formatDateString(association.endDate) : "לא מוגבל"}
                </TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewHistory(association)}
                    >
                      היסטוריה
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(association)}
                    >
                      עריכה
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleDelete(association)}
                    >
                      מחיקה
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center p-4">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
            >
              הקודם
            </Button>
            <div className="flex items-center mx-2">
              עמוד {page} מתוך {totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPages}
            >
              הבא
            </Button>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {selectedAssociation && (
        <AssociationFormModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          employerId={employerId}
          onSuccess={() => {
            setIsEditModalOpen(false);
            onRefresh();
          }}
          association={selectedAssociation}
        />
      )}

      {/* History Modal */}
      {selectedAssociation && (
        <AssociationHistoryModal
          isOpen={isHistoryModalOpen}
          onClose={() => setIsHistoryModalOpen(false)}
          association={selectedAssociation}
          history={historyData?.history || []}
          isLoading={isLoadingHistory}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>מחיקת שיוך</DialogTitle>
            <DialogDescription>
              האם אתה בטוח שברצונך למחוק את השיוך של{" "}
              <strong>{selectedAssociation?.employeeName}</strong> ל
              {associationTypeToHebrew[selectedAssociation?.associationType || "DEPARTMENT"]}?
              פעולה זו אינה ניתנת לביטול.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              ביטול
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "מוחק..." : "מחק"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}

function AssociationsTableSkeleton() {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>שם עובד</TableHead>
            <TableHead>מספר זיהוי</TableHead>
            <TableHead>סוג שיוך</TableHead>
            <TableHead>ישות משויכת</TableHead>
            <TableHead>תאריך התחלה</TableHead>
            <TableHead>תאריך סיום</TableHead>
            <TableHead>פעולות</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 5 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-4 w-32" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-20" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-28" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-4 w-24" />
              </TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
} 