export type { Employer } from "@/schema/employer";
export type * from "@/schema/payroll";
export type * from "@/utils/alert-management";
export type * from "@/utils/data-validation";
export type * from "@/utils/payroll-calculations";

export interface User {
	id: string;
	name: string;
	email: string;
	role: string;
	employerId: string;
	employerName: string;
	status: string;
}

export interface Report {
	id: string;
	title: string;
	description: string;
	details: string;
}

export interface AuditLog {
	id: string;
	timestamp: string;
	userEmail: string;
	employerName: string;
	actionType: string;
	entityType: string;
	details: Record<string, unknown>;
}

export interface AuditLogDetails extends AuditLog {
	userName: string;
	employerId: string;
	ipAddress: string;
	userAgent: string;
}

export interface CreateUserInput {
	name: string;
	email: string;
	role: string;
	employerId?: string;
	status?: "active" | "inactive";
}
