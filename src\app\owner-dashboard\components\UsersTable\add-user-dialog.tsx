"use client";

import { 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { UserForm } from "./user-form";  
import { type UserFormValues } from "./types";

type AddUserDialogProps = {
  isCreating?: boolean;
  createUser?: (data: UserFormValues) => void;
};

export function AddUserDialog({ isCreating, createUser }: AddUserDialogProps) {
  return (
    <DialogContent className="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle className="text-right">הוספת משתמש חדש</DialogTitle>
        <DialogDescription className="text-right">
          הזן את פרטי המשתמש החדש כאן. לחץ על שמור כדי להוסיף את המשתמש.
        </DialogDescription>
      </DialogHeader>
      <UserForm
        onSubmit={createUser}
        isSubmitting={isCreating}
      />
    </DialogContent>
  );
} 