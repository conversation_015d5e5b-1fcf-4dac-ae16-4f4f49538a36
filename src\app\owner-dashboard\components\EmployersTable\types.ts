import * as z from "zod";
import { type Employer, createEmployerSchema } from "@/schema/employer";
import { isValidIsraeliBusinessId } from "@/utils/validation";

export type EmployersTableProps = {
  employers?: Employer[];
  isLoading: boolean;
  createEmployer?: (data: z.infer<typeof createEmployerSchema>) => void;
  isCreating?: boolean;
};

export const employerFormSchema = z.object({
  name: z.string().min(1, { message: "שם המעסיק הוא שדה חובה" }),
  identifier: z.string()
    .min(1, { message: "מזהה הוא שדה חובה" })
    .length(9, { message: "המזהה חייב להכיל בדיוק 9 ספרות" })
    .regex(/^\d+$/, { message: "המזהה חייב לכלול רק ספרות" })
    .refine(isValidIsraeliBusinessId, { message: "מזהה עסקי לא תקין" }),
  status: z.enum(["active", "inactive"]),
});

export type EmployerFormValues = z.infer<typeof employerFormSchema>; 