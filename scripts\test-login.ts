import { PrismaClient } from "@prisma/client";
import bcrypt from "bcryptjs";

async function testLogin() {
  const prisma = new PrismaClient();
  
  // Email and password to test
  const email = "<EMAIL>";
  const password = "123456789";
  
  try {
    console.log(`Testing login for email: ${email}`);
    
    // Find the user
    const user = await prisma.user.findFirst({
      where: { 
        email: email,
        isActive: true
      }
    });

    if (!user) {
      console.error(`User with email ${email} not found!`);
      return;
    }
    
    console.log("User found:", {
      id: user.id,
      email: user.email,
      role: user.role,
      isActive: user.isActive,
      // Only show the first 20 characters of the password hash for security
      passwordHash: user.password ? user.password.substring(0, 20) + "..." : "no password set"
    });
    
    // Verify password
    if (!user.password) {
      console.error("User has no password set!");
      return;
    }
    
    const isPasswordValid = await bcrypt.compare(password, user.password);
    console.log(`Password verification result: ${isPasswordValid ? "SUCCESS" : "FAILED"}`);
    
    if (!isPasswordValid) {
      console.log("Attempting to update password...");
      const hashedPassword = await bcrypt.hash(password, 10);
      await prisma.user.update({
        where: { id: user.id },
        data: { password: hashedPassword }
      });
      console.log("Password updated. New hash:", hashedPassword.substring(0, 20) + "...");
    }
    
  } catch (error) {
    console.error("Error testing login:", error);
  } finally {
    await prisma.$disconnect();
  }
}

testLogin()
  .then(() => console.log("Test completed"))
  .catch((e) => {
    console.error("Test failed:", e);
    process.exit(1);
  }); 