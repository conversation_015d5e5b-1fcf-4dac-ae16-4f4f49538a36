"use client";

import { CalendarDaysIcon, InfoIcon, NewspaperIcon, FileTextIcon, BellIcon } from "lucide-react";

import { Button } from "@/components/ui/rtl-components";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/rtl-components";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/rtl-components";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/rtl-components";

export default function UpdatesPage() {
  // נתוני עדכונים לדוגמה
  const updates = [
    {
      id: 1,
      title: "עדכון מערכת 3.2.1",
      date: "19/04/2023",
      type: "system",
      badge: "חדש",
      content: "גרסה חדשה של המערכת שוחררה עם שיפורים ביציבות ותיקוני באגים. העדכון כולל ממשק משתמש משופר, ביצועים מהירים יותר, ותמיכה בדיווחי 102 מקוונים. נוספה גם אפשרות לייצא נתונים בפורמט Excel משופר."
    },
    {
      id: 2,
      title: "שינויים בחוק עובדים זרים",
      date: "15/04/2023",
      type: "legal",
      badge: "חשוב",
      content: "החל מתאריך 1.5.2023 יחולו שינויים בשיעורי הניכוי לעובדים זרים. הנתונים עודכנו אוטומטית במערכת. למידע נוסף ראה חוזר רשות המיסים 4/2023."
    },
    {
      id: 3,
      title: "הוספת דוחות חדשים",
      date: "10/04/2023",
      type: "feature",
      badge: "תכונה חדשה",
      content: "הוספנו מגוון דוחות חדשים למערכת: דוח שינויי שכר, דוח השוואת תלושים בין חודשים, ודוח ניתוח עלויות העסקה. גישה לדוחות אלו זמינה דרך מסך הדוחות תחת הקטגוריה 'דוחות מתקדמים'."
    },
    {
      id: 4,
      title: "עדכון שיעורי מס הכנסה",
      date: "01/04/2023",
      type: "legal",
      content: "בהתאם להנחיות רשות המסים, עודכנו במערכת שיעורי וסכומי מס הכנסה החדשים לשנת 2023. העדכון יחול אוטומטית על תלושי השכר החל מחודש אפריל."
    },
    {
      id: 5,
      title: "שיפורים בממשק משתמש",
      date: "28/03/2023",
      type: "system",
      content: "ביצענו שיפורים בממשק המשתמש של המערכת. כעת ניתן להתאים אישית את לוח המחוונים, לשנות את סדר העמודות בטבלאות, ולשמור תצוגות מותאמות אישית."
    },
    {
      id: 6,
      title: "תזכורת - דיווח רבעוני",
      date: "25/03/2023",
      type: "reminder",
      badge: "תזכורת",
      content: "נדרש להגיש דוחות רבעוניים למס הכנסה עד לתאריך 15/04/2023. המערכת תשלח תזכורת נוספת לקראת המועד."
    },
    {
      id: 7,
      title: "תיקון טופס 102",
      date: "20/03/2023",
      type: "legal",
      content: "בהתאם להנחיות רשות המסים, עודכן טופס 102 במערכת. העדכון כולל שדות חדשים עבור הפרשות פנסיוניות מיוחדות."
    },
    {
      id: 8,
      title: "חישוב הפרשה לקרן לעידוד הבנייה",
      date: "15/03/2023",
      type: "feature",
      content: "נוספה תמיכה בחישוב אוטומטי של הפרשה לקרן לעידוד הבנייה. המנגנון החדש זמין בהגדרות הפרשות מעסיק."
    }
  ];

  // קבוצות את העדכונים לפי הקטגוריה
  const systemUpdates = updates.filter(update => update.type === "system");
  const legalUpdates = updates.filter(update => update.type === "legal");
  const featureUpdates = updates.filter(update => update.type === "feature");
  const reminders = updates.filter(update => update.type === "reminder");

  const getBadgeVariant = (type: string) => {
    switch (type) {
      case "system": return "secondary";
      case "legal": return "default";
      case "feature": return "outline";
      case "reminder": return "destructive";
      default: return "default";
    }
  };

  const getIcon = (type: string) => {
    switch (type) {
      case "system": return <InfoIcon className="h-4 w-4" />;
      case "legal": return <FileTextIcon className="h-4 w-4" />;
      case "feature": return <NewspaperIcon className="h-4 w-4" />;
      case "reminder": return <BellIcon className="h-4 w-4" />;
      default: return <InfoIcon className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">עדכונים וחדשות</h1>
        <div className="flex items-center gap-2">
          <Select defaultValue="all">
            <SelectTrigger className="w-36">
              <SelectValue placeholder="סוג עדכון" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">הכל</SelectItem>
              <SelectItem value="system">מערכת</SelectItem>
              <SelectItem value="legal">רגולציה</SelectItem>
              <SelectItem value="feature">תכונות</SelectItem>
              <SelectItem value="reminder">תזכורות</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="all">כל העדכונים</TabsTrigger>
          <TabsTrigger value="system">עדכוני מערכת</TabsTrigger>
          <TabsTrigger value="legal">עדכוני רגולציה</TabsTrigger>
          <TabsTrigger value="feature">תכונות חדשות</TabsTrigger>
          <TabsTrigger value="reminder">תזכורות</TabsTrigger>
        </TabsList>
        
        {/* טאב כל העדכונים */}
        <TabsContent value="all">
          <div className="space-y-4">
            {updates.map((update) => (
              <Card key={update.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      {getIcon(update.type)}
                      <CardTitle className="text-lg">{update.title}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      {update.badge && (
                        <Badge variant={getBadgeVariant(update.type)}>{update.badge}</Badge>
                      )}
                      <div className="flex items-center text-xs text-muted-foreground">
                        <CalendarDaysIcon className="ml-1 h-3 w-3" />
                        {update.date}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{update.content}</p>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="link" className="px-0 h-auto">קרא עוד</Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        {/* טאב עדכוני מערכת */}
        <TabsContent value="system">
          <div className="space-y-4">
            {systemUpdates.map((update) => (
              <Card key={update.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <InfoIcon className="h-4 w-4" />
                      <CardTitle className="text-lg">{update.title}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      {update.badge && (
                        <Badge variant="secondary">{update.badge}</Badge>
                      )}
                      <div className="flex items-center text-xs text-muted-foreground">
                        <CalendarDaysIcon className="ml-1 h-3 w-3" />
                        {update.date}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{update.content}</p>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="link" className="px-0 h-auto">קרא עוד</Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        {/* טאב עדכוני רגולציה */}
        <TabsContent value="legal">
          <div className="space-y-4">
            {legalUpdates.map((update) => (
              <Card key={update.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <FileTextIcon className="h-4 w-4" />
                      <CardTitle className="text-lg">{update.title}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      {update.badge && (
                        <Badge>{update.badge}</Badge>
                      )}
                      <div className="flex items-center text-xs text-muted-foreground">
                        <CalendarDaysIcon className="ml-1 h-3 w-3" />
                        {update.date}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{update.content}</p>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="link" className="px-0 h-auto">קרא עוד</Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        {/* טאב תכונות חדשות */}
        <TabsContent value="feature">
          <div className="space-y-4">
            {featureUpdates.map((update) => (
              <Card key={update.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <NewspaperIcon className="h-4 w-4" />
                      <CardTitle className="text-lg">{update.title}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      {update.badge && (
                        <Badge variant="outline">{update.badge}</Badge>
                      )}
                      <div className="flex items-center text-xs text-muted-foreground">
                        <CalendarDaysIcon className="ml-1 h-3 w-3" />
                        {update.date}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{update.content}</p>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="link" className="px-0 h-auto">קרא עוד</Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        {/* טאב תזכורות */}
        <TabsContent value="reminder">
          <div className="space-y-4">
            {reminders.map((update) => (
              <Card key={update.id}>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <BellIcon className="h-4 w-4" />
                      <CardTitle className="text-lg">{update.title}</CardTitle>
                    </div>
                    <div className="flex items-center gap-2">
                      {update.badge && (
                        <Badge variant="destructive">{update.badge}</Badge>
                      )}
                      <div className="flex items-center text-xs text-muted-foreground">
                        <CalendarDaysIcon className="ml-1 h-3 w-3" />
                        {update.date}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{update.content}</p>
                </CardContent>
                <CardFooter className="pt-2">
                  <Button variant="link" className="px-0 h-auto">קרא עוד</Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 