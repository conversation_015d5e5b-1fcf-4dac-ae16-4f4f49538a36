"use client";

import { PlusCircle } from "lucide-react";
import { Button } from "@/components/ui/rtl-components";
import { 
  Dialog, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { AddUserDialog } from "./add-user-dialog";
import { type UserFormValues } from "./types";

type UsersHeaderProps = {
  isCreating?: boolean;
  createUser?: (data: UserFormValues) => void;
};

export function UsersHeader({ isCreating, createUser }: UsersHeaderProps) {
  return (
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">ניהול משתמשים</h2>
      <Dialog>
        <DialogTrigger asChild>
          <Button disabled={isCreating}>
            <PlusCircle className="ml-2 h-4 w-4" />
            הוסף משתמש
          </Button>
        </DialogTrigger>
        <AddUserDialog 
          isCreating={isCreating} 
          createUser={createUser} 
        />
      </Dialog>
    </div>
  );
} 