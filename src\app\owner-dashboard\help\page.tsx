"use client";

import { useState } from "react";
import { FileQuestion, Search, BookOpen, PlayCircle, FileText, ChevronRight, ArrowRight } from "lucide-react";

import { Button } from "@/components/ui/rtl-components";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/rtl-components";
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState<string>("");
  
  // קטגוריות עזרה לדוגמה
  const helpCategories = [
    {
      id: "general",
      title: "כללי",
      icon: <BookOpen className="h-5 w-5" />,
      topics: [
        { id: "intro", title: "היכרות עם המערכת", description: "מידע בסיסי על השימוש במערכת" },
        { id: "navigation", title: "ניווט במערכת", description: "כיצד לנווט בין המסכים השונים" },
        { id: "settings", title: "הגדרות ראשוניות", description: "התאמת המערכת לצרכיך" },
      ]
    },
    {
      id: "employees",
      title: "ניהול עובדים",
      icon: <FileText className="h-5 w-5" />,
      topics: [
        { id: "add-employee", title: "הוספת עובד חדש", description: "כיצד להוסיף עובד חדש למערכת" },
        { id: "employee-details", title: "עדכון פרטי עובד", description: "ניהול מידע אישי ופרטי העסקה" },
        { id: "terminate", title: "סיום העסקה", description: "תהליך סיום העסקת עובד" },
      ]
    },
    {
      id: "payroll",
      title: "ניהול שכר",
      icon: <FileText className="h-5 w-5" />,
      topics: [
        { id: "create-payslip", title: "הפקת תלושי שכר", description: "הנחיות להפקת תלושי שכר חודשיים" },
        { id: "components", title: "רכיבי שכר", description: "הגדרה וניהול של רכיבי שכר" },
        { id: "calculations", title: "חישובי שכר", description: "הסברים על אופן חישוב השכר" },
      ]
    },
    {
      id: "reports",
      title: "דוחות",
      icon: <FileText className="h-5 w-5" />,
      topics: [
        { id: "monthly-reports", title: "דוחות חודשיים", description: "דוחות שיש להפיק מדי חודש" },
        { id: "yearly-reports", title: "דוחות שנתיים", description: "דוחות שיש להפיק מדי שנה" },
        { id: "custom-reports", title: "דוחות מותאמים אישית", description: "יצירת דוחות לפי דרישה" },
      ]
    },
  ];
  
  // תוכן שאלות נפוצות לדוגמה
  const faqs = [
    {
      question: "כיצד להוסיף מעסיק חדש?",
      answer: "כדי להוסיף מעסיק חדש, לחץ על כפתור 'מעסיקים' בתפריט הראשי, ואז לחץ על כפתור 'הוספת מעסיק'. מלא את הטופס עם פרטי המעסיק החדש ולחץ 'שמור'."
    },
    {
      question: "איך לעדכן את פרטי הבנק של עובד?",
      answer: "עבור לרשימת העובדים, בחר את העובד הרלוונטי ולחץ על 'ערוך'. עבור ללשונית 'פרטי בנק' ועדכן את המידע. לחץ 'שמור' כדי לשמור את השינויים."
    },
    {
      question: "מתי יש להגיש דוח 102?",
      answer: "דוח 102 (דיווח למוסד לביטוח לאומי ומס הכנסה) יש להגיש מדי חודש עד ה-15 בחודש העוקב עבור חודש המשכורת הקודם."
    },
    {
      question: "איך להפיק טופס 106 לעובדים?",
      answer: "בסוף השנה, עבור למסך 'דוחות שנתיים', בחר 'טופס 106', בחר את השנה הרלוונטית ואת העובדים עבורם יש להפיק את הטופס, ולחץ 'הפק'."
    },
    {
      question: "כיצד לבצע גיבוי של נתוני המערכת?",
      answer: "עבור להגדרות המערכת, לחץ על לשונית 'גיבוי ושחזור', ואז לחץ על כפתור 'יצירת גיבוי'. הגיבוי יישמר באופן אוטומטי במיקום שהוגדר מראש."
    },
    {
      question: "איך להגדיר הרשאות למשתמשים שונים?",
      answer: "במסך 'ניהול משתמשים', בחר את המשתמש ולחץ על 'ערוך הרשאות'. הגדר את רמת ההרשאה הרצויה (מנהל, רואה חשבון, משאבי אנוש, וכו') ואת התכונות הספציפיות אליהן יהיה למשתמש גישה."
    },
  ];
  
  // מדריכי וידאו לדוגמה
  const videoTutorials = [
    {
      id: 1,
      title: "מדריך למשתמש החדש",
      thumbnail: "/placeholders/video-thumbnail-1.jpg",
      duration: "5:24",
      views: 1245,
      date: "10/01/2023"
    },
    {
      id: 2,
      title: "הפקת תלושי שכר",
      thumbnail: "/placeholders/video-thumbnail-2.jpg",
      duration: "8:12",
      views: 983,
      date: "15/02/2023"
    },
    {
      id: 3,
      title: "דיווחים למוסד לביטוח לאומי",
      thumbnail: "/placeholders/video-thumbnail-3.jpg",
      duration: "6:45",
      views: 756,
      date: "22/03/2023"
    },
    {
      id: 4,
      title: "הגדרות מתקדמות",
      thumbnail: "/placeholders/video-thumbnail-4.jpg",
      duration: "10:30",
      views: 512,
      date: "05/04/2023"
    },
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-1">
        <h1 className="text-3xl font-bold tracking-tight">מרכז העזרה</h1>
        <p className="text-muted-foreground">תמיכה, מדריכים ותשובות לשאלות נפוצות</p>
      </div>

      <div className="relative">
        <Search className="absolute right-3 top-3 h-5 w-5 text-muted-foreground" />
        <Input
          className="pl-4 pr-10"
          placeholder="חיפוש בעזרה..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      <Tabs defaultValue="topics" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="topics">נושאי עזרה</TabsTrigger>
          <TabsTrigger value="faq">שאלות נפוצות</TabsTrigger>
          <TabsTrigger value="videos">סרטוני הדרכה</TabsTrigger>
          <TabsTrigger value="contact">צור קשר</TabsTrigger>
        </TabsList>
        
        {/* נושאי עזרה */}
        <TabsContent value="topics">
          <div className="grid gap-6 md:grid-cols-2">
            {helpCategories.map((category) => (
              <Card key={category.id}>
                <CardHeader>
                  <div className="flex items-center gap-2">
                    {category.icon}
                    <CardTitle>{category.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="space-y-2">
                  {category.topics.map((topic) => (
                    <div key={topic.id} className="border-b pb-2 last:border-0 last:pb-0">
                      <Button variant="link" className="p-0 h-auto justify-start items-start">
                        <span className="font-medium">{topic.title}</span>
                      </Button>
                      <p className="text-sm text-muted-foreground">{topic.description}</p>
                    </div>
                  ))}
                </CardContent>
                <CardFooter>
                  <Button variant="ghost" className="w-full">
                    צפה בכל הנושאים
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        {/* שאלות נפוצות */}
        <TabsContent value="faq">
          <Card>
            <CardHeader>
              <CardTitle>שאלות נפוצות</CardTitle>
              <CardDescription>תשובות לשאלות הנפוצות ביותר</CardDescription>
            </CardHeader>
            <CardContent>
              <Accordion type="single" collapsible className="w-full">
                {faqs.map((faq, index) => (
                  <AccordionItem key={index} value={`faq-${index}`}>
                    <AccordionTrigger className="text-right">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-right">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full">
                צפה בכל השאלות הנפוצות
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* סרטוני הדרכה */}
        <TabsContent value="videos">
          <Card>
            <CardHeader>
              <CardTitle>סרטוני הדרכה</CardTitle>
              <CardDescription>למד כיצד להשתמש במערכת באמצעות מדריכי וידאו</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                {videoTutorials.map((video) => (
                  <div key={video.id} className="flex flex-col">
                    <div className="relative aspect-video w-full overflow-hidden rounded-md">
                      <div className="absolute inset-0 bg-gray-800/20 flex items-center justify-center">
                        <PlayCircle className="h-12 w-12 text-white" />
                      </div>
                      <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-1 rounded">
                        {video.duration}
                      </div>
                    </div>
                    <h3 className="mt-2 font-medium">{video.title}</h3>
                    <div className="text-xs text-muted-foreground mt-1">
                      {video.views} צפיות • {video.date}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="ghost" className="w-full">
                צפה בכל הסרטונים
                <ChevronRight className="ml-1 h-4 w-4" />
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* צור קשר */}
        <TabsContent value="contact">
          <Card>
            <CardHeader>
              <CardTitle>צור קשר עם התמיכה</CardTitle>
              <CardDescription>
                צוות התמיכה שלנו זמין לעזור לך בכל שאלה או בעיה
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="flex flex-col items-center justify-center p-4 border rounded-lg">
                  <FileQuestion className="h-10 w-10 mb-4 text-muted-foreground" />
                  <h3 className="font-medium mb-2">תמיכה טכנית</h3>
                  <p className="text-sm text-center text-muted-foreground mb-4">
                    לשאלות טכניות ופתרון בעיות במערכת
                  </p>
                  <div className="text-sm text-center">
                    <div>טלפון: 03-1234567</div>
                    <div>דוא"ל: <EMAIL></div>
                  </div>
                </div>
                
                <div className="flex flex-col items-center justify-center p-4 border rounded-lg">
                  <BookOpen className="h-10 w-10 mb-4 text-muted-foreground" />
                  <h3 className="font-medium mb-2">הדרכה וייעוץ</h3>
                  <p className="text-sm text-center text-muted-foreground mb-4">
                    לתיאום הדרכה או קבלת ייעוץ מקצועי
                  </p>
                  <div className="text-sm text-center">
                    <div>טלפון: 03-7654321</div>
                    <div>דוא"ל: <EMAIL></div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="font-medium mb-4">שעות פעילות</h3>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div>ימים א׳-ה׳:</div>
                  <div>08:00 - 17:00</div>
                  <div>יום ו׳:</div>
                  <div>08:00 - 13:00 (מענה לפניות דחופות בלבד)</div>
                  <div>שבת וחגים:</div>
                  <div>סגור</div>
                </div>
              </div>
              
              <div className="flex justify-center">
                <Button className="w-full md:w-auto">
                  <ArrowRight className="ml-2 h-4 w-4" />
                  עבור לעמוד התמיכה המלא
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 