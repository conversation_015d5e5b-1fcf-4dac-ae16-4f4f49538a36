---description: Guide for AI agents working with the salary-t3 applicationglobs: src/**/*.ts, src/**/*.tsxalwaysApply: true---# Tech Stack & Conventions Guide# AI Agent Guidelines for **salary-t3**> **Audience:** Developers and AI engineers writing autonomous or semi-autonomous agents that integrate with the salary-t3 payroll management system for foreign workers.---## 1  Purpose & ScopeThis document standardises how agents **read, write, validate, and reason** over salary-t3 data and code.  It supplements the existing Tech Stack & Conventions Guide and MUST be followed for any agent that executes in production, CI, or local developer tools.**Goals*** Maintain **data integrity** (salary, tax, compliance) across all automated operations.* Maximise **type-safety and traceability** by leveraging the T3 stack (TypeScript + tRPC + Prisma).* Guarantee **Hebrew RTL UX** and localisation whenever user-facing text is emitted.## 2  Agent Roles & Responsibilities| Agent                  | Core Duties                                                                                       | Key APIs                                     || ---------------------- | ------------------------------------------------------------------------------------------------- | -------------------------------------------- || **UI Assistant**       | Generate/modify React components, enforce RTL layout, inject shadcn/ui components                 | `@dashboard/components`, `src/components/ui` || **Data Validator**     | Validate incoming payloads (Forms 101, transactions) with shared **Zod** schemas                  | `src/server/api/**/*.ts`                     || **Payroll Calculator** | Trigger monthly payslip calculation, create `SalaryRecord` rows, handle drafts vs approved states | `trpc.payroll.calculate`                     || **Compliance Agent**   | Check legal constraints (min-wage, deposit caps, visa dates), raise alerts                        | `trpc.alerts.*`                              || **Alerts Manager**     | De-duplicate, escalate, and mark alerts as handled                                                | `alerts` DB table                            || **Report Generator**   | Compile statutory forms (102/126/106), MASAV files, and dashboard aggregates                      | `reports` domain                             |> **Note:**  Agents may combine roles when justified, but the single-responsibility principle is strongly preferred.## 3  Interaction Contract### 3.1 tRPC Procedures* **Always** import procedures from the generated `api` client (e.g. `api.employee.getAll.useQuery`) to preserve end-to-end types \[¹].* Wrap server mutations in `startTransition` when UI reactivity is required (to avoid blocking).### 3.2 Prisma Transactions* Group related DB writes with `$transaction()` to avoid partial commits \[²].* Use `prisma.$extends()` hooks for cross-cutting concerns (e.g. soft-delete).### 3.3 Error Handling* Report business errors via `TRPCError` codes; map to toast notifications on the client.* Include `ctx.session.user.id` in log context for auditability.## 4  Security & Privacy| Concern                | Rule                                                                                                                               || ---------------------- | ---------------------------------------------------------------------------------------------------------------------------------- || **Auth**               | Enforce `protectedProcedure` on any route returning personal or payroll data. Use NextAuth.js middleware for page protection \[³]. || **RBAC**               | Check `role` and `employerId` inside each procedure; never trust client payloads.                                                  || **PII**                | Mask bank account numbers when logging; do not store CVV or full IDs.                                                              || **Encryption at Rest** | Leverage PostgreSQL `pgcrypto` for sensitive columns; configure KMS keys in AWS for S3 uploads.                                    |## 5  Localization & RTL* Render root containers with `dir="rtl"` and the **Heebo** font family.* Use Tailwind logical properties (`ml`/`mr` replacements) or the official RTL plugin \[⁴].* Numbers inside Hebrew text remain **LTR**; prefer `unicode-bidi: plaintext` when mixing directions \[⁵].## 6  Performance Guidelines1. **Prefetch subsequent pages** with React Query's `prefetch` utility after successful queries \[⁶].2. Invalidate granular query keys (`['payslip', employeeId, month]`) post-mutation to minimise cache churn.3. Defer heavy report building to background jobs (Edge Functions scheduled on Vercel or AWS Lambda).## 7  Coding Conventions (Agent-side)* **Language:** TypeScript 5.x in ES modules.* **File names:** `kebab-case`; exported classes/functions in `PascalCase`.* **Doc Strings:** JSDoc with `@example` blocks.* **Testing:** Use `vitest` for pure logic; `react-testing-library` for UI flows.## 8  Sample Skeleton```ts// src/agents/payroll-calculator.tsimport { api } from '@/trpc';import { z } from 'zod';/** * PayrollCalculator triggers net salary calculation for a single employee. */export async function run(input: unknown) {  const schema = z.object({ employeeId: z.string().uuid(), month: z.string().regex(/^\d{4}-\d{2}$/) });  const { employeeId, month } = schema.parse(input);  // Verify prerequisites  const has101 = await api.tax.form101Exists.query({ employeeId, month });  if (!has101) throw new Error('Missing Form 101');  // Perform calculation in a single transaction  await api.payroll.calculate.mutate({ employeeId, month });}```## 9  Monitoring & Observability* Emit structured logs to stdout; Vercel captures them automatically.* Attach `traceparent` headers when making cross-service calls for OpenTelemetry support \[⁷].* Surface agent health in the admin dashboard via `@dashboard/metrics` widgets.## 10  Glossary| Term          | Definition                                                                            || ------------- | ------------------------------------------------------------------------------------- || **Form 101**  | Israeli annual employee tax declaration.                                              || **MASAV**     | Standard bank transfer file format in Israel.                                         || **TRPCError** | Error class used to propagate typed errors in tRPC.                                   || **T3 Stack**  | A React/TypeScript stack composed of Next.js, tRPC, Prisma, Tailwind, shadcn/ui, Zod. |---### Footnotes1. tRPC documentation: [https://trpc.io/docs](https://trpc.io/docs)2. Prisma transactions: [https://www.prisma.io/docs/orm/prisma-client/queries/transactions](https://www.prisma.io/docs/orm/prisma-client/queries/transactions)3. NextAuth.js securing pages: [https://next-auth.js.org/tutorials/securing-pages-and-api-routes](https://next-auth.js.org/tutorials/securing-pages-and-api-routes)4. Tailwind RTL support: [https://tailwindcss.com/blog/tailwindcss-v3-3](https://tailwindcss.com/blog/tailwindcss-v3-3)5. W3C bidi tutorial: [https://www.w3.org/International/tutorials/bidi-xhtml/](https://www.w3.org/International/tutorials/bidi-xhtml/)6. React Query invalidation: [https://tanstack.com/query/v4/docs/framework/react/guides/query-invalidation](https://tanstack.com/query/v4/docs/framework/react/guides/query-invalidation)7. Next.js App Router OpenTelemetry guide: [https://nextjs.org/docs/app/guides](https://nextjs.org/docs/app/guides)## System Requirements - Payroll Management System for Foreign Workers> **IMPORTANT NOTE:** This application is primarily in Hebrew and follows RTL (Right-to-Left) design principles. All user-facing content, forms, and reports must support Hebrew text and RTL layout.### 1. Employee Management* Add, edit, and delete employees* Support for foreign and local workers, including residency status for social security* Store banking details, base salary, hourly rate, and data about sector, country, agreement type, etc.* Manage department and employer assignment (for multiple employers)* Manage fixed salary profile (transportation, housing)* Validation for required fields, dependent fields (e.g., country for foreign workers), valid dates* View employment history (start and end dates)### 2. Tax Forms and Data (Form 101)* Add Form 101 for each employee for each tax year* Support details for marital status, children, residency, additional income* Validation for tax credits, exemptions* Prevent salary calculation without a valid form* Digital signature capability for Form 101### 3. Employee Transactions Management* Enter salary transactions (overtime, absences, advances, etc.) by month* Support for salary components by component code* Validation for amounts, quantities, dates* Mark transactions as "processed" after payslip calculation* Import option from file/external system### 4. Payslip Calculation and Issuance* Automatic salary calculation based on transactions, employee details, and regulatory settings* Create salary components (SalaryRecords) based on data* Calculate income tax according to credits and tax brackets* Calculate social security and health insurance based on residency and employee type* Calculate pension/deposit contributions* Display results: gross, deductions, net* Change payslip status: Draft → Calculated → Approved → Paid* Prevent duplicate calculations for the same month/employee### 5. Social Benefits Management (Provident Fund)* Create monthly deposits to funds: pension, deposit, workers' committee* Calculate based on percentages, caps, employee sector* Separate employee portion, employer portion, severance pay* Support for different types of funds (enum)* Monthly/yearly deposit reports### 6. Alerts* Create automatic alerts for:  * Missing Form 101  * Visa expiration  * Overtime violations  * Missing deposit for foreign worker* View alerts in a central interface* Option to mark as handled### 7. Reports and Payments* Form 102 - Monthly report: Social security, tax, deposits* Form 126 - Annual report* Form 106 - Issue to employees* MASAV payment file (bank payments)* Payslip summary report by month, employer, department### 8. User and Permission Management* Permission system by role: Admin, Employer, Viewer* User linked to employer - sees only their data* Support for employee login to view payslip (optional)### 9. Regulatory Compliance* Tax calculation according to current tax brackets and credits* Support for foreign workers, residents, infiltrators - different calculation for each group* Prevent legal violations (deduction above permitted amount, without form, salary below minimum)* Calculate deposit according to cap, percentages, immediate applicability* Separate employee reporting by type in forms### 10. User Interface (UI)* Employee management: table, search, editing* Monthly transaction entry* Payslip display including components and analysis* Dashboard with calculation statuses/alerts* Orderly process of calculation, approval, payment

This application uses the T3 Stack with the following technologies:

- **tRPC**: Type-safe API layer connecting frontend and backend
- **Prisma**: ORM for database interactions with PostgreSQL
- **PostgreSQL**: Primary database
- **Next.js**: React framework with App Router
- **Tailwind CSS**: Utility-first CSS framework
- **shadcn/ui**: Component library built on Radix UI
- **TypeScript**: Strongly typed language throughout the codebase
- **Zod**: Schema validation

## Project Structure

- **`src/app`**: Next.js App Router pages and routes
- **`src/components`**: Reusable UI components
  - **`src/components/ui`**: shadcn/ui components
- **`src/server`**: Server-side code
  - **`src/server/api`**: tRPC API definitions
  - **`src/server/auth`**: Authentication setup
  - **`src/server/db.ts`**: Prisma client initialization
- **`src/trpc`**: Client-side tRPC setup
- **`prisma`**: Database schema and migrations

## Key Conventions

### Route Organization

- **Dashboard Features**: Use `@dashboard` prefix for dashboard-related functionality
  - **Example**: `@dashboard/components`, `@dashboard/hooks`

### API Hooks Organization

- **Group related hooks in dedicated files**
- **Use consistent naming conventions**
- **Provide documentation for complex hooks**

```typescript
// ✅ DO: Organize hooks by domain/entity
// src/app/dashboard/hooks/useEmployer.ts
export const useEmployers = () => {
  return api.employer.getAll.useQuery();
};

// ❌ DON'T: Mix unrelated hooks in one file
```

### tRPC Implementation

- **API routes are organized in domain-specific routers**
- **Use Zod for input validation**
- **Procedures are either `publicProcedure` or `protectedProcedure`**
- **Client-side hooks follow React Query patterns with proper loading/error states**

```typescript
// Server-side router example
export const userRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(
      z.object({
        page: z.number().default(1),
        limit: z.number().default(10),
        status: z.enum(["active", "inactive", "all"]).default("all"),
      })
    )
    .query(async ({ ctx, input }) => {
      // Implementation
    }),
});

// Client-side hook example
export const useUsers = (page: number) => {
  const { data, isLoading } = api.user.getAll.useQuery({ page });

  return {
    users: data?.users,
    isLoading,
  };
};
```

### Component Patterns

- **Use shadcn/ui components whenever possible**
- **Implement loading states for all data-dependent components**
- **Follow RTL (Right-to-Left) conventions for Hebrew content**
- **Use Tailwind utility classes for styling**

```tsx
// Component example
<div className="flex flex-col w-full gap-4 p-6">
  {isLoading ? (
    <Skeleton className="h-[200px]" />
  ) : (
    <DataTable data={data} columns={columns} />
  )}
</div>
```

### Form Implementation

- **Use Zod with React Hook Form**
- **Reuse API validation schemas in forms**
- **Maintain type safety throughout the form submission process**

```typescript
// Example form implementation
const userCreateSchema = api.user.create.getInputSchema();

const {
  register,
  handleSubmit,
  formState: { errors },
} = useForm({
  resolver: zodResolver(userCreateSchema),
  defaultValues: {
    name: "",
    email: "",
  },
});
```

### Query Optimization

- **Implement prefetching for better UX**
- **Use proper query invalidation patterns**
- **Handle loading, error, and success states consistently**

`typescript// Example with prefetching and invalidationexport const useEmployers = (page: number) => {  const utils = api.useContext();  const { data, isLoading } = api.employer.getAll.useQuery({ page });  // Prefetch next page  React.useEffect(() => {    if (data && data.pageCount > page) {      void utils.employer.getAll.prefetch({ page: page + 1 });    }  }, [data, page, utils]);  // Mutation with invalidation  const createEmployer = api.employer.create.useMutation({    onSuccess: () => {      void utils.employer.getAll.invalidate();    },  });  return {    employers: data?.employers,    isLoading,    createEmployer: createEmployer.mutate,  };};`### Error Handling- **Implement consistent error UI patterns**- **Use try/catch blocks for async operations**- **Display user-friendly error messages**- **Log detailed errors for debugging**`tsx// ✅ DO: Implement proper error handlingtry {  await createEmployer(data);  toast.success("מעסיק נוצר בהצלחה");} catch (error) {  console.error("Error creating employer:", error);  toast.error(error instanceof Error ? error.message : "אירעה שגיאה");}// ✅ DO: Handle errors in UI{error ? (  <Alert variant="destructive">    <AlertTitle>שגיאה</AlertTitle>    <AlertDescription>{formatErrorMessage(error)}</AlertDescription>  </Alert>) : null}// ❌ DON'T: Ignore errorsawait createEmployer(data);`### RTL Support- **Use dir="rtl" for Hebrew content containers**- **Adjust flex directions and margins for RTL**- **Ensure shadcn components render correctly in RTL mode**`tsx// ✅ DO: Proper RTL container<div dir="rtl" className="font-hebrew">  <h1>כותרת בעברית</h1>  <p>תוכן בעברית</p></div>// ✅ DO: RTL-aware flex directions<div className="flex flex-row-reverse">  <div className="ml-0 mr-4">תוכן ימני</div>  <div>תוכן שמאלי</div></div>`### File Naming Conventions- **Use kebab-case for file names**- **Use PascalCase for component names**- **Use camelCase for variables, functions, and instances**- **Group related files in dedicated folders**`// ✅ DO: Follow the naming conventionsrc/  components/    ui/      data-table.tsx       // Component file in kebab-case      DataTable.tsx        // Component exported in PascalCase    dashboard/      metrics-card.tsx     // Component file in kebab-case       MetricsCard.tsx      // Component exported in PascalCase`### Authentication and Authorization- **Use NextAuth.js for authentication**- **Implement role-based access control**- **Protect routes and API endpoints**- **Check user permissions in tRPC procedures**`typescript// ✅ DO: Check permissions in tRPC proceduresexport const adminRouter = createTRPCRouter({  getAdminData: protectedProcedure    .input(z.object({ /* inputs */ }))    .use(({ ctx, next }) => {      if (!ctx.session.user.isAdmin) {        throw new TRPCError({ code: "FORBIDDEN" });      }      return next();    })    .query(async ({ ctx, input }) => {      // Admin-only query implementation    }),});// ✅ DO: Protect routes client-sideconst AdminPage = () => {  const { data: session, status } = useSession();    if (status === "loading") {    return <LoadingSpinner />;  }    if (!session || !session.user.isAdmin) {    return <AccessDenied />;  }    return (    <AdminDashboard />  );};`### Testing- **Write unit tests for critical business logic**- **Use React Testing Library for component tests**- **Mock tRPC procedures for frontend tests**- **Use test database for backend tests**`typescript// ✅ DO: Test components with React Testing Libraryimport { render, screen } from "@testing-library/react";import { UserForm } from "./UserForm";describe("UserForm", () => {  it("should display validation errors", async () => {    render(<UserForm />);        fireEvent.click(screen.getByRole("button", { name: /submit/i }));        expect(await screen.findByText("שם הוא שדה חובה")).toBeInTheDocument();  });});`### Performance Optimization- **Use Next.js Image component for optimized images**- **Implement code splitting with dynamic imports**- **Minimize re-renders with React.memo and useMemo**- **Optimize Tailwind usage with the JIT compiler**`tsx// ✅ DO: Use Next.js Image componentimport Image from "next/image";<Image   src="/company-logo.png"  width={200}  height={100}  alt="Company Logo"  priority/>// ✅ DO: Use dynamic imports for code splittingimport dynamic from "next/dynamic";const DynamicChart = dynamic(() => import("@/components/Chart"), {  loading: () => <Skeleton height={300} />,  ssr: false,});`### Prisma Best Practices- **Use transactions for related database operations**- **Implement proper data seeding**- **Create indexes for frequently queried fields**- **Use Prisma's native filtering and pagination**`typescript// ✅ DO: Use transactions for related operationsconst createUserWithEmployer = async (userData, employerData) => {  return await db.$transaction(async (tx) => {    const employer = await tx.employer.create({      data: employerData,    });        return await tx.user.create({      data: {        ...userData,        employerId: employer.id,      },    });  });};// ✅ DO: Use Prisma's native filteringconst getFilteredUsers = async (filters) => {  return await db.user.findMany({    where: {      status: filters.status,      employerId: filters.employerId,      createdAt: {        gte: filters.startDate,        lte: filters.endDate,      },    },    orderBy: {      createdAt: "desc",    },    take: filters.limit,    skip: (filters.page - 1) * filters.limit,  });};`
