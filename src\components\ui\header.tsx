"use client";

import { useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";

export function Header() {
	const { data: session } = useSession();

	return (
		<header className="absolute top-0 right-0 left-0 z-50 border-white/10 border-b bg-white/20 backdrop-blur-md">
			<div className="container mx-auto px-4">
				<div className="flex h-20 items-center justify-between">
					{/* Logo */}
					<Link
						href="/"
						className="flex items-center space-x-2 rtl:space-x-reverse"
					>
						<div className="relative flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-blue-600 to-blue-400">
							<span className="font-bold text-white text-xl">S</span>
						</div>
						<span className="font-bold text-navy-800 text-xl">שכר מערכות</span>
					</Link>

					{/* Navigation */}
					<nav className="hidden items-center md:flex">
						<ul className="flex space-x-8 rtl:space-x-reverse">
							{[
								{ name: "ראשי", href: "/" },
								{ name: "תכונות", href: "#features" },
								{ name: "מחירים", href: "#pricing" },
								{ name: "לקוחות", href: "#clients" },
								{ name: "צור קשר", href: "#contact" },
							].map((item) => (
								<li key={item.name}>
									<Link
										href={item.href}
										className="font-medium text-navy-700 transition-colors hover:text-blue-600"
									>
										{item.name}
									</Link>
								</li>
							))}
						</ul>
					</nav>

					{/* Auth buttons */}
					<div className="flex items-center space-x-3 rtl:space-x-reverse">
						{session ? (
							<div className="flex items-center gap-4">
								<span className="hidden text-navy-700 md:inline">
									שלום, {session.user?.name || "משתמש"}
								</span>
                                                                <Link
                                                                        href="/owner-dashboard"
									className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
								>
									לוח בקרה
								</Link>
							</div>
						) : (
							<>
								<Link
									href="/login"
									className="hidden px-4 py-2 text-navy-700 transition-colors hover:text-blue-600 md:inline"
								>
									התחברות
								</Link>
								<Link
									href="/login"
									className="rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
								>
									התחל ניסיון חינם
								</Link>
							</>
						)}

						{/* Mobile menu button */}
						<button
							type="button"
							aria-label="Toggle menu"
							className="rounded-lg p-2 text-navy-700 hover:bg-blue-100/50 md:hidden"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-6 w-6"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<title>Menu</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M4 6h16M4 12h16M4 18h16"
								/>
							</svg>
						</button>
					</div>
				</div>
			</div>
		</header>
	);
}
