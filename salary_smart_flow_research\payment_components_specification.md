# מפרט מסך מרכיבי תשלום (Payment Components)

## מטרת המסך
מסך מרכיבי תשלום מאפשר למנהל המערכת להגדיר, לערוך ולנהל את כל סוגי רכיבי התשלום במערכת השכר. רכיבים אלה מהווים את הבסיס לחישוב שכר העובדים ומשמשים בתלושי השכר.

## מבנה המסך

### כותרת ראשית
- **כותרת:** "מרכיבי תשלום"
- **כפתור פעולה ראשי:** "דוח שינויים" - מא<PERSON><PERSON>ר הפקת דוח של שינויים שנעשו ברכיבי התשלום

### טבלת רכיבי תשלום
טבלה מרכזית המציגה את כל רכיבי התשלום במערכת עם העמודות הבאות:

#### עמודות הטבלה
1. **שם התשלום** - שם הרכיב כפי שיופיע בתלוש השכר
   - מיון אפשרי (כפתורי מיון ▲▼)
   - דוגמאות: גמול, החזר הוצאות, הבראה, אש"ל חודשית, שעות נוספות, וכו'

2. **סוג תשלום** - קטגוריה של רכיב התשלום
   - דוגמאות: גמול, החזר הוצאות, הבראה, מזומנים, פדיון, וכו'

3. **חישוב נטו / הכנסה** - שיטת החישוב של הרכיב
   - אפשרויות: חייב מ"ה, פטור מ"ה

4. **חישוב ביטוח לאומי** - האם הרכיב חייב בביטוח לאומי
   - אפשרויות: חייב ב"ל, פטור ב"ל

5. **קובע לקצבה ופיצויים** - האם הרכיב נכלל בחישוב קצבה ופיצויים
   - אפשרויות: כן/לא (סימון ✓)

6. **אחוז מהמשכורת** - אחוז מהשכר הבסיסי (אם רלוונטי)
   - דוגמאות: 100%, 125%, 150%, 200%, 50%, 4%, וכו'

7. **קוד תגמול** - קוד פנימי לסיווג התגמול
   - כולל אפשרות לפתיחת תפריט נפתח

8. **סדר** - מספר סידורי לקביעת סדר הופעת הרכיבים בתלוש

9. **קבוצה** - שיוך הרכיב לקבוצה לוגית
   - דוגמאות: שכר יסודי, תוספות, וכו'

10. **חד פעמי** - האם הרכיב הוא חד פעמי או קבוע
    - אפשרויות: כן/לא (סימון ✓)

11. **פעולות** - עמודת פעולות עם אפשרויות עריכה
    - כפתורי פעולה לכל שורה המאפשרים עריכה, מחיקה, שכפול וכו'

### אפשרויות סינון וחיפוש
- שדה חיפוש לסינון רכיבי תשלום לפי שם או מאפיינים
- אפשרות לסינון לפי קטגוריות או סוגי תשלום

### כפתורי פעולה
- **הוספה** - כפתור להוספת רכיב תשלום חדש (לא נראה בתצלום המסך אך סביר שקיים)
- **בדיקות** - כפתורים לבדיקת תקינות הגדרות הרכיבים

## טופס עריכה/הוספת רכיב תשלום
בעת לחיצה על הוספת רכיב חדש או עריכת רכיב קיים, נפתח טופס עם השדות הבאים:

### שדות הטופס
1. **שם התשלום** - שדה טקסט להזנת שם הרכיב
2. **סוג תשלום** - רשימה נפתחת לבחירת סוג הרכיב
3. **חישוב מס הכנסה** - רשימה נפתחת לבחירת אופן חישוב מס הכנסה
4. **חישוב ביטוח לאומי** - רשימה נפתחת לבחירת אופן חישוב ביטוח לאומי
5. **קובע לקצבה ופיצויים** - תיבת סימון
6. **אחוז מהמשכורת** - שדה מספרי להזנת אחוז
7. **קוד תגמול** - רשימה נפתחת לבחירת קוד תגמול
8. **סדר** - שדה מספרי לקביעת סדר הופעה
9. **קבוצה** - רשימה נפתחת לבחירת קבוצת שיוך
10. **חד פעמי** - תיבת סימון

### כפתורי פעולה בטופס
- **שמירה** - לשמירת השינויים
- **ביטול** - לביטול הפעולה וחזרה למסך הראשי

## קשרים ותלויות
1. **קשר למסך תבנית שכר** - רכיבי התשלום משמשים בהגדרת תבניות שכר
2. **קשר למסך הסכמי שכר** - רכיבי התשלום משמשים בהגדרת הסכמי שכר
3. **קשר לתלושי שכר** - רכיבי התשלום מופיעים בתלושי השכר של העובדים

## תרחישי שימוש עיקריים
1. **הוספת רכיב תשלום חדש** - מנהל מערכת מוסיף רכיב תשלום חדש כמו "תוספת מקצועית"
2. **עדכון רכיב תשלום קיים** - שינוי הגדרות של רכיב קיים, למשל שינוי אחוז מהמשכורת
3. **הגדרת חוקי מיסוי לרכיב** - קביעה האם הרכיב חייב במס הכנסה וביטוח לאומי
4. **הפקת דוח שינויים** - הפקת דוח המציג את השינויים שנעשו ברכיבי התשלום לאורך זמן

## הרשאות
- גישה למסך זה מוגבלת למשתמשים בעלי הרשאות ניהול מערכת או ניהול שכר
- צפייה ועריכה של רכיבי תשלום דורשת הרשאות מתאימות

## התנהגות המסך
1. **טעינה ראשונית** - בטעינת המסך מוצגים כל רכיבי התשלום המוגדרים במערכת
2. **מיון** - לחיצה על כותרת עמודה ממיינת את הטבלה לפי אותה עמודה
3. **סינון** - הזנת טקסט בשדה החיפוש מסננת את הרכיבים המוצגים
4. **עריכה** - לחיצה על כפתור עריכה פותחת את טופס העריכה עם נתוני הרכיב הנבחר
5. **שמירה** - לאחר עריכה או הוספה, שמירת הנתונים מעדכנת את הטבלה

## הערות נוספות
- המסך מאפשר גמישות רבה בהגדרת רכיבי תשלום שונים לפי צרכי הארגון
- ניתן להגדיר רכיבים קבועים (כמו שכר בסיס) ורכיבים משתנים (כמו שעות נוספות)
- המערכת תומכת בהגדרת אחוזים שונים מהשכר הבסיסי (100%, 125%, 150%, וכו')
