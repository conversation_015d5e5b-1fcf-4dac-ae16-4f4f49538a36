/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { FileText } from "lucide-react";

interface S3DocumentViewerProps {
  fileUrl: string;
  fileName?: string;
  buttonText?: string;
  buttonClassName?: string;
  buttonVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  buttonSize?: "default" | "sm" | "lg" | "icon";
  onError?: (error: Error) => void;
}

type SignedUrlResponse = {
  signedUrl: string;
  error?: string;
};

export function S3DocumentViewer({
  fileUrl,
  fileName,
  buttonText = "צפה במסמך",
  buttonClassName = "",
  buttonVariant = "outline",
  buttonSize = "sm",
  onError,
}: S3DocumentViewerProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Extract key from S3 URL
  const extractS3Key = (url: string) => {
    try {
      const { pathname } = new URL(url);
      return pathname.startsWith('/') ? pathname.slice(1) : pathname;
    } catch (error) {
      console.error('Error extracting S3 key:', error);
      return null;
    }
  };

  // Open document with signed URL
  const openDocument = async () => {
    if (!fileUrl) return;
    
    setIsLoading(true);
    
    try {
      const key = extractS3Key(fileUrl);
      if (!key) {
        throw new Error("Invalid S3 file URL");
      }
      
      // Get signed URL from our API
      const response = await fetch(`/api/document?key=${encodeURIComponent(key)}`);
      
      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error ?? "Failed to get signed URL");
      }
      
      const data = await response.json() as SignedUrlResponse;
      
      if (!data.signedUrl) {
        throw new Error("No signed URL returned");
      }
      
      // Open the document in a new tab with security attributes
      window.open(data.signedUrl, "_blank", "noopener,noreferrer");
    } catch (error) {
      console.error("Error opening document:", error);
      toast.error("שגיאה בטעינת המסמך");
      onError?.(error as Error);
    } finally {
      setIsLoading(false);
    }
  };

  // Display file name if available
  const displayName = fileName ?? (fileUrl ? fileUrl.split('/').pop() : "");

  return (
    <div className="flex items-center gap-2 rtl">
      <Button
        type="button"
        variant={buttonVariant}
        size={buttonSize}
        onClick={openDocument}
        disabled={isLoading || !fileUrl}
        className={buttonClassName}
      >
        <FileText className="h-4 w-4 ml-1" />
        {isLoading ? "טוען..." : buttonText}
      </Button>
      {displayName && <span className="text-sm truncate max-w-[200px]">{displayName}</span>}
    </div>
  );
} 