/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
  // dont break the build when tslint errors are present
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Add TypeScript type checking during build
  // typescript: {
  // Don't fail build on type errors since we want to handle them gracefully
  //   ignoreBuildErrors: true
  //}
  
  // Hot reload optimizations
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Optimize hot reload performance
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: /node_modules/,
      };
    }
    return config;
  },
  
  // Enable experimental features for better hot reload
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },
};

export default config;
