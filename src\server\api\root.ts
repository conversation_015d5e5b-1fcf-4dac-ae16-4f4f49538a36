import { createTR<PERSON><PERSON>outer, createCallerFactory } from "@/server/api/trpc";
import { dashboardRouter } from "@/server/api/routers/dashboard";
import { employerRouter } from "@/server/api/routers/employer";
import { employeeRouter } from "@/server/api/routers/employee";
import { userRouter } from "@/server/api/routers/user";
import { auditLogRouter } from "@/server/api/routers/auditLog";
import { tenantRouter } from "@/server/api/routers/tenant";
import { reportRouter } from "@/server/api/routers/report";
import { integrationRouter } from "@/server/api/routers/integration";
import { documentSettingsRouter } from "@/server/api/routers/documentSettings";
import { associationsRouter } from "./routers/associations";
import { attendanceAgreementRouter } from "./routers/attendance-agreement";
import { shiftRouter } from "./routers/shift";
import { overtimeRuleRouter } from "./routers/overtime-rule";
import { breakRuleRouter } from "./routers/break-rule";
import { locationRouter } from "./routers/location";
import { movementTypeRouter } from "./routers/movement-type";
import { employeeAgreementRouter } from "./routers/employee-agreement";
import { deductionComponentRouter } from "./routers/deduction-component";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  dashboard: dashboardRouter,
  employer: employerRouter,
  employee: employeeRouter,
  user: userRouter,
  auditLog: auditLogRouter,
  tenant: tenantRouter,
  report: reportRouter,
  integration: integrationRouter,
  documentSettings: documentSettingsRouter,
  associations: associationsRouter,
  attendanceAgreement: attendanceAgreementRouter,
  shift: shiftRouter,
  overtimeRule: overtimeRuleRouter,
  breakRule: breakRuleRouter,
  location: locationRouter,
  movementType: movementTypeRouter,
  employeeAgreement: employeeAgreementRouter,
  deductionComponent: deductionComponentRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.user.all();
 *       ^? User[]
 */
export const createCaller = createCallerFactory(appRouter);
