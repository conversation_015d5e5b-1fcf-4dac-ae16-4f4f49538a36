import { <PERSON>Footer } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";

export type TablePaginationProps = {
  page: number;
  setPage: (page: number) => void;
  hasNextPage?: boolean;
};

export function TablePagination({
  page,
  setPage,
  hasNextPage = true,
}: TablePaginationProps) {
  return (
    <CardFooter className="flex justify-between py-4">
      <Button
        variant="outline"
        onClick={() => setPage(Math.max(1, page - 1))}
        disabled={page === 1}
      >
        הקודם
      </Button>
      <Button
        variant="outline"
        onClick={() => setPage(page + 1)}
        disabled={!hasNextPage}
      >
        הבא
      </Button>
    </CardFooter>
  );
} 