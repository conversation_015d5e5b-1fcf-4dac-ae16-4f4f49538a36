"use client";

import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/rtl-components";
import { DialogFooter } from "@/components/ui/dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, type SubmitHandler } from "react-hook-form";
import { employerFormSchema, type EmployerFormValues } from "./types";

type EmployerFormProps = {
  onSubmit?: (data: EmployerFormValues) => void;
  isSubmitting?: boolean;
  initialData?: Partial<EmployerFormValues>;
};

export function EmployerForm({ 
  onSubmit, 
  isSubmitting = false,
  initialData
}: EmployerFormProps) {
  const form = useForm<EmployerFormValues>({
    resolver: zodResolver(employerFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      identifier: initialData?.identifier || "",
      status: initialData?.status || "active",
    },
  });

  const handleSubmit: SubmitHandler<EmployerFormValues> = (data) => {
    if (onSubmit) {
      onSubmit(data);
      form.reset(); // Reset form after submission
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>שם המעסיק</FormLabel>
              <FormControl>
                <Input placeholder="הזן שם מעסיק" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="identifier"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>מזהה</FormLabel>
              <FormControl>
                <Input placeholder="הזן מזהה" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <DialogFooter className="mt-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "מוסיף..." : "הוסף מעסיק"}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
} 