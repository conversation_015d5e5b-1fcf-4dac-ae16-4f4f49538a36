import pino from "pino";

/**
 * Application logger using Pino
 * 
 * Features:
 * - Environment-specific logging levels (info in production, debug in development)
 * - Simple API compatible with console methods (logger.info, logger.error, etc.)
 */
const logger = pino({
  level: process.env.NODE_ENV === "production" ? "info" : "debug",
  // Disable transport in Next.js to avoid Worker issues
  // transport: process.env.NODE_ENV !== "production" 
  //   ? {
  //       target: "pino-pretty",
  //       options: { 
  //         colorize: true,
  //         translateTime: "SYS:standard"
  //       }
  //     } 
  //   : undefined
});

export default logger; 