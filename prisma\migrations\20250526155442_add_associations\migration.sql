/*
  Warnings:

  - You are about to drop the column `completedAt` on the `Form101` table. All the data in the column will be lost.
  - You are about to drop the column `data` on the `Form101` table. All the data in the column will be lost.
  - You are about to drop the column `documentId` on the `Form101` table. All the data in the column will be lost.
  - You are about to drop the column `validUntil` on the `Form101` table. All the data in the column will be lost.
  - You are about to alter the column `additionalCreditPoints` on the `Form101` table. The data in that column could be lost. The data in that column will be cast from `Decimal(5,2)` to `Decimal(4,2)`.
  - A unique constraint covering the columns `[employeeId,taxYear]` on the table `Form101` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `mimeType` to the `Document` table without a default value. This is not possible if the table is not empty.
  - Added the required column `uploadedBy` to the `Document` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Payslip` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "SignatureRequestStatus" AS ENUM ('PENDING', 'SIGNED', 'EXPIRED', 'CANCELLED');

-- CreateEnum
CREATE TYPE "Form101Status" AS ENUM ('DRAFT', 'PENDING_SIGNATURE', 'SIGNED', 'SUBMITTED');

-- CreateEnum
CREATE TYPE "AssociationType" AS ENUM ('DEPARTMENT', 'ROLE', 'SALARY_TEMPLATE', 'SALARY_AGREEMENT');

-- AlterEnum
ALTER TYPE "AlertCategory" ADD VALUE 'MISSING_DOCUMENTS';

-- AlterEnum
ALTER TYPE "PayslipStatus" ADD VALUE 'SENT';

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_employeeId_fkey";

-- DropForeignKey
ALTER TABLE "Document" DROP CONSTRAINT "Document_employerId_fkey";

-- DropForeignKey
ALTER TABLE "Form101" DROP CONSTRAINT "Form101_documentId_fkey";

-- DropIndex
DROP INDEX "Document_fileType_idx";

-- DropIndex
DROP INDEX "Document_referenceModel_referenceId_idx";

-- DropIndex
DROP INDEX "Document_s3Key_idx";

-- DropIndex
DROP INDEX "Form101_employeeId_key";

-- AlterTable
ALTER TABLE "Document" ADD COLUMN     "description" TEXT,
ADD COLUMN     "mimeType" TEXT NOT NULL,
ADD COLUMN     "uploadedBy" UUID NOT NULL,
ALTER COLUMN "fileType" DROP NOT NULL,
ALTER COLUMN "url" DROP NOT NULL;

-- AlterTable
ALTER TABLE "Employer" ADD COLUMN     "niNumber" TEXT,
ADD COLUMN     "roles" "Role"[];

-- AlterTable
ALTER TABLE "Form101" DROP COLUMN "completedAt",
DROP COLUMN "data",
DROP COLUMN "documentId",
DROP COLUMN "validUntil",
ADD COLUMN     "documentUrl" TEXT,
ADD COLUMN     "signatureRequestExpiresAt" TIMESTAMP(3),
ADD COLUMN     "signatureRequestId" TEXT,
ADD COLUMN     "signatureRequestSentAt" TIMESTAMP(3),
ADD COLUMN     "status" "Form101Status" NOT NULL DEFAULT 'DRAFT',
ALTER COLUMN "additionalCreditPoints" SET DATA TYPE DECIMAL(4,2);

-- AlterTable
ALTER TABLE "Payslip" ADD COLUMN     "approvedAt" TIMESTAMP(3),
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "sentAt" TIMESTAMP(3),
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- CreateTable
CREATE TABLE "DocumentSettings" (
    "tenantId" UUID NOT NULL,
    "allowedCategories" JSONB NOT NULL,

    CONSTRAINT "DocumentSettings_pkey" PRIMARY KEY ("tenantId")
);

-- CreateTable
CREATE TABLE "SmsLog" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "phone" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "status" BOOLEAN NOT NULL,
    "failureReason" TEXT,
    "gatewayResponse" TEXT,
    "httpStatus" INTEGER,
    "sentAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SmsLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SignatureRequest" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "form101Id" UUID NOT NULL,
    "status" "SignatureRequestStatus" NOT NULL DEFAULT 'PENDING',
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "signedAt" TIMESTAMP(3),
    "signatureData" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SignatureRequest_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EmployeeRole" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmployeeRole_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalaryTemplate" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "components" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SalaryTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalaryAgreement" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "effectiveFrom" TIMESTAMP(3),
    "effectiveTo" TIMESTAMP(3),
    "terms" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SalaryAgreement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Association" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "associationType" "AssociationType" NOT NULL,
    "departmentId" UUID,
    "roleId" UUID,
    "salaryTemplateId" UUID,
    "salaryAgreementId" UUID,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Association_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "DocumentSettings_tenantId_idx" ON "DocumentSettings"("tenantId");

-- CreateIndex
CREATE INDEX "SmsLog_tenantId_idx" ON "SmsLog"("tenantId");

-- CreateIndex
CREATE INDEX "SmsLog_employeeId_idx" ON "SmsLog"("employeeId");

-- CreateIndex
CREATE INDEX "SmsLog_sentAt_idx" ON "SmsLog"("sentAt");

-- CreateIndex
CREATE INDEX "SmsLog_status_idx" ON "SmsLog"("status");

-- CreateIndex
CREATE INDEX "SignatureRequest_tenantId_idx" ON "SignatureRequest"("tenantId");

-- CreateIndex
CREATE INDEX "SignatureRequest_form101Id_idx" ON "SignatureRequest"("form101Id");

-- CreateIndex
CREATE INDEX "SignatureRequest_expiresAt_idx" ON "SignatureRequest"("expiresAt");

-- CreateIndex
CREATE INDEX "EmployeeRole_tenantId_idx" ON "EmployeeRole"("tenantId");

-- CreateIndex
CREATE INDEX "EmployeeRole_employerId_idx" ON "EmployeeRole"("employerId");

-- CreateIndex
CREATE UNIQUE INDEX "EmployeeRole_employerId_name_key" ON "EmployeeRole"("employerId", "name");

-- CreateIndex
CREATE INDEX "SalaryTemplate_tenantId_idx" ON "SalaryTemplate"("tenantId");

-- CreateIndex
CREATE INDEX "SalaryTemplate_employerId_idx" ON "SalaryTemplate"("employerId");

-- CreateIndex
CREATE UNIQUE INDEX "SalaryTemplate_employerId_name_key" ON "SalaryTemplate"("employerId", "name");

-- CreateIndex
CREATE INDEX "SalaryAgreement_tenantId_idx" ON "SalaryAgreement"("tenantId");

-- CreateIndex
CREATE INDEX "SalaryAgreement_employerId_idx" ON "SalaryAgreement"("employerId");

-- CreateIndex
CREATE UNIQUE INDEX "SalaryAgreement_employerId_name_key" ON "SalaryAgreement"("employerId", "name");

-- CreateIndex
CREATE INDEX "Association_tenantId_idx" ON "Association"("tenantId");

-- CreateIndex
CREATE INDEX "Association_employeeId_idx" ON "Association"("employeeId");

-- CreateIndex
CREATE INDEX "Association_departmentId_idx" ON "Association"("departmentId");

-- CreateIndex
CREATE INDEX "Association_roleId_idx" ON "Association"("roleId");

-- CreateIndex
CREATE INDEX "Association_salaryTemplateId_idx" ON "Association"("salaryTemplateId");

-- CreateIndex
CREATE INDEX "Association_salaryAgreementId_idx" ON "Association"("salaryAgreementId");

-- CreateIndex
CREATE INDEX "Association_associationType_idx" ON "Association"("associationType");

-- CreateIndex
CREATE INDEX "Association_startDate_endDate_idx" ON "Association"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "Document_uploadedBy_idx" ON "Document"("uploadedBy");

-- CreateIndex
CREATE UNIQUE INDEX "Form101_employeeId_taxYear_key" ON "Form101"("employeeId", "taxYear");

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentSettings" ADD CONSTRAINT "DocumentSettings_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SmsLog" ADD CONSTRAINT "SmsLog_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SmsLog" ADD CONSTRAINT "SmsLog_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SignatureRequest" ADD CONSTRAINT "SignatureRequest_form101Id_fkey" FOREIGN KEY ("form101Id") REFERENCES "Form101"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SignatureRequest" ADD CONSTRAINT "SignatureRequest_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmployeeRole" ADD CONSTRAINT "EmployeeRole_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EmployeeRole" ADD CONSTRAINT "EmployeeRole_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryTemplate" ADD CONSTRAINT "SalaryTemplate_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryTemplate" ADD CONSTRAINT "SalaryTemplate_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryAgreement" ADD CONSTRAINT "SalaryAgreement_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryAgreement" ADD CONSTRAINT "SalaryAgreement_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Association" ADD CONSTRAINT "Association_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Association" ADD CONSTRAINT "Association_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Association" ADD CONSTRAINT "Association_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Association" ADD CONSTRAINT "Association_roleId_fkey" FOREIGN KEY ("roleId") REFERENCES "EmployeeRole"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Association" ADD CONSTRAINT "Association_salaryTemplateId_fkey" FOREIGN KEY ("salaryTemplateId") REFERENCES "SalaryTemplate"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Association" ADD CONSTRAINT "Association_salaryAgreementId_fkey" FOREIGN KEY ("salaryAgreementId") REFERENCES "SalaryAgreement"("id") ON DELETE SET NULL ON UPDATE CASCADE;
