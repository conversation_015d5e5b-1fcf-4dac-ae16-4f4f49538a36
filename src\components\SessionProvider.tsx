"use client";

import { useAccessStore } from "@/hooks/useAccessStore";
import type { Role } from "@prisma/client";
import {
	SessionProvider as NextAuthSessionProvider,
	useSession,
} from "next-auth/react";
import { useEffect } from "react";

function AccessSync({ children }: { children: React.ReactNode }) {
	const { data: session } = useSession();
	const setRole = useAccessStore((s) => s.setRole);
	const clearRole = useAccessStore((s) => s.clearRole);

	useEffect(() => {
		if (session?.user?.role) {
			setRole(session.user.role as Role);
		} else {
			clearRole();
		}
	}, [session, setRole, clearRole]);

	return <>{children}</>;
}

export const SessionProvider = ({
	children,
}: { children: React.ReactNode }) => {
	return (
		<NextAuthSessionProvider>
			<AccessSync>{children}</AccessSync>
		</NextAuthSessionProvider>
	);
};
