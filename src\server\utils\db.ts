import type { Prisma } from "@prisma/client";

/**
 * Utility function to get an existing entity or create it if it doesn't exist
 * This enables idempotent synchronization of entities across systems
 */
export async function getOrCreate<
  TModel extends keyof Prisma.TypeMap["model"]
>(
  tx: Prisma.TransactionClient,
  model: TModel,
  uniqueWhere: Prisma.TypeMap["model"][TModel]["operations"]["findUnique"]["args"]["where"],
  createData: Prisma.TypeMap["model"][TModel]["operations"]["create"]["args"]["data"],
) {
  const result = await (tx as any)[model].upsert({
    where: uniqueWhere,
    update: {},
    create: createData,
  });
  
  // Add a flag to know this was just created (based on whether update was applied or not)
  return { ...result, _created: !await (tx as any)[model].findFirst({ where: uniqueWhere, select: { id: true }, take: 2 }).then((r: <PERSON><PERSON>y<{ id: unknown }>) => r.length > 1) };
}

/**
 * Utility function similar to getOrCreate but uses findFirst instead of findUnique
 * Useful when you need to find based on multiple fields that aren't a unique constraint
 */
export async function findFirstOrCreate<
  TModel extends keyof Prisma.TypeMap["model"]
>(
  tx: Prisma.TransactionClient,
  model: TModel,
  whereCondition: Prisma.TypeMap["model"][TModel]["operations"]["findFirst"]["args"]["where"],
  createData: Prisma.TypeMap["model"][TModel]["operations"]["create"]["args"]["data"],
) {
  // For findFirst, we can't use upsert directly since it requires unique constraints
  // We'll use a transaction to ensure atomicity
  const existing = await (tx as any)[model].findFirst({ where: whereCondition });
  if (existing) return existing;

  try {
    const created = await (tx as any)[model].create({ data: createData });
    // Add a flag to know this was just created
    return { ...created, _created: true };
  } catch (error) {
    // If creation fails due to a race condition, try finding again
    const existingAfterError = await (tx as any)[model].findFirst({ where: whereCondition });
    if (existingAfterError) return existingAfterError;
    
    // If we still can't find it, rethrow the original error
    throw error;
  }
} 