import { PrismaAdapter } from "@auth/prisma-adapter";
import type { DefaultSession, NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";

import { db } from "@/server/db";

/**
 * Module augmentation for `next-auth` types. Allows us to add custom properties to the `session`
 * object and keep type safety.
 *
 * @see https://next-auth.js.org/getting-started/typescript#module-augmentation
 */
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role?: string;
      employerId?: string;
      employerName?: string;
    } & DefaultSession["user"];
  }

  interface User {
    role?: string;
    employerId?: string | null;
    employerName?: string | null;
  }
}

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      role?: string;
      employerId?: string;
      employerName?: string;
    } & DefaultSession["user"];
  }

  interface User {
    role?: string;
    employerId?: string | null;
    employerName?: string | null;
  }

  interface JWT {
    id?: string;
    role?: string;
    employerId?: string;
    employerName?: string;
  }
}

/**
 * Options for NextAuth.js used to configure adapters, providers, callbacks, etc.
 *
 * @see https://next-auth.js.org/configuration/options
 */
export const authConfig = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        console.log(
          "[AUTH DEBUG] Authorize called with credentials:",
          credentials
        );

        if (!credentials?.email || !credentials?.password) {
          console.log("[AUTH DEBUG] Missing email or password");
          return null;
        }

        try {
          const user = await db.user.findFirst({
            where: {
              email: credentials.email as string,
              isActive: true,
            },
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
              tenantId: true,
              password: true,
              employerId: true,
              // get employer name from prisma by employerId field
              employer: {
                select: {
                  name: true,
                },
              },
            },
          });

          console.log("[AUTH DEBUG] User from DB:", user);

          if (!user) {
            console.log(
              "[AUTH DEBUG] User not found or not active for email:",
              credentials.email
            );
            return null;
          }
          if (!user.password) {
            console.log(
              "[AUTH DEBUG] User found but has no password hash:",
              user.id
            );
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password as string,
            user.password
          );

          console.log(
            "[AUTH DEBUG] Password validation result:",
            isPasswordValid
          );

          if (!isPasswordValid) {
            console.log("[AUTH DEBUG] Password invalid for user:", user.id);
            return null;
          }

          const returnedUser = {
            id: user.id,
            email: user.email,
            name: user.name || "",
            role: user.role,
            employerId: user.employerId || undefined,
            employerName: user.employer?.name || undefined,
          };
          console.log(
            "[AUTH DEBUG] Authorize success, returning user:",
            returnedUser
          );
          return returnedUser;
        } catch (dbError) {
          console.error("[AUTH DEBUG] Error in authorize function:", dbError);
          return null;
        }
      },
    }),
  ],
  adapter: PrismaAdapter(db),
  callbacks: {
    session: ({ session, token }) => ({
      ...session,
      user: {
        ...session.user,
        id: token.sub as string,
        role: token.role as string,
        employerId: token.employerId as string | undefined,
        employerName: token.employerName as string | undefined,
      },
    }),
    jwt: ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.employerId = user.employerId;
        token.employerName = user.employerName;
      }
      return token;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: {
    strategy: "jwt",
    maxAge: 3 * 60 * 60, // 3 hours in seconds
  },
} satisfies NextAuthConfig;
