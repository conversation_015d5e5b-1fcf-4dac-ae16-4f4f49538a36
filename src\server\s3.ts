/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { env } from "@/env";

// Create S3 client with credentials from env
const s3Client = new S3Client({
  region: env.AWS_REGION,
  credentials: {
    accessKeyId: env.AWS_ACCESS_KEY_ID,
    secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
  },
});

// Prefix for salary-related files
const SALARY_PREFIX = "_talsalary/";

/**
 * Ensures a key has the salary prefix
 */
function ensureSalaryPrefix(key: string): string {
  // If the key already starts with the prefix, return it as is
  if (key.startsWith(SALARY_PREFIX)) {
    return key;
  }
  
  // Otherwise, add the prefix
  return `${SALARY_PREFIX}${key}`;
}

/**
 * Upload a file to S3
 */
export async function uploadFile(key: string, body: Buffer, contentType?: string) {
  try {
    const prefixedKey = ensureSalaryPrefix(key);
    
    const command = new PutObjectCommand({
      Bucket: env.AWS_S3_BUCKET_NAME,
      Key: prefixedKey,
      Body: body,
      ContentType: contentType,
    });

    return await s3Client.send(command);
  } catch (error) {
    console.error("Error uploading file to S3:", error);
    throw error;
  }
}

/**
 * Get a file from S3
 */
export async function getFile(key: string) {
  try {
    const command = new GetObjectCommand({
      Bucket: env.AWS_S3_BUCKET_NAME,
      Key: ensureSalaryPrefix(key.replace(/\.\./g, "")),
    });

    return await s3Client.send(command);
  } catch (error) {
    console.error("Error getting file from S3:", error);
    throw error;
  }
}

/**
 * Delete a file from S3
 */
export async function deleteFile(key: string) {
  try {
    const command = new DeleteObjectCommand({
      Bucket: env.AWS_S3_BUCKET_NAME,
      Key: ensureSalaryPrefix(key.replace(/\.\./g, "")),
    });

    return await s3Client.send(command);
  } catch (error) {
    console.error("Error deleting file from S3:", error);
    throw error;
  }
}

/**
 * List files in a directory
 */
export async function listFiles(prefix: string) {
  try {
    const sanitizedPrefix = prefix.replace(/\.\./g, "");
    const prefixedKey = ensureSalaryPrefix(sanitizedPrefix);
    
    const command = new ListObjectsV2Command({
      Bucket: env.AWS_S3_BUCKET_NAME,
      Prefix: prefixedKey,
    });

    return await s3Client.send(command);
  } catch (error) {
    console.error("Error listing files from S3:", error);
    throw error;
  }
}

/**
 * Generate a signed URL for getting a file (time-limited access)
 */
export async function getSignedDownloadUrl(key: string, expiresIn = 3600) {
  try {
    const command = new GetObjectCommand({
      Bucket: env.AWS_S3_BUCKET_NAME,
      Key: ensureSalaryPrefix(key.replace(/\.\./g, "")),
    });

    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    console.error("Error generating signed download URL:", error);
    throw error;
  }
}

/**
 * Generate a signed URL for uploading a file (time-limited upload permission)
 */
export async function getSignedUploadUrl(key: string, contentType: string, expiresIn = 3600) {
  try {
    const prefixedKey = ensureSalaryPrefix(key);
    
    const command = new PutObjectCommand({
      Bucket: env.AWS_S3_BUCKET_NAME,
      Key: prefixedKey,
      ContentType: contentType,
    });

    return await getSignedUrl(s3Client, command, { expiresIn });
  } catch (error) {
    console.error("Error generating signed upload URL:", error);
    throw error;
  }
}

/**
 * Get public URL for a file
 */
export function getPublicUrl(key: string) {
  return `https://${env.AWS_S3_BUCKET_NAME}.s3.${env.AWS_REGION}.amazonaws.com/${encodeURIComponent(
    key
  )}`;
}