/**
 * Validates an Israeli business identifier (9 digits with checksum)
 * @param id The business identifier to validate
 * @returns boolean indicating if the ID is valid
 */
export function isValidIsraeliBusinessId(id: string): boolean {
  // Must be exactly 9 digits
  if (!/^\d{9}$/.test(id)) {
    return false;
  }

  // Calculate checksum
  const digits = id.split('').map(Number);
  const sum = digits.reduce((acc, digit, index) => {
    // Multiply by 1 or 2 alternately from right to left
    let product = digit * ((index % 2) + 1);
    // If product is greater than 9, subtract 9
    product = product > 9 ? product - 9 : product;
    return acc + product;
  }, 0);

  // Sum must be divisible by 10
  return sum % 10 === 0;
} 