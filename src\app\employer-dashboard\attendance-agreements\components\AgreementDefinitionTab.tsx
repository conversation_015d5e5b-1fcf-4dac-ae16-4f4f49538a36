"use client";

import { useState } from "react";
import { Plus, Pencil, Trash2, <PERSON><PERSON>, Search } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { format } from "date-fns";
import { he } from "date-fns/locale";
import { useAgreements, useCreateAgreement, useUpdateAgreement, useDeleteAgreement } from "../hooks";
import type { AttendanceAgreement } from "../types";
import { AgreementFormModal } from "./AgreementFormModal";

export type FormAgreement = Omit<AttendanceAgreement, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> & {
  id?: string;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  deletedAt?: Date | string | null;
};

export function AgreementDefinitionTab() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedAgreement, setSelectedAgreement] = useState<FormAgreement | null>(null);
  const [agreementToDelete, setAgreementToDelete] = useState<AttendanceAgreement | null>(null);

  // Hooks
  const { data: agreements, isLoading } = useAgreements();
  const createMutation = useCreateAgreement();
  const updateMutation = useUpdateAgreement();
  const deleteMutation = useDeleteAgreement();

  // Filter agreements based on search
  const filteredAgreements = agreements?.filter(agreement =>
    agreement.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    agreement.code.toLowerCase().includes(searchTerm.toLowerCase())
  ) ?? [];

  const handleCreate = () => {
    setSelectedAgreement(null); // Initialize with null for a new agreement
    setIsFormOpen(true);
  };

  const handleEdit = (agreement: AttendanceAgreement) => {
    setSelectedAgreement(agreement);
    setIsFormOpen(true);
  };

  const handleDuplicate = (agreement: AttendanceAgreement) => {
    const { id, createdAt, updatedAt, deletedAt, ...restOfAgreement } = agreement;
    const duplicated: FormAgreement = {
      ...restOfAgreement,
      code: `${agreement.code}_copy`,
      name: `${agreement.name} (העתק)`,
    };
    setSelectedAgreement(duplicated);
    setIsFormOpen(true);
  };

  const handleDelete = async () => {
    if (!agreementToDelete) return;

    try {
      await deleteMutation.mutateAsync({ id: agreementToDelete.id });
      toast.success("הסכם נמחק בהצלחה", {
        description: `ההסכם "${agreementToDelete.name}" נמחק מהמערכת`,
      });
      setAgreementToDelete(null);
    } catch (error) {
      toast.error("שגיאה במחיקת הסכם", {
        description: "אירעה שגיאה במחיקת ההסכם",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive"> = {
      ACTIVE: "default",
      INACTIVE: "secondary",
      DRAFT: "destructive",
    };

    const labels: Record<string, string> = {
      ACTIVE: "פעיל",
      INACTIVE: "לא פעיל",
      DRAFT: "טיוטה",
    };

    return (
      <Badge variant={variants[status] || "default"}>
        {labels[status] || status}
      </Badge>
    );
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">טוען...</div>;
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="חיפוש לפי שם או קוד..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10 w-64"
            />
          </div>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="ml-2 h-4 w-4" />
          הסכם חדש
        </Button>
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-right">קוד</TableHead>
              <TableHead className="text-right">שם ההסכם</TableHead>
              <TableHead className="text-right">תיאור</TableHead>
              <TableHead className="text-right">סטטוס</TableHead>
              <TableHead className="text-right">תאריך יצירה</TableHead>
              <TableHead className="text-right">פעולות</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAgreements.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                  {searchTerm ? "לא נמצאו הסכמים התואמים לחיפוש" : "אין הסכמים במערכת"}
                </TableCell>
              </TableRow>
            ) : (
              filteredAgreements.map((agreement) => (
                <TableRow key={agreement.id}>
                  <TableCell className="font-medium">{agreement.code}</TableCell>
                  <TableCell>{agreement.name}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {agreement.description || "-"}
                  </TableCell>
                  <TableCell>{getStatusBadge(agreement.status)}</TableCell>
                  <TableCell>
                    {format(new Date(agreement.createdAt), "dd/MM/yyyy", { locale: he })}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(agreement)}
                        title="עריכה"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDuplicate(agreement)}
                        title="שכפול"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setAgreementToDelete(agreement)}
                        title="מחיקה"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Form Modal */}
      <AgreementFormModal
        open={isFormOpen}
        onOpenChange={setIsFormOpen}
        agreement={selectedAgreement}
        onSuccess={() => {
          setIsFormOpen(false);
          setSelectedAgreement(null);
        }}
      />

      {/* Delete Confirmation */}
      <AlertDialog open={!!agreementToDelete} onOpenChange={() => setAgreementToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>האם אתה בטוח?</AlertDialogTitle>
            <AlertDialogDescription>
              פעולה זו תמחק את ההסכם "{agreementToDelete?.name}" לצמיתות.
              לא ניתן לבטל פעולה זו.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>ביטול</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground">
              מחיקה
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 