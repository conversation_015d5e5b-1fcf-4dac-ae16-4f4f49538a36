"use client";

import React, { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect, useParams, useSearchParams } from "next/navigation";

import { useDashboardData } from "../owner-dashboard/usage";
import { useEmployerAuditLogs, useEmployerUsers } from "./hooks";
import { EmployerDashboardHeader } from "./components/employer-dashboard-header";
import { EmployerDashboardTabs } from "./components/employer-dashboard-tabs";
import { EmployerMetricsCards } from "./components/employer-metrics-cards";
import { EmployerSystemAlerts } from "./components/employer-system-alerts";
import { EmployerQuickAccess } from "./components/employer-quick-access";
import { EmployerEmployeesTable } from "./components/employer-employees-table";

import type {
  Role,
  User,
  UserFormValues,
} from "../owner-dashboard/components/UsersTable";

export default function EmployerDashboardPage() {
  // Set up local state first, before any other hooks
  const [selectedPeriod, setSelectedPeriod] = useState<
    "current" | "previous" | "quarter" | "year"
  >("current");
  const [usersPage, setUsersPage] = useState(1);
  const [auditLogsActionFilter, setAuditLogsActionFilter] = useState<
    "all" | "create" | "update" | "delete"
  >("all");
  const [auditLogsPage, setAuditLogsPage] = useState(1);

  // Use session with no destructuring to avoid hooks issues
  const session = useSession();

  // Handle loading and authentication states
  if (session.status === "loading") {
    return <LoadingDashboard 
      selectedPeriod={selectedPeriod}
      setSelectedPeriod={setSelectedPeriod}
      usersPage={usersPage}
      setUsersPage={setUsersPage}
      auditLogsPage={auditLogsPage}
      setAuditLogsPage={setAuditLogsPage}
      auditLogsActionFilter={auditLogsActionFilter}
      setAuditLogsActionFilter={setAuditLogsActionFilter}
    />;
  }

  if (session.status === "unauthenticated") {
    redirect("/login");
  }

  // Now we know session is authenticated, so we can safely use session.data
  const params = useParams();
  const searchParams = useSearchParams();
  
  const employerId = params?.["employer-id"] as string || 
                    searchParams.get("employerId") || 
                    session.data?.user?.employerId || 
                    "";

  const {
    metrics,
    alerts,
    isLoading: isDashboardLoading,
    refetch: refetchDashboard,
  } = useDashboardData(selectedPeriod);

  const {
    users: apiUsers,
    isLoading: isUsersLoading,
    createUser,
    isCreating: isCreatingUser,
  } = useEmployerUsers(usersPage, employerId);

  // Transform users to match the expected User type with Role enum
  // Ensure users is always an array, even if apiUsers is undefined
  const users = React.useMemo(() => {
    return apiUsers?.map((user) => ({
      ...user,
      role: user.role as Role,
      status: user.status as "active" | "inactive",
    })) ?? [];
  }, [apiUsers]);

  const { logs, isLoading: isLogsLoading } = useEmployerAuditLogs(
    auditLogsPage,
    auditLogsActionFilter,
    employerId
  );

  const generateReport = React.useCallback(() => undefined, []);

  const refreshData = React.useCallback(() => {
    refetchDashboard();
  }, [refetchDashboard]);

  // We are authenticated and have loaded the data, render the dashboard
  return (
    <div className="flex min-h-screen w-full flex-col gap-6 p-6">
      <EmployerDashboardHeader
        selectedPeriod={selectedPeriod}
        setSelectedPeriod={setSelectedPeriod}
        refreshData={refreshData}
        isLoading={isDashboardLoading}
      />

      <EmployerSystemAlerts alerts={alerts} isLoading={isDashboardLoading} />

      <EmployerMetricsCards metrics={metrics} isLoading={isDashboardLoading} />

      <EmployerEmployeesTable employerId={employerId} />

      <EmployerDashboardTabs
        visibleTabs={["users", "reports", "logs", "associations"]}
        users={users}
        isUsersLoading={isUsersLoading}
        usersPage={usersPage}
        setUsersPage={setUsersPage}
        createUser={createUser as (data: UserFormValues) => Promise<void>}
        isCreatingUser={isCreatingUser}
        reports={[]}
        isReportsLoading={false}
        generateReport={generateReport}
        isGeneratingReport={false}
        logs={logs ?? []}
        isLogsLoading={isLogsLoading}
        auditLogsPage={auditLogsPage}
        setAuditLogsPage={setAuditLogsPage}
        auditLogsActionFilter={auditLogsActionFilter}
        setAuditLogsActionFilter={setAuditLogsActionFilter}	
      />

      <EmployerQuickAccess isLoading={isDashboardLoading} />
    </div>
  );
}

// Separate loading component to avoid conditional hooks
function LoadingDashboard({
  selectedPeriod,
  setSelectedPeriod,
  usersPage,
  setUsersPage,
  auditLogsPage,
  setAuditLogsPage,
  auditLogsActionFilter,
  setAuditLogsActionFilter
}: {
  selectedPeriod: "current" | "previous" | "quarter" | "year";
  setSelectedPeriod: React.Dispatch<React.SetStateAction<"current" | "previous" | "quarter" | "year">>;
  usersPage: number;
  setUsersPage: React.Dispatch<React.SetStateAction<number>>;
  auditLogsPage: number;
  setAuditLogsPage: React.Dispatch<React.SetStateAction<number>>;
  auditLogsActionFilter: "all" | "create" | "update" | "delete";
  setAuditLogsActionFilter: React.Dispatch<React.SetStateAction<"all" | "create" | "update" | "delete">>;
}) {
  const refreshData = React.useCallback(() => undefined, []);

  return (
    <div className="flex min-h-screen w-full flex-col gap-6 p-6">
      <EmployerDashboardHeader
        selectedPeriod={selectedPeriod}
        setSelectedPeriod={setSelectedPeriod}
        refreshData={refreshData}
        isLoading={true}
      />
      <EmployerSystemAlerts isLoading={true} />
      <EmployerMetricsCards isLoading={true} />
      <EmployerDashboardTabs
        visibleTabs={["users", "reports", "logs", "associations"]}
        users={[]}
        isUsersLoading={true}
        usersPage={usersPage}
        setUsersPage={setUsersPage}
        createUser={async () => {}} 
        isCreatingUser={false}
        reports={[]}
        isReportsLoading={true}
        logs={[]}
        isLogsLoading={true}
        auditLogsPage={auditLogsPage}
        setAuditLogsPage={setAuditLogsPage}
        auditLogsActionFilter={auditLogsActionFilter}
        setAuditLogsActionFilter={setAuditLogsActionFilter}
        generateReport={refreshData}
        isGeneratingReport={false}
      />
      <EmployerQuickAccess isLoading={true} />
    </div>
  );
}
