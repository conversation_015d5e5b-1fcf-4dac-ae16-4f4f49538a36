import { create } from "zustand";

export type DashboardPeriod = "current" | "previous" | "quarter" | "year";
export type AuditActionFilter = "all" | "create" | "update" | "delete";

interface DashboardState {
  selectedPeriod: DashboardPeriod;
  employersPage: number;
  usersPage: number;
  auditLogsPage: number;
  auditLogsActionFilter: AuditActionFilter;
  setSelectedPeriod: (period: DashboardPeriod) => void;
  setEmployersPage: (page: number) => void;
  setUsersPage: (page: number) => void;
  setAuditLogsPage: (page: number) => void;
  setAuditLogsActionFilter: (filter: AuditActionFilter) => void;
}

/**
 * Global state for the dashboard to avoid prop drilling between components.
 */
export const useDashboardStore = create<DashboardState>((set) => ({
  selectedPeriod: "current",
  employersPage: 1,
  usersPage: 1,
  auditLogsPage: 1,
  auditLogsActionFilter: "all",
  setSelectedPeriod: (selectedPeriod) => set({ selectedPeriod }),
  setEmployersPage: (employersPage) => set({ employersPage }),
  setUsersPage: (usersPage) => set({ usersPage }),
  setAuditLogsPage: (auditLogsPage) => set({ auditLogsPage }),
  setAuditLogsActionFilter: (auditLogsActionFilter) => set({ auditLogsActionFilter }),
}));
