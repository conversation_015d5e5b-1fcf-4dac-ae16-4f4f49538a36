# מפרט מסך הסכמי נוכחות (Attendance Agreements)

## מטרת המסך
מסך הסכמי נוכחות מאפשר למנהל המערכת להגדיר, לערוך ולנהל את ההסכמים השונים הקשורים לנוכחות העובדים, כולל הגדרת שעות עבודה, שעות נוספות, משמרות, הפסקות וחוקי נוכחות אחרים. הסכמים אלה מהווים את הבסיס לחישוב שעות העבודה ותשלום השכר בהתאם.

## מבנה המסך

### כותרת ראשית
- **כותרת:** "הסכמי נוכחות"
- **כפתורי פעולה ראשיים:**
  - "מיקומים מורשים להחתמה" - להגדרת מיקומים מאושרים לרישום נוכחות
  - "תיאור תנועה" - להגדרת סוגי תנועות נוכחות
  - "שכפול ההסכם" - ליצירת עותק של הסכם קיים
  - "הוספת הסכם נוכחות" - ליצירת הסכם חדש
  - "בחירת הסכם נוכחות" - לבחירת הסכם קיים

### לשוניות משנה
המסך מחולק למספר לשוניות המייצגות היבטים שונים של הסכמי נוכחות:
1. **הגדרת הסכם נוכחות** - הגדרות בסיסיות של ההסכם
2. **פרטי ההסכם** - פרטים מפורטים של ההסכם
3. **מיקומים מורשים להחתמה** - הגדרת מיקומים מאושרים לרישום נוכחות
4. **תיאור תנועה** - הגדרת סוגי תנועות נוכחות
5. **טיפול בהסכם** - אפשרויות לניהול ההסכם

### אזור פרטי הסכם
#### שדות הגדרה בסיסיים
1. **שם ההסכם** - שדה טקסט להזנת שם ההסכם
2. **מחויבות** - שדה בחירה מרשימה לסוג המחויבות
3. **שיוך ראשי** - שדה בחירה מרשימה לשיוך הראשי
4. **תנית** - שדה בחירה מרשימה לסוג התנאי
5. **עד חודש** - שדות להזנת תאריך סיום תוקף (חודש ושנה)
6. **מחודש** - שדות להזנת תאריך תחילת תוקף (חודש ושנה)

#### תיבות סימון להגדרות מיוחדות
- **מערכת נוכחות מופעלת** - תיבת סימון להפעלת מערכת הנוכחות
- **תיקון תנועות אוטומטי** - תיבת סימון לתיקון אוטומטי של תנועות נוכחות
- **שעות נוספות שבועי גובר על יומי** - תיבת סימון להגדרת עדיפות חישוב שעות נוספות
- **נוכחות מתחיל ביום** - שדה בחירה ליום תחילת שבוע העבודה
- **תקרה תנועות אוטומטי** - תיבת סימון להגבלת תנועות אוטומטיות

### אזור הגדרת שעות
#### הגדרות שעות עבודה
- **תקן שעות בחודש** - שדה מספרי להזנת תקן שעות חודשי (לדוגמה: 182)
- **תקן שעות בשבוע** - שדה מספרי להזנת תקן שעות שבועי (לדוגמה: 42)

#### הגדרות מגבלות שעות
- **מקסימום שעות ביום** - שדה מספרי להגבלת מספר שעות עבודה ביום
- **מקסימום שעות נוספות בשבוע** - שדה מספרי להגבלת שעות נוספות שבועיות

### אזור הגדרת שעות נוספות
#### שעות נוספות חודשיות
טבלה המגדירה את חישוב השעות הנוספות החודשיות:
- **מרכיב תשלום** - שם מרכיב התשלום לשעות נוספות
- **שעה נוספת** - מספר השעה הנוספת
- **ערך** - ערך השעה הנוספת (לדוגמה: 125%, 150%)

#### שעות נוספות שבועיות
טבלה המגדירה את חישוב השעות הנוספות השבועיות:
- **מרכיב תשלום** - שם מרכיב התשלום לשעות נוספות
- **שעה נוספת** - מספר השעה הנוספת
- **ערך** - ערך השעה הנוספת (לדוגמה: 125%, 150%)

#### שעות נוספות יומיות
טבלה המגדירה את חישוב השעות הנוספות היומיות:
- **מרכיב תשלום** - שם מרכיב התשלום לשעות נוספות
- **שעה נוספת** - מספר השעה הנוספת
- **ערך** - ערך השעה הנוספת (לדוגמה: 125%, 150%)

### אזור הגדרת משמרות
#### הגדרות משמרת
- **שם המשמרת** - שדה טקסט לשם המשמרת
- **התחלה עד שעה** - שדה שעה לזמן התחלת המשמרת
- **התחלה משעה** - שדה שעה לזמן סיום המשמרת
- **שעות בסיס** - שדה מספרי למספר שעות הבסיס במשמרת
- **שעות עבודה** - שדה מספרי למספר שעות העבודה במשמרת

#### הגדרות משמרת לילה
- **משמרת לילה** - תיבת סימון להגדרת משמרת לילה
- **שעת מעבר ליום הבא** - שדה שעה להגדרת זמן המעבר ליום הבא
- **משך למעבר** - שדה מספרי למשך הזמן למעבר
- **ש"נ מתחילות בשעה** - שדה שעה לתחילת חישוב שעות נוספות

### אזור הגדרת ימי עבודה
טבלה המגדירה את ימי העבודה בשבוע:
- **א** - תיבת סימון ליום ראשון
- **ב** - תיבת סימון ליום שני
- **ג** - תיבת סימון ליום שלישי
- **ד** - תיבת סימון ליום רביעי
- **ה** - תיבת סימון ליום חמישי
- **ו** - תיבת סימון ליום שישי
- **ש** - תיבת סימון ליום שבת

### אזור הגדרת הפסקות
טבלה המגדירה את ההפסקות בעבודה:
- **שם ההפסקה** - שם ההפסקה
- **משך** - משך זמן ההפסקה
- **מינימום שעות בתחילת התחשבנות** - מינימום שעות עבודה לפני התחשבות בהפסקה
- **מינימום שעות בסיום התחשבנות** - מינימום שעות עבודה אחרי התחשבות בהפסקה
- **לפי התחתמות** - האם ההפסקה נקבעת לפי החתמה
- **שעה בתחילת יום** - שעת תחילת ההפסקה ביחס לתחילת יום העבודה
- **שעה בסיום יום** - שעת סיום ההפסקה ביחס לסיום יום העבודה

### אזור העברת שעות למשרה
- **העברת סך שעות לשעות במשרה בתלוש** - תיבת סימון להעברת שעות למשרה בתלוש
- שדות בחירה מרשימה לאופן העברת השעות

## קשרים ותלויות
1. **קשר למסך תבנית שכר** - הסכמי הנוכחות משפיעים על חישוב השכר בתבניות השכר
2. **קשר לתלושי שכר** - הסכמי הנוכחות משפיעים על חישוב שעות העבודה והשעות הנוספות בתלושי השכר
3. **קשר למסך שיוכים** - הסכמי הנוכחות משויכים לעובדים באמצעות מסך השיוכים
4. **קשר למסך מרכיבי תשלום** - הסכמי הנוכחות משתמשים במרכיבי תשלום לחישוב שכר השעות הנוספות

## תרחישי שימוש עיקריים
1. **יצירת הסכם נוכחות חדש** - מנהל מערכת יוצר הסכם נוכחות חדש לקבוצת עובדים
2. **עדכון הסכם נוכחות קיים** - שינוי הגדרות של הסכם קיים, למשל שינוי חישוב שעות נוספות
3. **הגדרת משמרות** - הגדרת משמרות עבודה שונות, כולל משמרות לילה
4. **הגדרת הפסקות** - הגדרת זמני הפסקות ואופן חישובן
5. **שכפול הסכם** - יצירת עותק של הסכם קיים כבסיס להסכם חדש

## הרשאות
- גישה למסך זה מוגבלת למשתמשים בעלי הרשאות ניהול מערכת או ניהול שכר
- צפייה ועריכה של הסכמי נוכחות דורשת הרשאות מתאימות

## התנהגות המסך
1. **טעינה ראשונית** - בטעינת המסך מוצג הסכם ברירת מחדל או שדות ריקים ליצירת הסכם חדש
2. **בחירת הסכם קיים** - לחיצה על כפתור "בחירת הסכם נוכחות" מציגה רשימת הסכמים קיימים לבחירה
3. **הוספת הסכם** - לחיצה על כפתור "הוספת הסכם נוכחות" מאפשרת יצירת הסכם חדש
4. **שכפול הסכם** - לחיצה על כפתור "שכפול ההסכם" יוצרת עותק של ההסכם הנוכחי
5. **שמירה** - לאחר עריכה או הוספה, שמירת הנתונים מעדכנת את ההסכם

## הערות נוספות
- המסך מאפשר גמישות רבה בהגדרת חוקי נוכחות והסכמי עבודה שונים
- ניתן להגדיר הסכמים שונים לקבוצות עובדים שונות
- המערכת תומכת בחישוב מורכב של שעות נוספות ברמה יומית, שבועית וחודשית
- ניתן להגדיר משמרות מיוחדות כמו משמרות לילה עם חוקי חישוב ייחודיים
