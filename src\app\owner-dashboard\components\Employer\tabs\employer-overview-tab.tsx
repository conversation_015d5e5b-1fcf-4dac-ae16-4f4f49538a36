"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/rtl-components";
import { Button } from "@/components/ui/rtl-components";
import { useState } from "react";
import { FileText, UserPlus, Mail, Send } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/rtl-components";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { type EmployerTabProps } from "../types";
import { type Employer } from "@/schema/employer";

// Extended employer type with additional UI properties
interface ExtendedEmployer extends Employer {
  taxId?: string;
  registrationDate?: string;
  accountManager?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  recentActivity: Array<{
    action: string;
    user: string;
    date: string;
  }>;
}

export function EmployerOverviewTab({ employer }: EmployerTabProps) {
  const extendedEmployer = employer as ExtendedEmployer;
  const [contactDialogOpen, setContactDialogOpen] = useState(false);
  const [emailDialogOpen, setEmailDialogOpen] = useState(false);

  const contactSchema = z.object({
    name: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
  });

  type ContactValues = z.infer<typeof contactSchema>;

  const contactForm = useForm<ContactValues>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      name: (employer as ExtendedEmployer).contactPerson || "",
      phone: (employer as ExtendedEmployer).contactPhone || "",
      email: (employer as ExtendedEmployer).contactEmail || "",
    },
  });

  const handleUpdateContact = () => {
    setContactDialogOpen(true);
  };

  const onSubmitContact = (values: ContactValues) => {
    console.log("Contact updated", values);
    toast.success("פרטי הקשר עודכנו");
    setContactDialogOpen(false);
  };

  const emailSchema = z.object({
    subject: z.string().min(1, "יש להזין נושא"),
    message: z.string().min(1, "יש להזין תוכן"),
  });

  type EmailValues = z.infer<typeof emailSchema>;

  const emailForm = useForm<EmailValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: { subject: "", message: "" },
  });

  const handleSendEmail = () => {
    setEmailDialogOpen(true);
  };

  const onSubmitEmail = (values: EmailValues) => {
    console.log("Send email", values);
    toast.success("המייל נשלח בהצלחה");
    setEmailDialogOpen(false);
  };

  // Add some default recent activity if not present, for UI demonstration
  if (!extendedEmployer.recentActivity) {
    extendedEmployer.recentActivity = [
      { action: "יצירת חשבון", user: "מערכת", date: "01/01/2024" },
      { action: "עדכון פרטי מעסיק", user: "מנהל מערכת", date: "15/01/2024" },
    ];
  }

  return (
    <>
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>פרטים כלליים</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-sm text-muted-foreground">
                  שם מעסיק
                </Label>
                <p>{extendedEmployer.name || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">מזהה</Label>
                <p>{extendedEmployer.identifier || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">
                  תיק ניכויים
                </Label>
                <p>{extendedEmployer.taxId || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">סטטוס</Label>
                <Badge
                  variant={
                    extendedEmployer.status === "active"
                      ? "default"
                      : "secondary"
                  }
                >
                  {extendedEmployer.status === "active" ? "פעיל" : "לא פעיל"}
                </Badge>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">
                  תאריך הקמה
                </Label>
                <p>{extendedEmployer.registrationDate || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">
                  מנהל תיק
                </Label>
                <p>{extendedEmployer.accountManager || "—"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>פרטי קשר</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-sm text-muted-foreground">איש קשר</Label>
                <p>{extendedEmployer.contactPerson || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">טלפון</Label>
                <p>{extendedEmployer.contactPhone || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">דוא"ל</Label>
                <p>{extendedEmployer.contactEmail || "—"}</p>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">כתובת</Label>
                <p>{extendedEmployer.address || "—"}</p>
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <Button variant="outline" size="sm" onClick={handleUpdateContact}>
                <UserPlus className="ml-2 h-4 w-4" />
                עדכון איש קשר
              </Button>
              <Button variant="outline" size="sm" onClick={handleSendEmail}>
                <Mail className="ml-2 h-4 w-4" />
                שליחת מייל
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>פעילות אחרונה</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {extendedEmployer.recentActivity.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  אין פעילות אחרונה להצגה
                </div>
              )}
              {extendedEmployer.recentActivity.map(
                (
                  activity: { action: string; user: string; date: string },
                  index: number
                ) => (
                  <div
                    key={index}
                    className="flex justify-between items-center border-b pb-2 last:border-0"
                  >
                    <div className="flex items-center">
                      <div className="ml-3 bg-secondary p-2 rounded-full">
                        <FileText className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-muted-foreground">
                          בוצע ע"י: {activity.user}
                        </p>
                      </div>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {activity.date}
                    </span>
                  </div>
                )
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Dialog open={contactDialogOpen} onOpenChange={setContactDialogOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>עדכון פרטי קשר</DialogTitle>
            <DialogDescription>עדכן את פרטי איש הקשר למעסיק.</DialogDescription>
          </DialogHeader>
          <Form {...contactForm}>
            <form
              onSubmit={contactForm.handleSubmit(onSubmitContact)}
              className="space-y-4"
            >
              <FormField
                control={contactForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>שם</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={contactForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>טלפון</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={contactForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>אימייל</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <DialogFooter className="mt-2">
                <Button type="submit" size="sm">
                  <Send className="ml-2 h-4 w-4" />
                  שמירה
                </Button>
                <DialogClose asChild>
                  <Button type="button" variant="outline" size="sm">
                    ביטול
                  </Button>
                </DialogClose>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      <Dialog open={emailDialogOpen} onOpenChange={setEmailDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>שליחת מייל</DialogTitle>
            <DialogDescription>
              שליחת הודעה ל{extendedEmployer.contactPerson || "איש הקשר"}
            </DialogDescription>
          </DialogHeader>
          <Form {...emailForm}>
            <form
              onSubmit={emailForm.handleSubmit(onSubmitEmail)}
              className="space-y-4"
            >
              <FormField
                control={emailForm.control}
                name="subject"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>נושא</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={emailForm.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>תוכן ההודעה</FormLabel>
                    <FormControl>
                      <Textarea rows={4} {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <DialogFooter className="mt-2">
                <Button type="submit" size="sm">
                  <Send className="ml-2 h-4 w-4" />
                  שלח
                </Button>
                <DialogClose asChild>
                  <Button type="button" variant="outline" size="sm">
                    ביטול
                  </Button>
                </DialogClose>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
