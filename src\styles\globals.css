@import url('https://fonts.googleapis.com/css2?family=Heebo:wght@100..900&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
	--font-sans: 'Heeb<PERSON>', var(--font-geist-sans), ui-sans-serif, system-ui, sans-serif,
		"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

@theme inline {
	--radius-sm: calc(var(--radius) - 4px);
	--radius-md: calc(var(--radius) - 2px);
	--radius-lg: var(--radius);
	--radius-xl: calc(var(--radius) + 4px);
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-destructive: var(--destructive);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-chart-1: var(--chart-1);
	--color-chart-2: var(--chart-2);
	--color-chart-3: var(--chart-3);
	--color-chart-4: var(--chart-4);
	--color-chart-5: var(--chart-5);
	--color-sidebar: var(--sidebar);
	--color-sidebar-foreground: var(--sidebar-foreground);
	--color-sidebar-primary: var(--sidebar-primary);
	--color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
	--color-sidebar-accent: var(--sidebar-accent);
	--color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
	--color-sidebar-border: var(--sidebar-border);
	--color-sidebar-ring: var(--sidebar-ring);
	--color-spektr-cyan-50: var(--spektr-cyan-50);
	--color-spektr-cyan-100: var(--spektr-cyan-100);
	--color-spektr-cyan-200: var(--spektr-cyan-200);
	--color-spektr-cyan-300: var(--spektr-cyan-300);
	--color-spektr-cyan-400: var(--spektr-cyan-400);
	--color-spektr-cyan-500: var(--spektr-cyan-500);
	--color-spektr-cyan-600: var(--spektr-cyan-600);
	--color-spektr-cyan-700: var(--spektr-cyan-700);
	--color-spektr-cyan-800: var(--spektr-cyan-800);
	--color-spektr-cyan-900: var(--spektr-cyan-900);
	--color-success-50: var(--success-50);
	--color-success-100: var(--success-100);
	--color-success-200: var(--success-200);
	--color-success-300: var(--success-300);
	--color-success-400: var(--success-400);
	--color-success-500: var(--success-500);
	--color-success-600: var(--success-600);
	--color-success-700: var(--success-700);
	--color-success-800: var(--success-800);
	--color-success-900: var(--success-900);
	--color-navy-50: var(--navy-50);
	--color-navy-100: var(--navy-100);
	--color-navy-200: var(--navy-200);
	--color-navy-300: var(--navy-300);
	--color-navy-400: var(--navy-400);
	--color-navy-500: var(--navy-500);
	--color-navy-600: var(--navy-600);
	--color-navy-700: var(--navy-700);
	--color-navy-800: var(--navy-800);
	--color-navy-900: var(--navy-900);
	--color-gold-50: var(--gold-50);
	--color-gold-100: var(--gold-100);
	--color-gold-200: var(--gold-200);
	--color-gold-300: var(--gold-300);
	--color-gold-400: var(--gold-400);
	--color-gold-500: var(--gold-500);
	--color-gold-600: var(--gold-600);
	--color-gold-700: var(--gold-700);
	--color-gold-800: var(--gold-800);
	--color-gold-900: var(--gold-900);
	--color-warning-50: var(--warning-50);
	--color-warning-100: var(--warning-100);
	--color-warning-200: var(--warning-200);
	--color-warning-300: var(--warning-300);
	--color-warning-400: var(--warning-400);
	--color-warning-500: var(--warning-500);
	--color-warning-600: var(--warning-600);
	--color-warning-700: var(--warning-700);
	--color-warning-800: var(--warning-800);
	--color-warning-900: var(--warning-900);
}

:root {
	--radius: 0.625rem;
	--background: oklch(1 0 0);
	--foreground: oklch(0.145 0 0);
	--card: oklch(1 0 0);
	--card-foreground: oklch(0.145 0 0);
	--popover: oklch(1 0 0);
	--popover-foreground: oklch(0.145 0 0);
	--primary: oklch(0.205 0 0);
	--primary-foreground: oklch(0.985 0 0);
	--secondary: oklch(0.97 0 0);
	--secondary-foreground: oklch(0.205 0 0);
	--muted: oklch(0.97 0 0);
	--muted-foreground: oklch(0.556 0 0);
	--accent: oklch(0.97 0 0);
	--accent-foreground: oklch(0.205 0 0);
	--destructive: oklch(0.577 0.245 27.325);
	--border: oklch(0.922 0 0);
	--input: oklch(0.922 0 0);
	--ring: oklch(0.708 0 0);
	--chart-1: oklch(0.646 0.222 41.116);
	--chart-2: oklch(0.6 0.118 184.704);
	--chart-3: oklch(0.398 0.07 227.392);
	--chart-4: oklch(0.828 0.189 84.429);
	--chart-5: oklch(0.769 0.188 70.08);
	--sidebar: oklch(0.985 0 0);
	--sidebar-foreground: oklch(0.145 0 0);
	--sidebar-primary: oklch(0.205 0 0);
	--sidebar-primary-foreground: oklch(0.985 0 0);
	--sidebar-accent: oklch(0.97 0 0);
	--sidebar-accent-foreground: oklch(0.205 0 0);
	--sidebar-border: oklch(0.922 0 0);
	--sidebar-ring: oklch(0.708 0 0);
	
	/* Professional blue for financial theme */
	--spektr-cyan-50: #EFF6FF;
	--spektr-cyan-100: #DBEAFE;
	--spektr-cyan-200: #BFDBFE;
	--spektr-cyan-300: #93C5FD;
	--spektr-cyan-400: #60A5FA;
	--spektr-cyan-500: #3B82F6;
	--spektr-cyan-600: #2563EB;
	--spektr-cyan-700: #1D4ED8;
	--spektr-cyan-800: #1E40AF;
	--spektr-cyan-900: #1E3A8A;
	
	/* Money green */
	--success-50: #F0FDF4;
	--success-100: #DCFCE7;
	--success-200: #BBF7D0;
	--success-300: #86EFAC;
	--success-400: #4ADE80;
	--success-500: #22C55E;
	--success-600: #16A34A;
	--success-700: #15803D;
	--success-800: #166534;
	--success-900: #14532D;
	
	/* Professional navy */
	--navy-50: #f0f4f8;
	--navy-100: #d9e2eb;
	--navy-200: #b7c5d7;
	--navy-300: #8fa1bc;
	--navy-400: #667ea5;
	--navy-500: #4f6589;
	--navy-600: #3d4c6c;
	--navy-700: #354057;
	--navy-800: #22293b;
	--navy-900: #171c27;
	
	/* Gold accent */
	--gold-50: #fefce8;
	--gold-100: #fef9c3;
	--gold-200: #fef08a;
	--gold-300: #fde047;
	--gold-400: #facc15;
	--gold-500: #eab308;
	--gold-600: #ca8a04;
	--gold-700: #a16207;
	--gold-800: #854d0e;
	--gold-900: #713f12;
	
	/* Warning red */
	--warning-50: #FEF2F2;
	--warning-100: #FEE2E2;
	--warning-200: #FECACA;
	--warning-300: #FCA5A5;
	--warning-400: #F87171;
	--warning-500: #EF4444;
	--warning-600: #DC2626;
	--warning-700: #B91C1C;
	--warning-800: #991B1B;
	--warning-900: #7F1D1D;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
	}
  body {
    @apply bg-background text-foreground;
	}
}

/* RTL Support */
html[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

html[dir="rtl"] body {
  text-align: right;
}

html[dir="rtl"] input,
html[dir="rtl"] textarea {
  text-align: right;
  direction: rtl;
}

html[dir="rtl"] .form-control {
  text-align: right;
}

html[dir="rtl"] select {
  background-position: left 0.5rem center !important;
  padding-right: 0.75rem !important;
  padding-left: 2rem !important;
}

@layer utilities {
  .rtl-flip {
    transform: scaleX(-1);
  }
  
  .rtl-rotate-180 {
    transform: rotate(180deg);
  }
  
  /* RTL margin adjustments */
  .rtl-mr-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
  }
  
  .rtl-ml-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
  
  /* RTL positioning */
  .rtl-right-auto {
    right: auto !important;
  }
  
  .rtl-left-auto {
    left: auto !important;
  }
  
  /* Common RTL swaps */
  .rtl-swap-left-right {
    right: var(--left-value, initial);
    left: var(--right-value, initial);
  }
  
  /* Common RTL flex direction */
  .rtl-flex-row-reverse {
    flex-direction: row-reverse !important;
  }
  
  /* RTL icons */
  .rtl-icon-flip {
    transform: scaleX(-1);
  }
  
  /* RTL text alignment overrides */
  .rtl-force-text-right {
    text-align: right !important;
  }
}
