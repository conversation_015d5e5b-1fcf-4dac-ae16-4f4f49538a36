"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";
import { BarChart } from "lucide-react";
import { type EmployerTabProps } from "../types";

export function EmployerReportsTab({ employer }: EmployerTabProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>דוחות</CardTitle>
        <Button size="sm">
          <BarChart className="ml-2 h-4 w-4" />
          יצירת דוח
        </Button>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card className="border-2 shadow-none">
            <CardHeader className="p-4">
              <CardTitle className="text-base font-medium">מצבת עובדים</Card<PERSON><PERSON>le>
              <CardDescription>סיכום פרטים על עובדי החברה</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <Button variant="link" className="p-0 h-auto">הפקת דוח</Button>
            </CardContent>
          </Card>
          <Card className="border-2 shadow-none">
            <CardHeader className="p-4">
              <CardTitle className="text-base font-medium">תלושי שכר לתקופה</CardTitle>
              <CardDescription>סיכום תלושי שכר לפי חודשים</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <Button variant="link" className="p-0 h-auto">הפקת דוח</Button>
            </CardContent>
          </Card>
          <Card className="border-2 shadow-none">
            <CardHeader className="p-4">
              <CardTitle className="text-base font-medium">דוח 102</CardTitle>
              <CardDescription>ניכויים והפרשות למס הכנסה</CardDescription>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <Button variant="link" className="p-0 h-auto">הפקת דוח</Button>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
} 