"use client";

import { useSession } from "next-auth/react";
import { useDashboardStore } from "./hooks/useDashboardStore";

// Import the custom hooks for data fetching
import { useDashboardData, useUsers, useReports, useAuditLogs } from "./usage";
import { useEmployers } from "./hooks/useEmployer";

// Import component parts
import { DashboardHeader } from "./components/dashboard-header";
import { SystemAlerts } from "./components/system-alerts";
import { MetricsCards } from "./components/metrics-cards";
import { DashboardTabs } from "./components/dashboard-tabs";
import { QuickAccess } from "./components/quick-access";
import type { UserFormValues, User, Role } from "./components/UsersTable/types";
import { redirect } from "next/navigation";

export default function OwnerDashboard() {
  const { data: session, status: sessionStatus } = useSession();
  const {
    selectedPeriod,
    setSelectedPeriod,
    employersPage,
    setEmployersPage,
    usersPage,
    setUsersPage,
    auditLogsActionFilter,
    setAuditLogsActionFilter,
    auditLogsPage,
    setAuditLogsPage,
  } = useDashboardStore();

  const isSessionLoading = sessionStatus === "loading";

  // Use our custom hooks to fetch data
  const {
    metrics,
    alerts,
    isLoading: isDashboardLoading,
    refetch: refetchDashboard,
  } = useDashboardData(selectedPeriod);

  // Use our new employers hook
  const {
    employers,
    isLoading: isEmployersLoading,
    createEmployer,
    isCreating: isCreatingEmployer,
  } = useEmployers(employersPage);

  const {
    users: apiUsers,
    isLoading: isUsersLoading,
    createUser,
    isCreating: isCreatingUser,
  } = useUsers(usersPage);

  const {
    reports,
    isLoading: isReportsLoading,
    generateReport: generateReportMutation,
    isGenerating: isGeneratingReport,
  } = useReports();

  const { logs, isLoading: isLogsLoading } = useAuditLogs(
    auditLogsPage,
    auditLogsActionFilter
  );

  // Transform API users to match the expected User type with Role enum
  const users = apiUsers?.map(user => ({
    ...user,
    role: user.role as Role,
    status: user.status as "active" | "inactive"
  }));

  // Create a wrapper function to handle type conversion
  const generateReport = (data: { reportId: string }) => {
    // Convert string to the expected enum type
    const typedReportId = data.reportId as
      | "FORM_102"
      | "PAYROLL_SUMMARY"
      | "TAX_REPORT"
      | "EMPLOYEE_OVERVIEW";
    generateReportMutation({ reportId: typedReportId });
  };

  const refreshData = () => {
    refetchDashboard();
  };

  if (isSessionLoading) {
    return (
      <div className="flex flex-col w-full min-h-screen p-6 gap-6">
        <DashboardHeader refreshData={refreshData} isLoading={true} />
        <SystemAlerts isLoading={true} />
        <MetricsCards isLoading={true} />
        <DashboardTabs
          employers={[]}
          isEmployersLoading={true}
          users={[]}
          isUsersLoading={true}
          reports={[]}
          isReportsLoading={true}
          logs={[]}
          isLogsLoading={true}
          isLoading={true}
        />
        <QuickAccess isLoading={true} />
      </div>
    );
  }

  if (!session) {
    redirect("/login");
  }

  return (
    <div className="flex flex-col w-full min-h-screen p-6 gap-6">
      <DashboardHeader refreshData={refreshData} isLoading={isDashboardLoading} />

      <SystemAlerts alerts={alerts} isLoading={isDashboardLoading} />

      <MetricsCards metrics={metrics} isLoading={isDashboardLoading} />

      <DashboardTabs
        employers={employers}
        isEmployersLoading={isEmployersLoading}
        createEmployer={createEmployer}
        isCreatingEmployer={isCreatingEmployer}
        users={users}
        isUsersLoading={isUsersLoading}
        createUser={createUser as (data: UserFormValues) => Promise<void>}
        isCreatingUser={isCreatingUser}
        reports={reports}
        isReportsLoading={isReportsLoading}
        generateReport={generateReport}
        isGeneratingReport={isGeneratingReport}
        logs={logs}
        isLogsLoading={isLogsLoading}
      />

      <QuickAccess />
    </div>
  );
}
