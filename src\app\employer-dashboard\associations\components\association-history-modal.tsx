import React from "react";
import {
  Dialog,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { formatDateString } from "../hooks";
import type { Association, AssociationHistoryEntry } from "../hooks";

// Helper to translate association types to Hebrew
const associationTypeToHebrew = {
  DEPARTMENT: "מחלקה",
  ROLE: "תפקיד",
  SALARY_TEMPLATE: "תבנית שכר",
  SALARY_AGREEMENT: "הסכם שכר",
};

interface AssociationHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  association: Association;
  history: AssociationHistoryEntry[];
  isLoading: boolean;
}

export function AssociationHistoryModal({
  isO<PERSON>,
  onClose,
  association,
  history,
  isLoading,
}: AssociationHistoryModalProps) {
  // Helper function to get badge variant based on change type
  const getChangeTypeVariant = (changeType: string) => {
    switch (changeType) {
      case "CREATE":
        return "success";
      case "UPDATE":
        return "warning";
      case "DELETE":
        return "destructive";
      default:
        return "default";
    }
  };

  // Helper function to translate change types to Hebrew
  const changeTypeToHebrew = (changeType: string) => {
    switch (changeType) {
      case "CREATE":
        return "יצירה";
      case "UPDATE":
        return "עדכון";
      case "DELETE":
        return "מחיקה";
      default:
        return changeType;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>
            היסטוריית שינויים - {association.employeeName} ({associationTypeToHebrew[association.associationType]}: {association.associatedEntityName})
          </DialogTitle>
          <DialogDescription>
            צפה בכל השינויים שבוצעו בשיוך זה לאורך זמן
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-2">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-48" />
              </div>
            ))}
          </div>
        ) : history.length === 0 ? (
          <div className="text-center p-4">
            <p className="text-gray-500">לא נמצאו שינויים להצגה</p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>תאריך</TableHead>
                <TableHead>סוג שינוי</TableHead>
                <TableHead>ערך קודם</TableHead>
                <TableHead>ערך חדש</TableHead>
                <TableHead>בוצע על ידי</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {history.map((entry) => (
                <TableRow key={entry.id}>
                  <TableCell>{formatDateString(entry.changeDate)}</TableCell>
                  <TableCell>
                    <Badge variant={getChangeTypeVariant(entry.changeType) as any}>
                      {changeTypeToHebrew(entry.changeType)}
                    </Badge>
                  </TableCell>
                  <TableCell>{entry.oldValue || "-"}</TableCell>
                  <TableCell>{entry.newValue || "-"}</TableCell>
                  <TableCell>{entry.changedBy}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </DialogContent>
    </Dialog>
  );
} 