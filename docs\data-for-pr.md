# אפיון מערכת שכר וניהול עובדים זרים

המערכת מיועדת לטפל בשכר עובדים זרים ובניהול נתוני העסקתם, בהתבסס על קובץ אקסל קיים ותכנון סכמת Prisma. האפיון מתייחס למודל נתונים (ישויות הליבה), רכיבי שכר חשובים (Enums), טבלאות נוספות הנדרשות, יחסי גומלין בין הישויות, ולידציות (Validations) באמצעות Zod, הפרדת לוגיקה עסקית (עם tRPC) מהממשק, שילוב בשכבת ה־UI של ShadCN, ולבסוף דגשים רגולטוריים קריטיים (מיסוי, ביטוח לאומי, טפסים וכד'). throughout the specification, Excel fields and Prisma schema elements are compared to ensure a full operational system.

## ישויות ליבה – מודלים (Prisma Models)

להלן פירוט הישויות המרכזיות במערכת, כולל שדות עיקריים, טיפוסי נתונים, חוקים עסקיים וערכים מותרים. בכל מודל מצוינים התאמות בין השדות בגיליון האקסל לסכמת הנתונים המתוכננת ב-Prisma, תוך הדגשת פערים ושדות נוספים שנדרשים למערכת.

### Employee (עובד)

מודל **Employee** מייצג עובד יחיד (עובד זר או מקומי). המודל כולל פרטים אישיים, סטטוס העסקה, והגדרות המשפיעות על חישוב השכר. שדות עיקריים במודל:

* **id** – מפתח ראשי (UUID או מספר רץ).
* **firstName** (שם פרטי) – מחרוזת, שם העובד.
* **lastName** (שם משפחה) – מחרוזת, שם משפחה של העובד.
* **nationalId** – מחרוזת למספר זהות/דרכון. עבור עובד זר ייתכן מספר דרכון במקום ת"ז.
* **isForeign** (עובד זר?) – ערך בוליאני המציין אם העובד מוגדר כעובד זר. באקסל הופיע עמודה "עובד זר" (כן/לא).
* **country** (מדינה) – המדינה ממנה העובד, רלוונטי רק אם הוא זר (למשל לצורך טופס 101 ואשרות).
* **status** – סטטוס העסקה (Enum, ראו פירוט בהמשך: פעיל, לא פעיל, הסתיים וכו').
* **startDate** (תחילת עבודה) – תאריך תחילת העסקה.
* **endDate** – תאריך סיום עבודה (אם הסתיים).
* **sector** (ענף) – סוג הענף בו מועסק העובד, למשל "חקלאות", "בנייה", "סיעוד" וכו'. באקסל צוין "ענף" כנתון עבור עובד. ענף זה חשוב כי חלים עליו כללי שכר שונים (למשל חובת פיקדון בענפים מסוימים).
* **departmentId** (מחלקה) – הפניה לישות Department (מחלקה בארגון) אליה משתייך העובד. באקסל הופיעה עמודת "מחלקה". אם אין שימוש במחלקות, אפשר להשאיר ערך ריק.
* **agreementType** (סוג הסכם עובד) – Enum המתאר את סוג ההסכם: למשל "פרטני" (הסכם אישי), "קיבוצי" (הסכם ענפי/קיבוצי) או "תבנית" (מבוסס תבנית מודל). באקסל צוין "הסכם עובד: פרטני/תבנית/הסכם" כקטגוריה למיון. ערך זה עשוי להשפיע על תנאי השכר (כגון אם מוחלים תנאי צו הרחבה כלשהו).
* **isResidentForNI** (עובד חוץ לביטוח לאומי) – בוליאני המציין אם העובד **אינו** תושב ישראל לצורך ביטוח לאומי. עובדים זרים בעלי אשרה לרוב אינם תושבי ישראל בביטוח לאומי, ולכן זכאים לפטורים מסוימים (למשל לא משלמים ביטוח בריאות ממלכתי דרך ביטוח לאומי). באקסל הופיע שדה "עובד חוץ לביטוח לאומי". ערך זה ישמש לחישוב דמי הביטוח בהתאם (ראו פירוט רגולציה).
* **baseSalary** – שכר בסיס של העובד. ניתן לשמור כשכר חודשי קבוע (למשרות חודשיות) או לחשב משעה. עבור עובדים לפי שעה, ניתן לשמור **hourlyRate** (תעריף לשעה) ו/או **standardHoursPerMonth** (כמות שעות תקן בחודש) לחישוב שכר הבסיס. למשל, באקסל חושב שכר יומי כנגזרת של שעות ותקן יומי. במערכת ניתן לשמור את תעריף השעה (hourlyRate) וכמות שעות ביום/חודש לחישוב אוטומטי של עלות חודשית.
* **allowances** – הטבות או רכיבי שכר קבועים אחרים. למשל: החזר נסיעות חודשי, דיור, אחזקת רכב. באקסל לדוגמה היה רכיב "תעריף נסיעות" קבוע 250 ש"ח. ניתן לשמור רכיב נסיעות קבוע בפרופיל העובד או לחלופין לרשום אותו בכל חודש כ"תנועה" (ראו מודל SalaryTransaction).
* **bankAccount** – פרטי חשבון בנק של העובד לצורך העברת שכר. אפשר לנהל כשדות נפרדים (קוד בנק, סניף, מספר חשבון) או כמבנה מאוחד.
* **createdAt** / **updatedAt** – חותמות זמן למעקב.

בטבלה הבאה מושווים שדות עיקריים בין **טבלת העובד** שבאקסל לבין מודל Employee המתוכנן ב-Prisma, כולל ציון סוגי הנתונים ופערים:

#### השוואת שדות – Employee (אקסל לעומת Prisma)

| שדה באקסל                      | שדה במודל Employee (Prisma)             | סוג נתונים    | הערות ותוספות                                                                                                                                                    |
| ------------------------------ | --------------------------------------- | ------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| שם (פרטי)                      | firstName                               | String        | שם פרטי של העובד (אותיות). חובה להזין.                                                                                                                           |
| משפחה (שם משפחה)               | lastName                                | String        | שם משפחה של העובד. חובה.                                                                                                                                         |
| עובד זר (כן/לא)                | isForeign                               | Boolean       | **Yes/No** אם העובד מוגדר כזר. משפיע על חישובי מס וביטוח.                                                                                                        |
| מדינה                          | country                                 | String        | ארץ מוצא העובד (למשל "תאילנד"). אופציונלי; נדרש אם isForeign=true.                                                                                               |
| ענף                            | sector                                  | Enum/String   | ענף תעסוקה (חקלאות/בנייה/סיעוד וכו'). Enum מותאם לערכי ענף מותרים.                                                                                               |
| מחלקה                          | departmentId                            | FK (מחלקה)    | שיוך למחלקה ארגונית. באקסל צוין כשם מחלקה; במערכת מיוצג כקישור לישות Department.                                                                                 |
| רווק (כן/לא)                   | maritalStatus (בטופס 101)               | Enum          | מצב משפחתי – ערך זה מנוהל בטופס 101 (Single/Married וכו'). באקסל סומן "רווק – כן", כלומר העובד רווק. במודל נשמר בישות Form101 (ראו בהמשך) ולא ישירות ב-Employee. |
| תחילת עבודה                    | startDate                               | Date          | תאריך התחלת ההעסקה. חובה.                                                                                                                                        |
| סיום עבודה                     | endDate                                 | Date          | תאריך סיום (אם הסתיים). שדה נוסף הנדרש במערכת (לא הופיע באקסל).                                                                                                  |
| עובד חוץ לביטוח לאומי          | isResidentForNI                         | Boolean       | האם אינו תושב לעניין ביטוח לאומי. משפיע על חישוב דמי ביטוח בריאות.                                                                                               |
| הסכם עובד (פרטני/תבנית/קיבוצי) | agreementType                           | Enum          | סוג ההסכם/מודל העסקה (Personal/Template/Collective). באקסל סומן כסוג הסכם.                                                                                       |
| שכר בסיס/תעריף שעה             | baseSalary / hourlyRate                 | Decimal       | שכר הבסיס החודשי או תעריף השעה. באקסל חושב מתוך שעות וערך שעה; במערכת נשמר כשדה מפורש.                                                                           |
| תעריף נסיעות                   | travelAllowance                         | Decimal       | החזר נסיעות חודשי קבוע. הופיע באקסל כ-250₪. אופציונלי – ניתן גם כ"תנועת שכר" במקום שדה קבוע.                                                                     |
| חשבון בנק להעברה               | bankAccount (bankCode, branch, account) | String/Struct | פרטי חשבון בנק של העובד. באקסל סומן "חשבון בנק העברה שכר". במערכת מפורק לשדות מזהים.                                                                             |
| סטטוס עובד                     | status                                  | Enum          | לא הופיע ישירות באקסל. ערכים: פעיל, לא פעיל, פוטר, התפטר וכו'. נדרש לניהול מחזור חיי עובד.                                                                       |
| מזהה עובד 101                  | (אין באקסל, חדש)                        | Form101Id     | קישור לרשומת טופס 101 נוכחית של העובד. באקסל הנתונים האישיים (רווק וכו') היו חלקית בטבלת עובד; במערכת נשמר בנפרד בטופס 101.                                      |

**חוקים עסקיים וולידציות לדוגמה**:

* יש לוודא ייחודיות של nationalId לכל עובד (מניעת כפילויות).
* `startDate` חייב להיות לפני `endDate`.
* עובד עם isForeign=true חייב לכלול שדות country ו-isResidentForNI (ישירות או נגזר מהגדרות כלליות).
* ערך status מוגבל לסטטוסים המוגדרים (ראה Enum סטטוס עובד).
* אם עובד מוגדר ב-sector שבו נדרש פיקדון (למשל בנייה, סיעוד – ראו רגולציה), המערכת תוודא שקיימת הגדרת קופת פיקדון (ProvidentFundContribution) מתאימה עבורו.

### Employer (מעסיק)

מודל **Employer** מייצג מעסיק (חברה/חקלאי/ארגון שמעסיק את העובדים). המערכת תומכת בריבוי מעסיקים (multi-tenant) ולכן כל עובד משויך למעסיק. שדות עיקריים במודל:

* **id** – מפתח המעסיק.
* **name** – שם הארגון/המעסיק (מחרוזת).
* **companyId** – מספר ח.פ./ע.מ של המעסיק (לצרכי דיווח).
* **industry** – סוג הענף העיקרי של המעסיק (אם כל העובדים תחת ענף זה; ניתן גם לגזור מענף העובדים).
* **address** – כתובת המעסיק (לצורכי טפסים).
* **contactPerson** – איש קשר אחראי.
* **users** – רשימת משתמשים (אם יש הפרדת חשבונות למעסיק במערכת).

קשר: מעסיק יכול להעסיק עובדים רבים (יחס אחד-לרבים: Employer -> Employees). כל Employee כולל שדה זר employerId.

> **הערה:** באקסל לא הייתה טבלת "Employer" כי ההתמקדות הייתה בעובד. עם זאת, בסכמת Prisma סופקה ישות Employer, כנראה לתמיכה בריבוי מעסיקים או הבחנה בין ישויות משפטיות. לפיכך, זהו מודל שאין לו מקבילה בטבלאות האקסל ויש להתבסס על הדרישות הידועות לניהול פרטי מעסיק.

חוקים עסקיים:

* אין למחוק מעסיק אם מקושרים אליו עובדים או נתוני שכר.
* שדות companyId וכד' צריכים לעבור ולידציה (מספר מזהה תואם פורמט ח.פ./ע.מ).

### Department (מחלקה) – **אובייקט משלים**

ניתן לנהל ישות **Department** לתיאור מחלקות/יחידות בתוך ארגון, במיוחד אם רוצים לדווח על עלויות לפי מחלקה. באקסל הופיע "מחלקה" עבור תנועות ולעובד, המצביע על אפשרות לשייך רכיבי שכר למחלקות. המודל יכלול:

* id,
* name (שם מחלקה),
* employerId (שיוך למעסיק).

קשר: Employer -> Departments (1\:N), Department -> Employees (1\:N) וגם ייתכן Department -> SalaryTransaction (ראו בהמשך) אם תנועות שכר ספציפיות משויכות למחלקה.

### Form101 (טופס 101 – כרטיס עובד)

מודל **Form101** מאחסן את פרטי כרטיס העובד לצורכי מס כפי שמולאו בטופס 101. עובדים ממלאים טופס 101 בתחילת עבודה וכל שנה, והנתונים בו משפיעים על חישוב ניכוי המס במקור. המודל כולל:

* **id** – מפתח.
* **employeeId** – קישור לעובד שאליו שייך הטופס (Relation: Employee 1 -> N Form101, בד"כ אחד פעיל לשנה).
* **year** – שנת המס שאליה מתייחס טופס 101 (למשל 2025). בדרך כלל נדרש טופס חדש לכל שנה.
* **maritalStatus** – מצב משפחתי (רווק/נשוי/גרוש/אלמן). לדוגמה, ערך "רווק" כפי שראינו באקסל.
* **spouseWorks** – האם בן/בת זוג עובד/ת (checkbox בטופס 101).
* **childrenCount** – מספר ילדים (לצורך נקודות זיכוי). לעיתים מפרטים מספר ילדים עד גיל מסוים בנפרד – ניתן להרחיב בפיצול שדות אם צריך.
* **additionalCreditPoints** – נקודות זיכוי נוספות שמגיעות (למשל עולה חדש, תושב יישוב ספר, הורה יחידני וכו'). ייתכן לשמור כשדה כולל או פירוט נפרד.
* **isMainEmployer** – האם המעסיק הנוכחי הוא "השכר העיקרי" של העובד (בטופס 101 סעיף לגבי הכנסה נוספת).
* **hasAdditionalIncome** – האם יש לעובד הכנסות נוספות (מעסיק נוסף או פנסיה).
* **exemptionPercentage** – אחוזי פטור ממס (אם יש, למשל נכות רפואית נותנת פטור עד תקרה).
* **signedAt** – תאריך חתימת הטופס (מועד מילוי).
* **validUntil** – תוקף הטופס (בדרך כלל 31/12 של השנה הנתונה).

הנתונים הללו משמשים לחישוב **נקודות הזיכוי** למס הכנסה של העובד. למשל, עובד זר רגיל יקבל נקודות בסיס (ייתכן 2.25 או 1.25 בהתאם לתושבות); פליטים/מבקשי מקלט אינם זכאים לנקודות זיכוי. המערכת תחושב את נקודות הזיכוי על בסיס השדות הללו.

קשר: Form101 שייך לעובד. ניתן לשמור היסטוריה – טופס חדש לכל שנה או בעת שינוי במצב אישי במהלך השנה.

**חוקים עסקיים**:

* חייב להיות Form101 תקף לכל עובד פעיל (אחרת – התראה לפני הפקת תלוש).
* ולידציה על שדות: מספר ילדים לא שלילי, אחוז פטור בין 0 ל-100, etc.
* אם hasAdditionalIncome=true ו-isMainEmployer=false, המשמעות שיש ליישם ניכוי מס מרבי (כמו עובד משני ללא נקודות זיכוי).

### Payslip (תלוש שכר)

מודל **Payslip** מייצג תלוש שכר חודשי לעובד. זהו המאסטר שמקבץ את כל רכיבי השכר והניכויים עבור עובד בחודש מסוים. שדות עיקריים:

* **id** – מפתח.
* **employeeId** – העובד אליו שייך התלוש (Relation: Employee 1 -> N Payslips).
* **periodMonth** – חודש התלוש (1-12).
* **periodYear** – שנה (למשל 2025). יחד עם חודש מהווה מזהה תקופה.
* **status** – סטטוס עיבוד התלוש (Draft/Calculated/Approved/Paid וכו', ניתן כEnum). למשל בתחילה "Draft" עד לחישוב, אז "Calculated/Approved", לאחר תשלום "Paid".
* **grossPay** – סך כל הברוטו לתשלום באותו חודש (מחושב).
* **netPay** – סך נטו לתשלום (מחושב: ברוטו + שווי – ניכויים).
* **incomeTax** – סך ניכוי מס הכנסה בחודש זה.
* **nationalInsurance** – סך ניכוי ביטוח לאומי (עובד) בחודש זה.
* **healthInsurance** – סך ניכוי ביטוח בריאות (אם רלוונטי) – אצל עובד זר זה לרוב חלק מביטוח לאומי או ניכוי נפרד בהתאם לתושבות.
* **pensionEmployee** – סך ניכוי עובד לפנסיה/פיקדון (אם יש).
* **pensionEmployer** – חלק מעסיק לפנסיה (אם יש, לצורכי דיווח 102). אם הפיקדון החליף פנסיה – השדה הזה עשוי להיות 0, וההפקדה משוקפת ב-ProvidentFundContribution.
* **severancePay** – חלק פיצויים שהופרש (מעסיק) – גם כאן, אם יש פיקדון, הוא למעשה מחושב שם.
* **otherDeductions** – סך יתר הניכויים (רשות) שנוכו (למשל ניכוי עבור דיור, ביטוח רפואי פרטי וכו').
* **netDeposit** – סכום פיקדון חודשי שהופרש עבור עובד זר (לא מתוך השכר, ע"ח מעסיק). אפשר גם לא לשמור כאן אלא להסתמך על ProvidentFundContribution.
* **createdAt** – תאריך הפקת התלוש/חישוב.

רוב השדות הכספיים ניתנים לחישוב מסכימת הרכיבים (SalaryRecords) הקשורים לתלוש, אך נכללים פה לנוחות ושאילתות. למשל, עבור דוח טופס 126 שנתי נרצה לשלוף ישירות משדות התלוש המצטברים.

קשרים: לכל Payslip יש **רשומות שכר** (SalaryRecords) מרובות – פירוט התשלומים והניכויים (Relation: Payslip 1 -> N SalaryRecords). בנוסף, לכל Payslip יכולים להיות רשומות הפקדה סוציאליות (ProvidentFundContributions) – למשל הפקדה לפיקדון עבור אותו חודש.

**חוקים עסקיים**:

* רק תלוש אחד לכל עובד לכל חודש-שנה (ייחודיות על employeeId+period).
* תלוש לא ישולם (סטטוס Paid) לפני שאושר סופית ונבדק.
* בשמירת תלוש במצב סופי, ודא שנוצרו הרשומות הנלוות (ניכויי חובה, הפקדות וכו').

### SalaryRecord (רכיב שכר בפועל)

מודל **SalaryRecord** מייצג שורה בתלוש השכר – רכיב שכר בודד, שהוא או תשלום (הכנסה), ניכוי, או פריט אינפורמטיבי. כל SalaryRecord משויך לתלוש ספציפי. שדות:

* **id** – מפתח.
* **payslipId** – קישור לתלוש אב (Relation: Payslip 1 -> N SalaryRecords).
* **componentCode** – קוד רכיב השכר (Enum או קוד מספרי בהתאם לטבלת הרכיבים). למשל 100 = שעות רגילות, 125 = שעות נוספות 125%, 400 = מקדמה (ניכוי) וכו'. רצוי להשתמש ב-Enum או לפחות רשימת קודים ידועה (ראו בהמשך Enum רכיבי שכר).
* **description** – תיאור הרכיב. ניתן לגזור אוטומטית לפי הקוד (ע"י join לטבלת רכיבי שכר או מפה במערכת), אך אפשר גם לשמור לצורך היסטורי.
* **quantity** – כמות יחידות לרכיב, אם רלוונטי. למשל מספר שעות, מספר ימים. בקוד 100 ("שעות רגילות") זו כמות השעות לתשלום; בקוד 121 ("ימי עבודה") זו כמות ימי העבודה המדווחים; לרכיב "מפרעה" (400) או סכומי כסף קבועים ייתכן שאין כמות (יכולה להיות 1).
* **rate** – תעריף יחידה. למשל  כמות 10 שעות \* תעריף 34.3 ₪. בחישובי שעות נוספות, התעריף עשוי להיות מוגדל (או ששומרים אחוז בנפרד). ניתן לשמור את התעריף בפועל ששימש לחישוב.
* **percentage** – אחוז, במקרה של רכיב שהוא אחוז מתעריף יסוד. לדוגמה שעות נוספות 125%: האחוז הוא 125%. ניתן לשמור אחוז זה (או לגזור מהקוד).
* **amount** – סכום כספי של הרכיב לאותו תלוש. עבור תשלום – הסכום לתוספת לברוטו; עבור ניכוי – סכום להפחתה מהנטו; עבור רכיב אינפורמטיבי – סכום שמוצג בלבד (ללא חישוב).
* **isDebit** – בוליאני/Enum המציין אם זה רכיב מסוג זיכוי (תשלום) או חיוב (ניכוי). אפשר לגזור מסוג הרכיב, אך להבהרה ניתן לכלול.
* **isInfo** – בוליאני אם הרכיב הוא אינפורמטיבי בלבד (לא משפיע על נטו). למשל "סה"כ שעות" או "ניצול חופש" מסומנים כרכיבים אינפורמטיביים.
* **departmentId** – במידה והרכיב מיוחס למחלקה שונה מזו של העובד (לצרכי ניתוח עלות), ניתן לשמור שיוך למחלקה. באקסל הופיע "מחלקה בתנועה" בתנועות לעובד, המאפשר שיוך רכיב ספציפי למחלקה אחרת. שדה אופציונלי.
* **note** – הערה/תיעוד ידני אם יש צורך.

הרשומות נוצרות כתוצאה מחישוב השכר: חלקן באופן קבוע (לדוגמה שכר בסיס, נסיעות קבועות), חלקן מתנועות שהוזנו (כגון שעות נוספות, היעדרויות), וחלקן נגזרות (ניכויי חובה, ניצול חופש וכד').

**הערה לגבי קלט מול פלט:** ייתכן שמודל SalaryRecord משמש גם להזנת התנועות הקבועות/זמניות (קלט) וגם לתוצאה (פלט). לחלופין, ניתן להגדיר מודל נפרד לתנועות קלט (ראו SalaryTransaction בהמשך). כדי לשמור על הפרדה ברורה, נציע מודל קלט נפרד, אך גם המודל היחיד אפשרי אם מוסיפים שדה ציון מקור (input/manual vs calculated).

#### SalaryTransaction (תנועת שכר לעובד) – *מודל קלט מוצע*

כדי לייצג את הנתונים שמוזנים לפני חישוב (כגון שעות עבודה, היעדרויות, שעות נוספות, מקדמות וכו'), נגדיר מודל **SalaryTransaction** נפרד. מודל זה תואם למסך "תנועות לעובד" באקסל, ומאפשר להזין פריטים לפני הרצת החישוב, ולאחסן אותם להיסטוריה:

* **id** – מפתח.
* **employeeId** – העובד שאליו שייכת התנועה.
* **periodMonth**, **periodYear** – לאיזה חודש מתייחס הנתון (כדי שנדע לאיזה תלוש לשייך).
* **componentCode** – קוד רכיב שכר (בדומה ל-SalaryRecord). למשל קוד 125 עבור דיווח שעות נוספות 125%.
* **quantity**, **rate**, **percentage**, **amount** – שדות כמות/תעריף/אחוז/סכום כפי שהוזנו. בממשק יכולים להזין למשל "5 שעות נוספות 125%" ואז quantity=5, componentCode=125, וייתכן rate ימולא אוטומטית מתעריף העובד, percentage=1.25 (125%).
* **fromDate**, **toDate** – טווח תאריכים אם התנועה מתייחסת לפרק זמן (למשל היעדרות מ-10 עד 12 בחודש). באקסל בעמודת תנועות היו "מתאריך" ו"עד תאריך". לרוב בשכר חודשי ניתן להשאיר ריק (כל החודש).
* **departmentId** – שיוך למחלקה (אם התנועה קשורה למחלקה ספציפית).
* **source** – מקור התנועה (ידני, ייבוא מערכת שעון, וכד'). באקסל הייתה עמודת "מקור".
* **userId** – מי הזין את התנועה (אם רלוונטי, למעקב שינויים). באקסל "יוזר".
* **createdAt** – תאריך יצירת התנועה. באקסל "תאריך".
* **note** – הערה.
* **isProcessed** – בוליאני אם התנועה כבר עובדה לתלוש (כדי למנוע כפל).

לאחר הרצת חישוב השכר לחודש, המערכת תייצר אוטומטית את ה-SalaryRecords מתוך ה-Transactions שטרם עובדו (ותסמן אותן כמטופלות).

בטבלה הבאה מוצג מיפוי בין עמודות גליון "תנועות לעובד" באקסל לבין השדות במודל SalaryTransaction המוצע:

#### השוואת שדות – תנועות לעובד (Excel) מול SalaryTransaction (Prisma)

| עמודה במסך תנועות לעובד (Excel) | שדה ב-SalaryTransaction | סוג         | הערות                                                                                   |
| ------------------------------- | ----------------------- | ----------- | --------------------------------------------------------------------------------------- |
| מס' רכיב שכר (קוד)              | componentCode           | Enum/Int    | קוד רכיב שכר מדווח.                                                                     |
| שם רכיב שכר                     | (נגזר מ-componentCode)  | String      | התיאור מופיע באקסל לשם הנוחות, במערכת ניתן לגזור לפי הקוד.                              |
| סוג סמל                         | (לא נדרש ישירות)        | -           | סוג הרכיב (תשלום/ניכוי/מידע) נגזר מהקוד, לא צריך לשמור בכל תנועה.                       |
| מחלקה בתנועה                    | departmentId            | FK          | שיוך למחלקה אם צוין.                                                                    |
| כמות                            | quantity                | Decimal     | כמות יחידות (שעות/ימים).                                                                |
| תעריף                           | rate                    | Decimal     | תעריף יחידה, אם הוזן ידנית (אחרת יחושב אוט').                                           |
| אחוז                            | percentage              | Decimal     | אחוז לתעריף יסוד (למשל 1.25 עבור 125%). נגזר מהקוד אך ניתן לשינוי במקרים מסוימים.       |
| סכום                            | amount                  | Decimal     | סכום כספי ישיר (אם מזינים סכום במקום כמות\*תעריף).                                      |
| מתאריך                          | fromDate                | Date        | תאריך התחלה לתנועה (אם נדרש).                                                           |
| עד תאריך                        | toDate                  | Date        | תאריך סיום לתנועה.                                                                      |
| גילום                           | isGrossedUp             | Boolean     | "גילום" – האם בוצע גילום מס על הסכום. באקסל הופיע בעמודת "גילום". במערכת נייצג בבוליאן. |
| מקור                            | source                  | String/Enum | מקור הנתון (ידני/מערכת חיצונית וכו').                                                   |
| יוזר                            | userId                  | FK (User)   | מזהה המשתמש שהזין.                                                                      |
| תאריך (קליטת התנועה)            | createdAt               | DateTime    | תאריך ושעת הזנת התנועה.                                                                 |

**חוקים עסקיים**:

* לא לאפשר הזנת שני רכיבים זהים לאותו עובד ואותה תקופה ללא ייחוד (אלא אם מאפשרים בכוונה פיצול, במקרה כזה המערכת תצבור אותם).
* ולידציה שתאריך בטווח \[fromDate, toDate] נופל בתוך חודש הדיווח (אם חורג, ייתכן לפצל אוטומטית בין חודשים).
* אם צוין גילום (isGrossedUp=true), המערכת תחייב לחשב את המס הגלום ולהוסיף רכיב "גילום שווי" מתאים (קוד כמו 'gilum' או אחר, כפי שבאקסל).

### ProvidentFundContribution (הפקדה לקופת גמל/פיקדון)

מודל **ProvidentFundContribution** מייצג הפקדה סוציאלית חודשית עבור עובד – בין אם לקרן פנסיה רגילה, קרן השתלמות, או פיקדון לעובדים זרים. כיוון שלעובדים זרים בענפים מסוימים יש **פיקדון חובה** במקום קרן פנסיה, מודל זה תוכנן לגמישות לשני הסוגים. שדות:

* **id** – מפתח.
* **employeeId** – העובד שאליו שייכת ההפקדה. (אפשר גם לקשר דרך payslipId אך לעיתים הפקדה נעשית גם ללא תלוש, למשל השלמה).
* **payslipId** – קישור לתלוש הרלוונטי (אם ההפקדה קשורה לחודש שכר ספציפי).
* **type** – סוג ההפקדה (Enum מסוג ProvidentFundType, ראו בהמשך): למשל "DEPOSIT" לפיקדון עובד זר, "PENSION" לקרן פנסיה, "COMPENSATION" לקרן פיצויים נפרדת, "HISHTALMUT" לקרן השתלמות, "UNION" לדמי ועד עובדים וכד'.
* **employeeContribution** – סכום שנוכה משכר העובד לקרן (אם רלוונטי). למשל בפנסיה רגילה – 6% משכר. בפיקדון בענפים – לרוב עובד זר **לא** מפריש משכרו לפנסיה (נאסר לנכות לפיקדון), ולכן שדה זה יהיה 0 עבור פיקדון.
* **employerContribution** – סכום שהמעסיק הפריש (תגמולים מעסיק). למשל בפנסיה: \~6.5% משכר; בפיקדון: מרכיב תגמולי מעסיק לפיקדון (5.5% לפי הדוגמה באקסל).
* **compensationContribution** – סכום שהופרש עבור פיצויים. בפנסיה רגילה: 8.33% מהשכר (אם לא חלק מהשכר); בפיקדון: מרכיב הפיצויים של הפיקדון (8.33% לפי האקסל).
* **totalContribution** – סך הכל ההפקדה (חישוב: סכום מעסיק + פיצויים + סכום עובד אם יש). ניתן לשמור לנוחות.
* **capApplied** – האם הוגבלה בתקרה. באקסל נראו תקרות למשל 6248 ש"ח שכר ברוטו לחישוב מקסימום הפיקדון, ו-770 ש"ח הפקדה מקסימלית. ניתן לסמן אם הייתה מגבלת תקרה.
* **fundName** – שם הקופה/גורם מנהל (אופציונלי, למשל שם קרן הפנסיה או ציון "פיקדון רשות ההגירה"). אפשר גם להחזיק טבלת סוגי קופות בנפרד.
* **periodMonth**, **periodYear** – חודש ושנה של ההפקדה (כדי לאפשר רישום הפקדה חריגה מחוץ למחזור שכר רגיל, למשל פיקדון עבור חודש ללא תלוש כי העובד לא קיבל שכר בפועל, או הפרשה לפנסיה אחרי סיום העסקה). אם משויכת ל-payslipId ניתן לגזור, אך נכלל לגמישות.

קשרים: Employee 1 -> N ProvidentFundContributions. כל הפקדה יכולה להיות קשורה גם לPayslip. ייתכן שלכל Payslip יהיו 0,1 או כמה רשומות Contributions (למשל גם פנסיה רגילה וגם קרן השתלמות, אם כי עבור עובד זר סביר אחת – הפיקדון).

**לדוגמה**: עבור עובד בענף תעשייה זר, חודש ינואר 2025, עם שכר 5,000₪: הפיקדון יחושב כ-5.5%+8.33% = 13.83% מהשכר (≈691.5₪). אם תקרת ההפקדה היא 770₪, הסכום 691.5 מתחת לתקרה, והוא יוזן כ-employerContribution=275₪, compensationContribution=416.5₪, employeeContribution=0, type="DEPOSIT". אם השכר עלה מעל 6248₪, המערכת תגביל לפי 770₪ מקסימום.

שדות הדוגמה באקסל "קופות גמל" כללו "אחוז עובד", "אחוז מעביד", "אחוז פיצויים", ותקרות. מערכים אלה נגזרו הערכים שיוזנו בכל חודש בהתאם לשכר. במערכת, ייתכן צורך בטבלת הגדרות לאחוזים (ראו בהמשך) אך את התוצאה החודשית נשמור במודל זה.

**חוקים עסקיים**:

* חישוב אוטומטי: בעת חישוב תלוש, אם העובד זכאי לפנסיה/פיקדון, לחשב את הסכומים לפי אחוזי חובה ויצירת ProvidentFundContribution.
* איסור ניכוי חלק עובד לפיקדון: לוודא שב-type=DEPOSIT תמיד employeeContribution=0 (ע"פ חוק המעסיק לא רשאי לנכות מהעובד עבור הפיקדון).
* תקרות: ליישם תקרה על השכר המבוטח. למשל אם יש תקרת שכר חודשי לפנסיה (נניח לפי צו ההרחבה) או תקרת הפקדת פיקדון (לפי התקנות, באקסל 770₪ לחודש), לבצע min().
* הפרשות רק החל מותק מסוים: למשל בפנסיה, עובד זר זכאי לפנסיה אחרי 6 חודשי עבודה (אם לא היה מבוטח לפני). במערכת ניתן להוסיף לוגיקה: לא ליצור תרומה לפני תאריך זכאות. בפיקדון, חובה מהחודש הראשון.

### Alert (התראה)

מודל **Alert** מיועד לניהול התראות וטריגרים חשובים הקשורים לעובדים או לתהליכים, כדי שצוות השכר יטפל בהם. דוגמאות להתראות: תוקף אשרת עובד זר עומד לפוג, עובד ללא טופס 101 תקף, חריגה בשעות העבודה, וכדומה. שדות:

* **id** – מפתח ההתראה.
* **employeeId** – קישור לעובד קשור (אם ההתראה נוגעת לעובד ספציפי; יכול להיות null להתראה כללית).
* **type** – סוג ההתראה (Enum של סוגי התראות, ראו בהמשך). למשל: `VISA_EXPIRATION`, `MISSING_FORM101`, `EXCESS_OVERTIME`, `UNUSUAL_DEDUCTION` וכו'.
* **message** – טקסט ההודעה/תיאור ההתראה. למשל: "אשרת העבודה של עובד X תפוג בעוד 30 יום", "טופס 101 לשנת 2025 טרם הוגש".
* **date** – תאריך יצירה של ההתראה.
* **dueDate** – תאריך יעד לפעולה (אם רלוונטי). למשל, תאריך פקיעת האשרה בפועל.
* **severity** – רמת חומרה (נמוכה/בינונית/גבוהה).
* **isResolved** – בוליאני האם טופל.

קשרים: Alert יכול להיות קשור ל-Employee (לרוב) או ללא (כללי). מערכת ההתראות תאפשר לצפות בכל ההתראות הפתוחות ולסמן כטופלו.

**חוקים עסקיים ודוגמאות**:

* יצירת התראת `MISSING_FORM101` אוטומטית לכל עובד שאין לו Form101 בתוקף בתחילת שנה חדשה.
* יצירת התראת `VISA_EXPIRATION` 60 יום לפני שפג תוקף ויזת עובד (דורש לשמור גם שדות תאריך ויזה/אישור עבודה ב-Employee – שדה שלא הוגדר לעיל, אך ניתן להוספה).
* התראת `EXCESS_OVERTIME` אם מוזנות שעות נוספות מעל X (למשל בניגוד לחוק שעות עבודה ומנוחה).
* התראת `DEPOSIT_PAYMENT_MISSING` אם עובד זר בענף חייב פיקדון ואין רשומת ProvidentFundContribution לחודש שכר מסוים.

מימוש ההתראות דורש מנגנון בדיקות תקופתי (cron job או בדיקה בעת חישוב תלושים).

## Enumים חיוניים במערכת

בחלק זה מפורטים סוגי Enum (ערכים קבועים) מרכזיים, כולל ערכי דוגמה. חלק מה-Enums נגזרים מטבלאות באקסל ("אקסולוגיה" לרכיבי שכר, טבלאות סטטוס וכו').

### Enum: סוגי רכיבי שכר (SalaryComponentCode)

רכיבי השכר מוגדרים עם קודים מספריים ותכונות שונות, כפי שנראה בטבלת "אקסולוגיה" באקסל. במערכת נגדיר Enum או מבנה נתונים לרכיבי השכר. להלן דוגמאות מרכזיות (קוד – תיאור – סיווג רכיב):

| קוד רכיב  | תיאור הרכיב         | סיווג רכיב                          |
| --------- | ------------------- | ----------------------------------- |
| **100**   | שעות רגילות         | תשלום (שכר יסוד)                    |
| **125**   | שעות נוספות 125%    | תשלום                               |
| **150**   | שעות נוספות 150%    | תשלום                               |
| **200**   | שעות נוספות 200%    | תשלום                               |
| **250**   | פרמיה (תגמול מיוחד) | תשלום                               |
| **113**   | דמי הבראה           | תשלום (תשלום הבראה)                 |
| **102**   | החזר נסיעות         | תשלום (החזר הוצאות)                 |
| **120**   | אחזקת רכב           | תשלום (הטבת רכב)                    |
| **131**   | שווי רכב            | תשלום (שווי שימוש ברכב, חייב מס)    |
| **gilum** | גילום שווי          | תשלום (גילום מס על הטבה)            |
| **170**   | מענק שנתי           | תשלום (בונוס שנתי)                  |
| **312**   | חופש                | תשלום (תמורת חופשה/תשלום ימי חופשה) |
| **313**   | מחלה                | תשלום (תמורת ימי מחלה)              |
| **114**   | פדיון חופשה         | תשלום (פדיון יתרת חופשה)            |
| **191**   | ניצול חופשה         | מידע (מס' ימי חופש שנוצלו)          |
| **192**   | ניצול הבראה         | מידע (אם ניצל זכות הבראה)           |
| **193**   | ניצול מחלה          | מידע (מס' ימי מחלה שנוצלו)          |
| **121**   | ימי עבודה           | מידע (מס' ימי עבודה בחודש)          |
| **900**   | תעריף שעה           | מידע (תעריף שכר שעתי למצגת)         |
| **400**   | מקדמה (מפרעה)       | ניכוי (החזר מקדמה)                  |
| **500**   | סך שעות             | מידע (סה"כ שעות לתקופה)             |

הרשימה לעיל היא חלקית לצרכי הדגמה אך מכסה את הרכיבים המרכזיים שהופיעו בקובץ האקסל. בפועל ייתכנו רכיבים נוספים (לדוגמה: ניכויי מס הכנסה, ביטוח לאומי – שאולי לא הופיעו באקסל כרכיב כי הם מחושבים אוטומטית, אך במערכת יוצגו בתלוש). ניתן לנהל את הרשימה כטבלת reference ב-DB במקום Enum קשיח, כדי לאפשר עדכון אחוזים/מאפיינים בלי שינוי קוד.

**תכונות נוספות לרכיב שכר** (מעבר לסוג) – אלו לא בדיוק Enum אלא שדות הגדרה: למשל האם חייב מס, חייב ביטוח לאומי, נכלל לחישוב פנסיה, נכלל לחישוב חופשה/מחלה, סוג יחידה (שעות/ימים/חודשי) ועוד כפי שראינו באקסל. במערכת ניתן ליישם אותם כמאפיינים של אובייקט הרכיב. לדוגמה, רכיב 100 "שעות רגילות": סוג=תשלום, חייב מס = כן, חייב ביטוח לאומי = כן, נספר לפנסיה = כן, חישוב לפי שעות; רכיב 191 "ניצול חופש": סוג=מידע, לא כספי, רק מציג ניצול ימי חופש. הגדרות אלו יאוכסנו בטבלת הרכיבים או במבנה קבוע.

### Enum: סטטוס עובד (EmployeeStatus)

סטטוסים אפשריים לעובד:

* **Active** (פעיל) – עובד כעת.
* **Terminated** (סיומת עבודה) – סיים לעבוד (פוטר/התפטר).
* **Inactive** (לא פעיל זמנית) – לדוגמה חופשה ללא תשלום, עובד בהפסקה אך לא נסגר תיק העסקה.
* **Pending** (ממתין להתחלת עבודה) – אם רוצים לציין עובד שעתיד להתחיל.

(ניתן גם לפצל Terminated לתת-סטטוסים: Fired/Resigned וכו' אך מספיק ציון סיום והחזקת תאריך סיום ב-Employee).

### Enum: סוג קופת גמל / פיקדון (ProvidentFundType)

סוגי ההפקדות/קופות:

* **DEPOSIT** – פיקדון עובדים זרים (הסדר ייעודי דרך רשות האוכלוסין).
* **PENSION** – קרן פנסיה מקיפה (תגמולים ופיצויים רגילים).
* **COMPENSATION** – קרן פיצויים (אם מפרישים בנפרד או מפצלים).
* **HISHTALMUT** – קרן השתלמות.
* **UNION** – ועד עובדים/דמי חבר ארגון (כמו שהופיע באקסל 2.5% "ועד עובדים").
* **OTHER** – סוג אחר במידת הצורך.

ערכים אלה משמשים ב-ProvidentFundContribution.type כדי לזהות את אופי ההפקדה ולנהוג בהתאם (פיקדון – חוקים מיוחדים, פנסיה – דיווח 102 וכו').

### Enum: סוג חופשה (LeaveType)

סוגי חופשות/היעדרויות לניהול זכאויות ודיווח:

* **Annual** (חופשה שנתית) – ימי חופש.
* **Sick** (מחלה) – ימי מחלה.
* **Maternity** (חופשת לידה) – לרישום תקופת לידה.
* **Other** (אחר) – היעדרות אחרת (אבל, מילואים וכו' – אפשר להרחיב בערכים ספציפיים במידת הצורך).

סוג החופשה ישמש למשל ברכיב חופשה/מחלה, ובטבלת דיני עבודה (למשל "חופש", "מחלה" כפי שראינו). ייתכן שלא יוגדר Enum נפרד במודל אלא יבוא לידי ביטוי דרך קודי הרכיבים 312/313 וכו', אך עבור ניהול יתרות עדיף לזהות סוג.

### Enum: סוג התראה (AlertType)

סוגי ההתראות האפשריות:

* **VISA\_EXPIRATION** – פקיעת אשרת עובד זר מתקרבת.
* **MISSING\_FORM101** – טופס 101 חסר/פג תוקף.
* **OVERTIME\_LIMIT** – חריגת שעות נוספות (מעבר למגבלה חוקית).
* **DEPOSIT\_COMPLIANCE** – בעיית ציות לפיקדון (למשל אי-הפקדת פיקדון חודש).
* **GENERAL** – כללי/אחר.

(Enum זה ניתן להרחבה בהתאם לצרכים העסקיים והרגולטוריים).

### Enums נוספים

* **PayrollProcessStatus** – סטטוס לתלוש/מחזור שכר (טיוטה, מחושב, מאושר, משולם, מבוטל).
* **AgreementType** – סוג הסכם עובד: `PERSONAL`, `COLLECTIVE`, `TEMPLATE` (הוגדר בשדה Employee.agreementType).
* **MaritalStatus** – מצב משפחתי: `SINGLE`, `MARRIED`, `DIVORCED`, `WIDOWED` (השתמשנו במסגרת Form101).
* **Gender** – מין העובד, במידת הצורך (זכר/נקבה/אחר) – בטופס 101 יש השפעה (נק' זיכוי לאישה, שירות צבאי וכו'), אך אפשר לגזור מנתונים אחרים.
* **UserRole** – תפקיד משתמש במערכת (מנהל שכר, צופה, מנהל מערכת וכו') אם נדרש לניהול הרשאות, בהתאם ל-ShadCN/T3 auth.

## ישויות וטבלאות נוספות נדרשות (השלמות לסכמת Prisma)

מעבר למודלים שנסקרו, יש מידע חיוני לניהול השכר שלא תמיד מיוצג כישויות עסקיות רגילות, אך צריך להיות זמין במערכת. קובץ האקסל סיפק חלק מטבלאות אלה. נפרט את הטבלאות/אובייקטים הנוספים הדרושים:

* **טבלת מיסים (מס הכנסה)** – המערכת צריכה לדעת לחשב מס הכנסה חודשי. לשם כך נדרש לטעון טבלת מדרגות מס, נקודות זיכוי וערכי נקודת זיכוי. ניתן לנהל זאת כטבלה סטטית בבסיס הנתונים: למשל טבלת TaxBracket עם שדות: fromAmount, toAmount, rate (אחוז המס השולי) לכל מדרגה, וטבלה/פרמטר לנקודת זיכוי (שווי נקודה \~ 235 ש"ח לחודש בשנת 2025, לדוגמה). כמו כן, חישוב תיאומי מס אם עובדים נוספים – מבוסס בטופס 101 (כאן פשוט ניקח את מלוא המס). **הערה:** באקסל לא סופקה טבלת מס הכנסה, כנראה בהנחה שמשתמשים בנוסחאות ידועות חיצוניות. יש לדאוג לעדכון תקופתי של מדרגות המס.

* **טבלת דמי ביטוח לאומי ובריאות** – חישוב ביטוח לאומי דורש אחוזי ניכוי מופחת ומלא, הן חלק עובד והן חלק מעסיק, וכן הפרדה בין מרכיב ביטוח לאומי למרכיב ביטוח בריאות. באקסל סופקה "טבלת מיסים" עם שיעורי ביטוח לאומי עבור סוגי עובדים שונים (צעיר, מבוגר מעל גיל פרישה, נכה, חייל וכו'). במיוחד, עבור עובד זר יש להתייחס אם הוא תושב ישראל או לא – עובד זר אינו מבוטח בביטוח בריאות ממלכתי ולכן חלק הבריאות מתנהג אחרת. הטבלה במערכת יכולה להכיל רשומות לפי קטגוריה (תושב, זר, מסתנן וכו') עם אחוזי ניכוי עובד ומעסיק מפוצלים. לדוגמה:

  * עובד רגיל: 0%/3.5% ביטוח לאומי עובד (מופחת/מלא) ועוד 3.1%/5% בריאות עובד, מעסיק 3.55%/7.6%.
  * עובד זר חוקי: ינוכה ביטוח לאומי בדומה לתושב אך ללא מרכיב בריאות (או מרכיב מוקטן), בהתאם לתקנות (יש מורכבויות בחוק – למשל בסיעוד יש פטור מתשלום חלק עובד בדמי ביטוח).
  * מבקש מקלט ("מסתנן"): רק ביטוח לאומי בלי נק' זיכוי למס.
    אפשר לשמור 2-3 רשומות רלוונטיות (תושב, זר, מסתנן) עם שיעורים מתאימים.

* **טבלת זכאויות ודיני עבודה** – חישוב ימי החופשה, המחלה וההבראה המגיעים לעובד לפי ותק. באקסל סופקה "טבלת דיני עבודה" המציגה כמה ימי חופש ומחלה וכו' לכל שנת ותק, בנפרד לעובד יומי/שעתי. למשל: שנה 1 – 12 ימי חופש (לעובד 6 ימי עבודה בשבוע) או 14 (ל-5 ימים בשבוע), 18 ימי מחלה, 5 ימי הבראה; שנה 2 – 12/14 ימי חופש, 18 מחלה, 6 הבראה; וכן הלאה. יש לעדכן נתונים אלה לפי החוק (שיכול להשתנות). ניתן לממש זאת כטבלת Entitlements: שדות: yearOfSeniority, annualLeaveDaysFor5DWeek, annualLeaveDaysFor6DWeek, sickDays, havraaDays. המערכת תשתמש בטבלה זו כדי לנהל **יתרות חופשה ומחלה** לכל עובד (כלומר לחשב כמה צבר וכמה ניצל, בין היתר בעזרת רכיבי ה"ניצול" האינפורמטיביים). אם עובד בהסכם מיוחד (לדוגמה יותר ימי חופש מהמינימום), המערכת צריכה לאפשר הגדרת **ערכים אישיים** – אולי דרך שדות ב-Employee כגון `annualLeaveDaysPerYear` אם יש חריגה, או רשומת זכאות אישית. באקסל ראינו מושג "הגדרה מפעלית" vs "הגדרה אישית" – כנראה כדי לציין אם להשתמש בטבלת ברירת המחדל או חריג. במערכת: אפשר שדה Employee.useDefaultEntitlements (בוליאני) ואם false – לאפשר להזין זכאויות מותאמות אישית.

* **טבלת נוסחאות חישוב** – עבור רכיבים מורכבים, ייתכן שמגדירים נוסחה. באקסל הוצגו נוסחאות כמו "חישוב שווי דירה" ו"חישוב ביטוח רפואי", עם הסבר על סדר פעולות. ניתן להסיק שיש צורך להחיל חישובים לפי פרמטרים (למשל: **שווי דיור** – אולי מחושב כאחוז מהשכר או סכום קבוע לפי אזור; **ניכוי ביטוח רפואי** – עד % מסוים מהעלות). במקום לאפיין מנגנון נוסחה כללי, במערכת נקבע לוגיקה מפורשת (בקוד) לשוויי דיור ולניכויי ביטוח רפואי, בשל המורכבות הנמוכה. עם זאת, נציין שאם רוצים גמישות, אפשר טבלת Formula עם: formulaName, componentCodesInvolved, operation, condition וכו'. זהו יתרון פחות עדיף כרגע (מגדיל סיכון שגיאה), ולכן ניישם כללית:

  * **שווי דיור**: לפי תקנות עובדים זרים, מותר להוסיף כנגד העובד שווי הטבה עבור מגורים (או לנכות השתתפות בעלות). לרוב המעסיק במקום מוסדר מנכה חלק מהשכר עבור דיור. אפשר לממש זאת כרכיב ניכוי "דיור" אם רלוונטי, שהסכום שלו יהיה מינימום(עלות אמיתית, תקרת ניכוי לפי אזור). התקרות מפורטות בחוק (כ \~500 ש"ח במרכז, \~300 בפריפריה, תלוי אם כולל חשבונות).
  * **ניכוי ביטוח רפואי**: מעסיק רשאי לנכות עד שליש מעלות ביטוח הבריאות הפרטי של העובד הזר (ועד מקסימום עדכני, למשל כ-143 ש"ח ב-2025). במערכת נגדיר רכיב ניכוי "ביטוח רפואי" המחושב אוטומטית: לוקחים את עלות הפוליסה (שהמעסיק יזין עבור העובד, או ערך קבוע למעסיק), מחשבים שליש ממנו ומגבילים לתקרה (המתעדכנת מדי שנה). לדוגמה: אם עלות ביטוח לחודש 300 ש"ח – מותר לנכות 100 ש"ח, בהנחה שהתקרה היא 144 ש"ח, 100 ש"ח < תקרה, אז ינוכה 100 ש"ח.
  * **גילום מס**: במידה ויש הטבה שמעסיק מגלם (משלם את המס במקום העובד), המערכת תחשב את הנטו->ברוטו. באקסל צוין "גילום שווי" כרכיב, וכנראה הנוסחה שם מחברת את שווי ההטבה לשכר בצורה שמגדילה נטו לאותו סכום. זה מורכב אך ניתנת למימוש בקוד בעת החישוב.

* **טבלת משתמשים והרשאות** – לצורך ניהול כניסה למערכת. במסגרת T3 Stack, בדרך כלל ניהול המשתמשים (auth) נעשה מול NextAuth או פתרון אחר, אך לאקסל אין מידע על כך. נגדיר בכלליות: טבלת Users עם id, email, hashedPassword, role, employerId (אם משתמש משויך למעסיק ספציפי). כך מערכת מרובת-מעסיקים תוכל להגביל כל משתמש לנתוני המעסיק שלו. **הרשאות** (permissions) לא מפורטות בשאלה, אך שורטט בעץ התזרימים עמודת "הרשאות", לכן יש לציין אותן: סביר שנגדיר roles כגון "Admin" (מנהל מערכת – רואה הכל), "EmployerAdmin" (מנהל מטעם מעסיק – רואה רק עובדים שלו), "Manager" (יכול לעדכן נתונים) ו-"Viewer".

## קשרים בין הישויות (ERD)

להלן תיאור קשרי הגומלין המרכזיים במודל הנתונים, בין הישויות שתוארו:

* **Employer ←→ Employee**: לכל Employee יש שדה זר employerId המקשר למעסיק שלו (רבים-לאחד). מעסיק אחד יכול להכיל עובדים רבים. ניתן גם לקשר Department למעסיק.
* **Department ←→ Employee**: במידה ומשתמשים במחלקות, לכל Employee יש departmentId (רבים-לאחד). מחלקה אחת יכולה להכיל עובדים רבים. ייתכן גם קשר Department ←→ SalaryRecord/Transaction כפי שהוסבר (לשיוך רכיבים למחלקות תקציביות).
* **Employee ←→ Form101**: לכל Employee יכולים להיות מספר טפסי 101 (יחס אחד-לרבים), אך רק אחד פעיל לכל שנה. אפשר לסמן בטופס איזה בתוקף.
* **Employee ←→ Payslip**: לכל Employee יהיו מספר תלושים (כל חודש, יחס 1-לרבים).
* **Payslip ←→ SalaryRecord**: כל Payslip הוא ראשי ויש לו רשומות מרובות (1-לרבים). כל SalaryRecord מתייחס בדיוק לתלוש אחד.
* **Employee ←→ SalaryTransaction**: תנועות השכר (אם משתמשים במודל זה) נאגדות לפי עובד (1-לרבים) ולפי תקופה. הן מעובדות לSalaryRecords בעת יצירת תלוש.
* **Payslip ←→ ProvidentFundContribution**: כל תלוש עשוי להיות קשור למספר רשומות הפקדה (למשל אחת לפיקדון, אולי שנייה לדמי ועד). יחס 1-לרבים. לחלופין אפשר לקשר ProvidentFundContribution רק לעובד+תקופה, אך עדיף דרך התלוש לחיבור קל לדוחות 102/טפסים שנתיים.
* **Employee ←→ ProvidentFundContribution**: קשר עקיף דרך payslip, או ישיר (כדי לאפשר רישום תרומות לא דרך תלוש). בפועל, כל ProvidentFundContribution מזוהה ע"י שילוב מפתח של עובד+תקופה+סוג.
* **Employee ←→ Alert**: התראות לרוב משויכות לעובד (רבים-לאחד, עובד יכול לקבל מספר התראות). חלק מהתראות עשויות לא להיות משויכות (employeeId = null) אם הן כלליות.
* **User ←→ Employer**: אם יש כמה מעסיקים במערכת, משתמש מסוים משויך למעסיק (כדי להגביל נתונים) – יחס רבים-לאחד (מעסיק יכול להיות לו כמה משתמשי מערכת).
* **User ←→ Employee**: אם המערכת מאפשרת לעובדים גישה (פורטל עובדים), ניתן לקשר משתמש לחשבון העובד שלו. לא צוין במפורש בצרכים, אך אפשרי כהרחבה עתידית (למשל עובד יוכל לראות את התלוש שלו אונליין).

*(תרשים ERD ויזואלי יציג את הישויות כמלבנים עם שמות השדות וקשרים בניהן. לדוגמה: Employer --< Employee, Employee --< Payslip, Payslip --< SalaryRecord, Employee --< SalaryTransaction, Employee --< Form101, Employee --< Alert, Employee --< ProvidentFundContribution (או דרך Payslip).*

בסיכום, הקשרים מבטיחים עקיבות לוגית: תלוש מתקשר לעובד הנכון, תרומות לפנסיה/פיקדון מתקשרות לתלוש ולעובד, התראות מקשרות לגורם הרלוונטי, וכו'.

## ולידציות וValidations עם Zod

באמצעות Zod (ספריית schema validation המשולבת ב-tRPC וב-T3 stack), נבצע אימות של קלט המשתמש ונתונים לפני שמירתם. רשימה של ולידציות קריטיות במערכת השכר:

* **ולידציית שדות טפסים**: הגדרת סכימות Zod עבור כל יצירה/עדכון של ישות:

  * EmployeeSchema תוודא שדות כגון firstName/lastName (מחרוזות לא ריקות, באורך מוגבל – נניח עד 50 תווים), nationalId (תבנית של 9 ספרות אם ת"ז ישראלי או דרכון אלפאנומרי, בהתאם לכלל מוגדר), startDate ≤ endDate כאשר endDate קיים, isForeign חייב להיות true/false (לא ריק). כמו כן, כלל מותנה: אם isForeign=true ויש חובה לפיקדון בענף הספציפי, ניתן להזהיר אם sector ערוך לפטור (אבל זו לוגיקה עסקית – ראו בהמשך).
  * Form101Schema תבדוק שסכומי נקודות הזיכוי לא שליליים, שמספר הילדים הגיוני (למשל לא מעל 20...), שאם maritalStatus = "Married" אבל spouseWorks לא הוזן -> ברירת מחדל spouseWorks=false. ואם hasAdditionalIncome=true -> isMainEmployer חובה לציין false או להפך (למנוע מצב לא עקבי).
  * SalaryTransactionSchema יוודא שהכמות ≥ 0, שהתעריף ≥ 0, שאחוז בין 0 ל-10 (לא סביר מעל 1000%, אולי נגדיר גבול של 10 כדי לתפוס שגיאות), שסכום נוכחי ≥ 0. כמו כן, exactlyOneOf(\[quantity\*rate, amount]) – למשל אם הוזן סכום ישיר, אפשר לאפשר quantity=0 או 1, אבל נוודא שמשתמש לא מילא גם כמות וגם סכום ידני סותר.
  * ProvidentFundContributionSchema תבדוק שסכומי ההפרשות ≥ 0 ולא עולים על תקרה הגיונית. למשל: employeeContribution + employerContribution + compensationContribution = totalContribution (ניתן Zod.refine לוודא התאמה). סוג DEPOSIT -> נבדוק ש-employeeContribution = 0 (חוקי) ואילו סוג PENSION -> אפשר employeeContribution > 0.
  * AlertSchema יוודא שאם type דורש employeeId (למשל VISA\_EXPIRATION) אז employeeId לא ריק, ושאם צוין dueDate אז dueDate ≥ date (תאריך יעד אחרי יצירת ההתראה).

* **ולידציות ברמת תהליכים**:

  * בעת חישוב תלוש (action דרך tRPC), נרצה validar שהנתונים הדרושים קיימים: למשל שקיים Form101 תקף; שלהעובד יש תעריף שכר או שכר בסיס; שאם העובד לא תושב (isResidentForNI=false) הוגדרו חישובי ביטוח לאומי מתאימים. חלק מזה יתבטא בהתראות אם חסר, אבל גם בפונקציית החישוב אפשר לעשות `z.preprocess` או בדיקות ידניות ולזרוק שגיאה.
  * אימות עקביות סכומי התלוש: לאחר חישוב, אפשר להשוות סכומים ידנית: Σתשלומים - Σניכויים = נטו (עד העיגול אגורות). אפשר ליצור Schema לתלוש שמחשב זאת (`ZodEffects` אחרי יצירה). אם לא מתאים – זו אינדיקציית באג.
  * Validate שאי אפשר לשמור שני תלושים לאותו עובד ואותו חודש (Constraint ברמת DB, אך גם לבדוק ב-service לפני יצירה).
  * Validate מניעת פעולות לא חוקיות: למשל, ניסיון למחוק תלוש שכבר שולם; ניסיון לערוך Retroactively חודש שכבר דווח (אולי להתריע).

* **מסמכים מצורפים**: אם נדרש להעלות קובץ חתום של טופס 101, אפשר לבדוק באמצעות Zod file או לוודא סוג קובץ.

באופן כללי, Zod ישתלב בהגדרת ה-Router של tRPC: נגדיר סכימות Input לכל קריאה. דוגמה חלקית (בקוד TypeScript משוער):

```ts
// דוגמה לסכימת ולידציה לעובד חדש
const NewEmployeeSchema = z.object({
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  nationalId: z.string().regex(/^[A-Z0-9]{5,10}$/), // דוגמה: דרכון יכול אותיות ומספרים
  isForeign: z.boolean(),
  country: z.string().optional().nullable(),
  sector: z.nativeEnum(SectorEnum),
  startDate: z.date(),
  endDate: z.date().optional(),
  agreementType: z.nativeEnum(AgreementTypeEnum),
  isResidentForNI: z.boolean().optional()
}).refine(data => !data.isForeign || (data.country && data.isResidentForNI!==undefined),
   { message: "Must specify country and NI residency for foreign employee", path: ["country"] });
```

ה-refine לעיל למשל מוודא שאם isForeign=true אז country ו-isResidentForNI מוגדרים. בצורה דומה נגדיר תנאים בין שדות Form101.

בשכבת ה-UI, נשתמש ב-zod להסיק סכימות עבור טפסים (באמצעות `react-hook-form` ו-`zodResolver`) כדי לספק למשתמש משוב מיידי על שדות שגויים (לדוגמה: הודעה אם הוזן מספר לא תקין). שילוב זה, בנוסף לולידציה בצד השרת, יבטיח עקביות ואמינות הנתונים.

## הפרדת לוגיקה עסקית מה-UI באמצעות tRPC

המערכת בנויה בסטאק **T3** ולכן משתמשת ב-tRPC כ"שכבת שרת" בתוך אפליקציית Next.js. העקרון הוא להפריד את הלוגיקה העסקית – חישובי שכר, בדיקות רגולטוריות, גישה למסד הנתונים – מממשק המשתמש. כמה דגשים במימוש:

* **מודולי Service בצד השרת**: נכתוב פונקציות שירות (service functions) לביצוע פעולות עסקיות מורכבות. למשל, `calculatePayslip(employeeId, month, year)` שתבצע את כל חישובי השכר עבור העובד והחודש הנתונים: צבירת שעות רגילות, חישוב שעות נוספות לפי התנועות, חישוב מס הכנסה לפי פרטי ה-Form101 של העובד, חישוב ביטוח לאומי, יצירת רשומות SalaryRecord ו-ProvidentFundContribution, וכו'. פונקציה כזו תמוקם בתיקיית ה-server (למשל תחת `src/server/payslipService.ts`).
* **Routerים של tRPC**: נגדיר ראוטרים כגון `employeeRouter`, `payslipRouter`, `transactionRouter` וכו'. כל ראוטר יכיל פרוצדורות (procedures) לחשיפה ל-UI. לדוגמה:

  * `createEmployee` (mutation) – יקבל input validated by Zod, יקרא ל-Prisma ליצירת רשומת Employee חדשה.
  * `calculatePayslip` (mutation או query) – יקבל מזהה עובד וחודש, ויקרא לפונקציית service של חישוב תלוש. הפונקציה תחזיר את הנתונים (רשומות השכר המחושבות וכו'), וה-procedure יכול להחליט האם לשמור אותם ב-DB (במקרה של אישור סופי) או רק להחזיר סימולציה.
  * `listPayslips` (query) – יחזיר רשימת תלושים (למשל להציג היסטוריה) תוך שימוש ב-Prisma כולל יחסים (employee name וכד').
  * `submitTransactions` (mutation) – נקלטות "תנועות לעובד" למשל מקובץ נוכחות, יישמרו בבסיס נתונים, ואז אולי יקרא אוטומטית לחישוב (או בנפרד).
* **הפרדת אחריות**: ה-UI (קומפוננטות React עם ShadCN) לא ידע את פרטי החישוב או שאילתות SQL – הוא יזמן רק את פונקציות ה-tRPC המתאימות. למשל, כאשר משתמש לוחץ "חשב תלושים לחודש X", ה-UI יקרא `payslipRouter.calculateAll({ month: X, year: Y })`. השרת יבצע: עבור כל עובד פעיל -> איסוף התנועות -> חישוב -> שמירה. **התוצאה** (למשל, ספירת תלושים שעודכנו, או אפילו הנתונים עצמם) תחזור ל-UI להציג הודעת הצלחה.
* **Business Logic Reuse**: שימוש ב-tRPC מאפשר גם לקרוא ללוגיקה מתוך פקדי UI שונים. למשל, ייתכן מסך הזנת תנועות העובד שבו לאחר שמירה רוצים מיד לראות את ההשפעה על השכר – אפשר לקרוא ל-procedure של חישוב תלוש במצב טיוטה ולקבל נתונים להצגה, מבלי שה-UI יצטרך לחשב דבר.
* **הרשאות ובקרת גישה**: ב-tRPC Middleware נבדוק את תפקיד המשתמש. לדוגמה, רק משתמש עם role=EmployerAdmin שמעסיקו תואם לנתוני העובד המבוקש יכול ליצור/לערוך נתוני עובד שלו. את הבדיקה הזו עושים ב-layer של ה-router (middleware per procedure or router). כך למשל `employeeRouter.getAll` יסנן Employees לפי המשתמש המחובר (אם Employer role – יחזיר רק עובדים של המעסיק שלו, אם Admin – יכול לבחור מעסיק וכד').
* **Error Handling**: שגיאות עסקיות (כמו "אין טופס 101, לא ניתן לחשב תלוש") ינוהלו בצד השרת ויועברו ל-UI כ-exception דרך tRPC, שם ניתן לתפוס ולהציג הודעה ידידותית (שימוש ב-`TRPCError`). לדוגמה:

```ts
if (!form101) {
  throw new TRPCError({ code: 'BAD_REQUEST', message: 'טופס 101 חסר לעובד, לא ניתן לחשב מס' });
}
```

* **מניעת לוגיקה בצד הלקוח**: כל חישוב הנוגע לרגולציה (מס, ביטוח לאומי, פנסיה) יעשה בשרת. ה-Client לא יכיל למשל חישוב נקודות זיכוי או מדרגות – זאת כדי להבטיח מקור אמת אחד ולעמוד בדרישות אבטחת מידע (לא לחשוף מידע רגיש או לאפשר מניפולציה).

לסיכום, tRPC משמש כמתווך נקי: הוא מקבל את הקריאות ממרכיבי ה-UI, עובר ולידציה עם Zod, מבצע את הלוגיקה (או קורא לפונקציות service), ומשיב עם הנתונים או עם הודעת שגיאה מוסברת. בכך ה-UI נשאר "דק" ומתמקד בתצוגה וחוויית משתמש, והשרידות והדיוק משתפרים.

## חיבור לשכבת ההצגה (UI) עם ShadCN UI

ShadCN UI הוא סט קומפוננטות מעוצבות על בסיס Radix ו-Tailwind, המאפשר בניית ממשקים עקביים. במערכת שלנו נבנה ממשקי ניהול שכר המקלים על המשתמש להבין את הנתונים המורכבים. מספר דוגמאות למסכים ורכיבי UI שנעצב:

* **לוח מחוונים ראשי**: יציג למשתמש תקציר של התראות ודברים לטיפול. לדוגמה, כרטיסי מידע (Cards) עבור: "# עובדים זרים ללא טופס 101", "# אשרות יפוגו בחודש הקרוב", "סטטוס חישוב שכר לחודש נוכחי" וכו'. נשתמש ב-**Card** ו-**Alert** components של ShadCN כדי להבליט מידע חשוב. למשל, התראת אשרה קריטית תופיע כ-Card אדום עם אייקון התרעה.

* **ניהול עובדים**: דף טבלה של עובדים (כנראה בעזרת ShadCN **Table** component עם פילוח וחיפוש). העמודה "סטטוס" תשתמש ב-badge או color indicator (למשל צבע ירוק לפעיל, אפור ללא פעיל). לחיצה על עובד תפתח דף פרופיל עובד.

  * **טופס פרטי עובד**: טופס עריכה/יצירה המשתמש ב-**Form** של ShadCN וקומפוננטות קלט (Input, Select, DatePicker וכו'). למשל שדות שם, ת"ז, מדינה (Select מתוך רשימת מדינות), תאריך התחלה (DatePicker). הטופס יחובר ל-zodResolver כדי להציג שגיאות מתחת לשדות באופן אוטומטי.
  * **כרטיסיות פרופיל**: ייתכן דף העובד יציג בכרטיסיות (Tabs) מידע כגון "פרטים אישיים", "טופס 101", "שכר והסכמים", "יתרות". בכל כרטיסיה נשתמש ברכיבי ShadCN מתאימים. למשל טאב "טופס 101" יציג טופס דינאמי עם checkboxes (לשדות כמו spouseWorks) וקלט מספרי (childrenCount).
  * **רשימת התראות לעובד**: בחלון הפרופיל נציג רשימת Alertים פתוחים הקשורים לעובד, אולי בתוך Accordion או פשוט רשימה עם Icon + text. אפשר למשל להשתמש ב-**AlertDialog** עבור הצגת תוכן התראה בלחיצה.

* **ניהול תנועות שכר**: מסך להזנת "תנועות לעובד" (כמו באקסל). נשתמש ב-**Data Table** או **Card** עם טופס ערך:

  * בראש הדף בוחרים עובד וחודש (Dropdowns).
  * מציגים טבלה של תנועות קיימות לחודש (אם יש, נטען דרך tRPC).
  * מתחת, טופס הוספת תנועה חדשה: רכיב Select לרכיב שכר (עם אפשרות להקליד קוד או שם – ניתן לממש **Combobox** component של ShadCN לחיפוש ברשימת הרכיבים), שדה כמות, שדה תעריף, וכד'.
  * בלחיצה על "הוסף", נקרא tRPC mutation להוספה, ודרך React Query נחזור רשימה מעודכנת.
  * ניתן להוסיף ולידציה מיידית: למשל אם בחרו "שעות נוספות 125%" ולא מילאו כמות, להאיר את השדה.
  * מסך זה יכול גם להציג Summary: סה"כ שעות רגילות/נוספות שהוזנו. אולי נשתמש ב-**Popover** שמראה חישוב ביניים או ב-**Progress** bar למכסת שעות (למשל, "X/186 שעות החודש נוצלו").

* **חישוב והצגת תלוש**: לאחר הזנת כל הנתונים, משתמש (מנהל שכר) ילחץ "חשב שכר".

  * ניתן במסך מרכזי לבחור "חודש" וללחוץ "חשב לכולם". תופיע למשל **Dialog** (Modal) של התקדמות. נעדכן את המצב בזמן-אמת (maybe using websockets or polling) – אבל בפשטות, קריאת tRPC תחזיר בסיום.
  * עם סיום החישוב, ניתן להפנות למסך "תלושים" בו מופיעים כל התלושים שחושבו.
  * **רשימת תלושים**: טבלה המציגה שורה לכל תלוש עם: שם עובד, חודש-שנה, ברוטו, נטו, סטטוס. יכול להיות **DataTable** עם אפשרות לסנן לפי מעסיק/מחלקה.
  * לחיצה על תלוש ספציפי תפתח **Drawer** או **Modal** עם פירוט התלוש:

    * כותרת עם פרטי העובד (שם, ת.ז., מחלקה, סטטוס עובד) ופרטי התקופה (חודש, שנה, תאריכי תחילה/סיום עבודה אם חל באמצע חודש).
    * טבלה פנימית של רכיבי השכר: עמודות: תיאור, כמות, תעריף, סכום. נחלק את הרכיבים לקבוצות: תשלומים, ניכויים, מידע. אפשר להשתמש ברכיבי **Table** בתוך המודל, עם perhaps grouping rows או פשוט כותרות מפרידות בין חלק "תשלומים" לחלק "ניכויים".
    * בסוף, סכומי ביניים: סה"כ הברוטו, סה"כ הניכויים, נטו לתשלום. נציג אותם בצורה ברורה (bold). ייתכן שימוש ב-**Separator** component להפריד.
    * בחלק התחתון של המודל, נציג כפתורים: "אשר תשלום" (שינוי סטטוס ל-Paid) או "ערוך מחדש" (חוזר לטיוטה אם צריך).
    * אולי טאבים נוספים: "ניתוח תלוש" שמפרט כיצד חושבו המס והביטוח (באקסל הייתה עמודה "חקר תלוש – איך הגענו למס... "). ניתן במערכת להציג תחת טאב "פירוט חישובי מס/ביטוח" הסבר: למשל "מס הכנסה: ברוטו חייב 5000 ש"ח => מדרגה 10% עד 5000 => 500 ש"ח, פחות נק' זיכוי 2.25\*235 =>  (וכו')" – מידע זה יכול להיווצר ע"י הפונקציה מחשבת ולהיחשף.
    * אפשר להשתמש ברכיב **Accordion** לשדות חישוב, כדי שלא לעמיס, רק אם המשתמש רוצה "לחקור" הוא פותח וראה פירוט.

* **ממשק תשלום והפקדות**: לאחר אישור, יש צורך להפיק קבצי תשלום לבנק ודוחות לרשויות:

  * **קובץ בנק (מס"ב)**: נוצרת רשימת תשלומי נטו. אפשר במסך "תשלום שכר" לבחור את החודש ולראות רשימה של עובדים עם סכום נטו, סימון מי שולם ומי לא. נשתמש ב-**Checkbox** או Switch ליד כל עובד. כפתור "ייצא קובץ בנק" – יקרא tRPC שמייצר קובץ (txt לפי פורמט מס"ב). בעת חזרה, המערכת יכולה לסמן את הרשומות ששולמו.
  * **דוח הפקדות לפיקדון**: עבור עובדים זרים עם פיקדון, כנראה אין צורך ליצור קובץ – ההפקדה נעשית על ידי המעסיק ידנית לחשבון הייעודי (או דיווח מקוון). אבל המערכת יכולה להציג טבלת "סכומי פיקדון לחודש" כדי שהמעסיק ידע כמה עליו להפקיד לכל עובד. מסך זה יכול להיות דומה לרשימת תלושים אבל רק עם עובדי פיקדון ועמודה "פיקדון" (סה"כ). אולי נוסיף אפשרות להפיק קובץ CSV של נתוני הפיקדונות כדי להעלותו לאתר רשות ההגירה.
  * **הפקת דוחות 102, 126**:

    * *טופס 102 (דיווח חודשי)* – המערכת תפיק סכומי מס הכנסה, ביטוח לאומי ופקדון לתשלום. אפשר ממשק שבו בוחרים חודש, ומוצג סיכום: "סך משכורות X, סך מס Y, סך ביטוח לאומי חלק עובד Z, חלק מעסיק W, סך פיקדונות Q". וכפתור "הפק דוח 102" שיוציא PDF או קובץ נתונים שניתן להגיש. ייתכן שנרצה גם פורמט קליטה לתוכנות חיצוניות (יש מעסיקים שמדווחים דרך קובץ למערכת ממשלתית).
    * *טופס 126 (שנתי)* – דוח שנתי מרוכז. ניתן להפיק דרך שאילתת Prisma מסכמת (או אף לשמור נתוני שנה בכל תלוש חודשי ואז פשוט סכום). הממשק: לבחור שנה -> כפתור "הפק טופס 126" -> המערכת יוצרת קובץ (למשל בפורמט XML שהמעסיק מעלה לרשויות או טופס PDF).
    * *טופס 106 (שנתי לכל עובד)* – יכול להיות בפורטל העובד (אם יהיה) או לפחות אפשרות להוריד PDF לכל עובד. שיטת ההפקה דומה – משתמשים בנתוני התלושים של השנה.

* **עיצוב ונגישות**: נבחר ב-ShadCN UI משום שיש בו כבר עיצובים עקביים. נטפל בtheme כהה/בהיר אוטומטית אם נרצה, ונוודא RTL (כי הממשק בעברית). ShadCN מבוסס Tailwind אז קל לתמוך ב-RTL. אם צריך אייקונים (למשל התרעות) – נשתמש ב-icon pack הכלול.

* **פורטל עובדים** (אופציונלי): אם ירצו לאפשר לעובדים הזרים עצמם גישה, אפשר באמצעות אותו UI: דף כניסה שונה לעובד שמאפשר לראות רק את התלושים שלו, למלא טופס 101 דיגיטלי וכד'. שם נשים דגש על פישוט והנגשה (שפה – אולי תרגום לאנגלית/רוסית/תאית לפי הצורך). זה מעבר לתכולה המבוקשת כרגע, אך המערכת בנויה באופן שיכול לתמוך בכך.

לסיכום, עם ShadCN UI נייצר חוויית משתמש מודרנית, עם טבלאות ופורמס נוחים, פידבק מיידי לולידציות, ותצוגה ברורה של תלוש השכר ונתוני העובד. השימוש בקומפוננטות סטנדרטיות מבטיח עקומת למידה קצרה לצוות והיתכנות תחזוקה טובה לאורך זמן.

## דגשים רגולטוריים קריטיים

המערכת חייבת לעמוד בדרישות החוק בישראל בנוגע להעסקת עובדים זרים ושכרם. להלן הדגשים המרכזיים שיש להתחשב בהם, כפי שנרמז גם ממבנה הנתונים:

* **חובת טופס 101**: בכל תחילת שנה (ובתחילת העסקה) העובד חייב למלא טופס 101. ללא טופס 101, יש לנכות מס מרבי (47% כיום). לכן המערכת צריכה:

  * לסמן עובד ללא טופס 101 תקף – בהתראה (`MISSING_FORM101`).
  * לאפשר קליטת נתוני הטופס למערכת (כאמור במודל Form101).
  * להשתמש בנתונים אלה לחישוב נקודות זיכוי במס. למשל, עובד נשוי+2 יקבל נק' זיכוי נוספות בהתאם לגיל הילדים וכו', בעוד עובד רווק יקבל בסיס 2.25 נקודות (או פחות אם אינו תושב).
  * אם עובד הצהיר שהוא לא "עיקרי" (יש לו מעסיק אחר עיקרי), המערכת אמורה לנכות מס בהתאם – בדרך כלל לנכות לפי מדרגת מס מרבית ללא נקודות זיכוי. צריך ליישם זאת בלוגיקה (if !isMainEmployer then tax = full 47% on all income, for example, or apply secondary tax table if provided).
  * המערכת צריכה לספק אפשרות הפקת **טופס 101 דיגיטלי לחתימה** – באקסל ראינו ציון "שליחת 101 לחתימה". כלומר, אפשרות לשלוח לעובד קישור למילוי הטופס אונליין (ואז הנתונים נשמרים ישירות). זהו feature שיכול להיות מתוכנן (integrate with services like docusign or פשוט UI מאובטח לעובד).

* **מדרגות מס ונקודות זיכוי**: יש ליישם עדכון מדרגות שנתי. נכון ל-2025, למשל:

  * 0₪–\~6500₪ – 10%,
  * \~6500–\~9200 – 14%,
  * וכו' (ערכים משוערים) עד מדרגה עליונה 50%.
  * נקודת זיכוי חודשית \~235 ש"ח. אישה מקבלת 0.5 נקודה יותר, הורה לילדים קטנים נק' נוספות, עולה חדש וכד' – בהתאם לנתוני Form101.
  * עובדים זרים: אם נחשבים "תושבי חוץ" לצורכי מס (לרוב כן), חלקם זכאים לנק' בסיס (1.25 אולי) וחלקם לא (מבקשי מקלט – 0 נק'). המערכת צריכה לאפשר סימון סטטוס זה (אולי דרך isResidentForNI=false או שדה נוסף).
  * יש לעדכן את חישוב המס כך שיעשה **חודשי מצטבר** או חודשי רגיל לפי בחירה. בישראל מקובל חישוב מצטבר (כדי להתחשב בשינויים במהלך השנה). מאפיון זה לא נדרש במפורש, אבל מערכת טובה תתמוך בכך: למשל, אם עובד התחיל באמצע שנה, לחלק נק' זיכוי באופן יחסי, אם קיבל בונוס גדול – ניתן לפרוס על פני שנה וכו'. באקסל דווקא ראינו רכיב "פריסה" (למשל ב-הבראה קוד 113 סומן "פריסה" לביטוח לאומי). ניישם פריסה כנדרש: דמי הבראה שנתיים נפרסים ל-12 לחישוב ביטוח לאומי, אבל למס הכנסה – המעסיק יכול לבחור לפרוס מענק.
  * **טופס 116 ותיאומי מס**: במידה ועובד מביא אישור תיאום מס, יהיה צורך להזין למערכת את מדרגת המס המותאמת. זה לא צוין ישירות אך ניתן לתמוך על ידי שדה ב-Form101 או עובד (כמו overrideTaxRate או deduction).

* **ניכויי ביטוח לאומי ובריאות**:

  * העובד **חייב** בתשלום דמי ביטוח לאומי (כולל ביטוח בריאות אם תושב) – המעסיק חייב לנכות במקור.
  * המערכת צריכה לחשב את חלק העובד בדמי ביטוח לאומי לפי השיעורים העדכניים. למשל, עבור תושב ישראל 2025: 0% עד \~7130₪ ולאחר מכן 7%; חלק בריאות 3.1% -> 5%.
  * חלק המעסיק: 3.55% -> 7.6% לביטוח לאומי.
  * עובד זר:

    * אם בעל אשרה ורישיון (חוקי), המעסיק חייב לשלם ביטוח לאומי עבורו. חלק העובד בד"כ דומה לתושב ישראל, *אך* עובדים זרים **פטורים מתשלום ביטוח בריאות ממלכתי** כי אינם זכאים לשירותי בריאות ממלכתיים. במקומו, המעסיק צריך לבטח אותם בפרטי. לכן, **חלק הבריאות בדמי הביטוח לאומי לא ינוכה**. בפועל, שיעורי ביטוח לאומי לעובד זר: עד סכום מסוים \~1.0%, מעליו \~?% (צריך לוודא, ייתכן \~5%). באקסל לא ראינו explicitly, אך סביר שהשתמשו בקטגוריה נפרדת. המערכת צריכה בהתאם לשדה isResidentForNI לקבוע טבלת שיעורים שונה.
    * דוגמה: עובד זר לא בסיעוד – תשלום עובד: 1.04% (מופחת) במקום 1.04+3.23=4.27% שהיה אצל תושב, כלומר אינו משלם 3.23% בריאות. במקסימלי: 7% במקום 12.17%. מעסיק: אותו 7.6% מקסימלי, כנראה. כך לפי הטבלה בשורה 1 המתייחסת לתושב (4.27% מול 12.17% עובד). צריך לוודא באופן מדויק מתקנות, אך המערכת תתמוך בשני מצבים.
    * עובד זר בסיעוד: לפי חוק פטור לחלוטין מדמי ביטוח לאומי חלק עובד. הטבלה באקסל כללה קטגוריות לגיל הפרישה ונכות, לא מפורש סיעוד, אך יש פטור (אולי "מי שטרם מלאו 18" דומה, או מוגדר מחוץ). בשביל זה אפשר לתת סוג עובד Enum נוסף (sector=Caregiving then NI rates differ).
  * המערכת תנכה את חלק העובד מתוך השכר ותדאג להפריד חלק מעסיק (לצורכי דוח עלות).

* **פיקדון חודשי לעובדים זרים**: אחד ההיבטים החשובים. חוקים אחרונים (2016, 2020) מחייבים מעסיקים בענפי בנייה, חקלאות, תעשייה ומוסדות סיעוד מסוימים להפקיד **פיקדון כספי חודשי** עבור כל עובד זר. עיקרי הדרישות:

  * גובה הפיקדון הוא **סכום חלק המעסיק בפנסיה + חלק הפיצויים** עבור אותו עובד, במשרה מלאה. כלומר סה"כ \~14% מהשכר (ליתר דיוק בהתאם לצו – בבנייה וחקלאות 6.5%+6% פנסיה, 8.33% פיצויים -> 20.83%. אך בחוק הפיקדון היו מספרים אחרים: למשל תעשייה/סיעוד הוזכר 5.5%+8.33% = 13.83%, אולי בגלל ששיעור תגמולי המעסיק בסיסי נמוך יותר או שזה מהצו שם). באקסל בדוגמה "ניהול פקדונות" הוגדר 5.5% ו-8.33% – כנראה בהתאם לתעשייה.
  * **אין לנכות מהעובד את הפיקדון** – זה כסף נוסף מעבר לשכר הברוטו, המשולם ע"י המעסיק. כלומר, בניגוד לפנסיה רגילה שבה חלק העובד מנוכה מהברוטו, כאן העובד לא משתתף. המערכת תוודא זאת (ולעולם לא תיצור SalaryRecord של ניכוי פנסיה עבור עובד זר בענפים הללו).
  * המעסיק מפקיד לחשבון ייעודי המנוהל ע"י רשות ההגירה. מבחינת מערכת – היא לא מבצעת את ההעברה בפועל אבל צריכה לספק את הנתונים: כמה להפקיד בכל חודש, על מי, ואולי דוח אישורים.
  * הפיקדון מחליף את חובת המעסיק לביטוח פנסיוני עבור העובד, אך אינו פוטר מתשלום זכויות סוציאליות אחרות (כגון פיצויים עבור תקופות שלא כוסו, אולי אם השכר עלה מעל התקרה). עבורנו: אם התווית Employee.sector מציינת ענף חייב פיקדון, **לא** נבצע חישוב פנסיה רגילה, אלא נחשב ProvidentFundContribution מסוג DEPOSIT.
  * מתי מתחילים: החל מהחודש הראשון לעבודת העובד הזר – אין תקופת המתנה (בניגוד לפנסיה רגילה שיש 6 חודשי המתנה לעובד חדש). לכן הלוגיקה: אם Employee.isForeign && sector דורש פיקדון && יש תלוש לחודש -> צור ProvidentFundContribution.
  * תקרה: החוק מגביל הפקדה לפי השכר הממוצע במשק או סכום קבוע. באקסל נראה תקרה 6248 ש"ח שכר מבוטח ו770₪ הפקדה – כנראה ערכים של 2025. המערכת תיישם זאת: if (שכר > 6248) חישוב לפי 6248, ואם סכום מחושב > 770₪, חתוך ל-770₪.
  * משיכה: העובד ימשוך את הכסף רק בעת עזיבת ישראל. אם נשאר שלא כחוק > משיתים קנס (20% או 40%). זה לא בהכרח במScope של מערכת שכר, אבל **חשוב** שהמערכת תתן דוח "פיקדון" שנתי כדי שהמעסיק יוכל למסור לעובד או לשלטונות.
  * חריג: עובדים זרים מסתננים (מבקשי מקלט מאריתראה/סודן) – להם היה הסדר פיקדון נפרד: המעסיק מפקיד 20% מהשכר על חשבון העובד (כלומר כן מנוכה מהעובד 20%) ועוד 16% על חשבון המעסיק. במקרה כזה, יש ניכוי מברוטו. אך אלו תקנות מיוחדות (ייתכן שאינן בתוקף לאחר 2020 בשל פסיקה). אם כן, במערכת ניתן להגדיר סוג ProvidentFundType="DEPOSIT\_ASYLUM" למשל, וליצור רכיב ניכוי 20% מהברוטו, אבל רק אם רלוונטי. לפי Kol Zchut, מסתננים לא זכאים לנק' זיכוי ממס ויש פיקדון 20%. נזכיר זאת כהערת שוליים: אם הארגון מעסיק מסתננים, יש להתאים.

* **זכויות סוציאליות נוספות**:

  * **דמי הבראה**: אחרי שנת עבודה ראשונה, כל עובד (גם זר) זכאי לדמי הבראה. בד"כ משולמים אחת לשנה או בפריסה חודשית. באקסל רכיב 113 "הבראה" סומן כ"פריסה" לצורך ביטוח לאומי, משמע שמשלמים כנראה פעם בשנה אבל מפצלים לצורך חישוב דמי ביטוח. במערכת אפשר לטפל באופן פשוט: להוסיף בתלוש של חודש מסוים את הסכום (לפי X ימים \* תעריף יום הבראה, תעריף שמתעדכן – \~384 ש"ח ליום הבראה נכון ל-2023). ניתן לאפיין שהתלוש של חודש **יוני** למשל כולל הבראה עבור השנה (או במלאת שנת עבודה לעובד).
  * **חופשה שנתית**: חובה לנהל רישום ימי חופש. המערכת תחשב ותציג יתרה. אם עובד עוזב, יש לשלם **פדיון חופשה** על היתרה (רכיב 114 באקסל). זה אומר: אם נותרו לעובד 5 ימי חופש לא מנוצלים, בתלוש האחרון יופיע "פדיון חופש – 5 ימים \* שווי יום = סכום". השווי יום = שכר חודשי חלקי מספר הימים שעובדים בחודש (או לפי 200 שעות אם שעתי).
  * **מחלה**: עובדים צוברים 1.5 ימי מחלה לחודש (18 לשנה, כמו שראינו). המערכת תעקוב, וברכיב "מחלה" בתלוש (313) ישולם לפי חוק: יום 1 ללא תשלום, 2-3 ב-50%, מהיום 4 ואילך 100%. צריך להתחשב אם העובד נצל מלא – המערכת יודעת כמה ניצל (רכיב 193 אינפורמטיבי), ותחשב אוטומטית את התשלום.
  * **שעות נוספות ומנוחה שבועית**: חוק שעות עבודה קובע תגמול 125% ל-2 השעות הנוספות הראשונות ליום, 150% מעבר לכך, וכן 150% למנוחה שבועית + שעות נוספות במנוחה 175%/200%. באקסל הוגדרו רכיבים 125, 150, 200 בהתאמה. המערכת יכולה בעת חישוב לבדוק את שעות הנוספות שהוזנו (נניח SalaryTransaction עם קוד 125 ו-150) ולעדכן אם צריך מעבר. כמו כן אם מוזנות שעות במנוחה שבועית – אולי יסומנו בנפרד והמערכת תפלג.
  * **שכר מינימום**: (נושא קריטי אך לא הוזכר מפורשות) – יש לוודא ששכר הבסיס + שעות עבור עובד זר לא יורדים מתחת למינימום החוקי (5300 ש"ח לחודש, או 29.12 ש"ח לשעה נכון ל-2025). אם למשל עובד חודשי חלקית, המערכת תתריע.
  * **ניכויים מותרים מהשכר**: אסור לנכות משכר עובד זר סכומים שאינם חוקיים. מותרים: מס הכנסה, ביטוח לאומי, הבריאות שהוזכר (עד שליש עלות, תקרה \~₪139), ניכוי עבור מגורים (עד תקרה אזורית), מקדמות שהוסכם עליהם, ועוד כמה.

    * המערכת צריכה להגביל את סכומי הניכוי בהתאם. לדוגמה, אם הוכנס רכיב "ניכוי עבור דיור" והסכום עולה על התקרה, אפשר לבצע אוטומטית `min(הסכום, תקרה)` או לפחות להתריע.
    * לפי כל-זכות: מעסיק יכול לנכות עבור דיור ועלויות נלוות עד סכום מירבי שקובע משרד העבודה (שונה בין אזור מרכז/אחר, ולסיעוד יש כללים נפרדים). למשל ב-2023: כ-528 ש"ח לחודש במרכז עבור דיור. על המערכת להכיל ערכים אלה כטבלה לפי שנה ואזור.
    * ניכוי עבור ביטוח רפואי: כאמור עד שליש מהעלות ולא יותר מכ-143 ש"ח (2025).
    * סה"כ כל הניכויים המותרים (למעט ניכויי חובה) לא יעלו על 25% מהשכר (יש תקנה כללית מגבילה). המערכת יכולה בבדיקת תלוש ליישם כלל: if (Σניכויי רשות > 25% ברוטו) -> התרעה/מניעה.
    * ניכויים שאינם בשקלים אלא הטבות שווי (כמו "שווי רכב" 131) אינם מנוכים מהנטו אבל כן חייבים במס – המערכת מטפלת בזה כנפרד.
  * **הפרשות פנסיוניות רגילות**: במידה והמערכת גם משמשת למעסיקים של עובדים ישראלים או שלעובדים זרים יש זכאות רגילה (למשל עובד זר שאינו מחויב בפיקדון – אולי בהייטק עם ויזת מומחה – אז חייב פנסיה רגילה), יש לעמוד בצו ההרחבה לפנסיה: 6% עובד, 6.5% מעסיק, 8.33% פיצויים. זה כבר חלק מהחישובים שתוארו, אך מצויין כדי לזכור להפריד בין מי שהולך לפנסיה (ואז employeeContribution קיים) למי שהולך לפיקדון (employeeContribution=0, type=DEPOSIT).
  * **דיווח לרשויות**:

    * טופס 102 חודשי – המערכת מכינה את הנתונים אך המעסיק בדרך כלל מדווח באתר/שולח קובץ. חשוב שממשק ה-102 יציג את **פילוח העובדים זרים** בנפרד, כי בדוח 102 יש סעיפים ייעודיים: עובדים תושבי ישראל, עובדים זרים, מבקשי מקלט – לכל קבוצה סכומי שכר וחישובי ביטוח נפרדים, כיוון ששיעורי הביטוח ופטורי המס שונים. לכן, במערכת בדוח 102, יש לחשב ולפרט:

      * "סה"כ שכר חייב מס של עובדים זרים: X, מס שנוכה: Y" וכד'.
      * "סה"כ שכר מבוטח ביטוח לאומי של עובדים זרים: A, דמי ביטוח (עובד/מעסיק): B/C".
      * כנ"ל עבור עובדים ישראלים רגילים.
      * מבקשי מקלט אם יש – יצוינו לחוד.
    * טופס 126 שנתי – מפרט לכל עובד את סה"כ השכר, מס, ביטוח וכו'. המערכת תכלול את כל העובדים, אך שדה "תושב ישראל" אצל עובד זר יהיה מסומן לא, מה שמשפיע גם על קליטת הנתונים ברשויות.
    * טופס 101 יש לשמור למקרה ביקורת, אך אין דיווח שלו לרשות אלא אולי בקרות מעסיקים – המערכת מאפשרת להפיק טופס 101 בפורמט חתום דיגיטלית אם יבקשו.
    * **תקנות דיווח פיקדון**: החל מ-2022 מעסיקים חייבים לדווח די חודשי גם על הפיקדון באתר יעודי. המערכת יכולה לעזור להפיק קובץ דיווח זה (לפי מספרי דרכון וכו'). אם רוצים, ניתן לשמור בכל ProvidentFundContribution גם מזהה שהדיווח בוצע.

* **אבטחת מידע ורגישות**: נתוני עובדים זרים כוללים פרטים אישיים רגישים (דרכון, אשרה) וגם נתוני שכר. המערכת צריכה לאבטח מידע זה:

  * הגבלת גישה (כאמור, עובד יראה רק שלו, מעסיק רק שלו).
  * הצפנת מספרי זיהוי רגישים ייתכן (לפחות בהשמה).
  * עמידה בדרישות הגנת הפרטיות.
  * שמירת לוג פעולות – מי צפה/שינה (GDPR אולי לא חובה מקומית אבל רצוי).

לאור כל האמור, מודל הנתונים והלוגיקה של המערכת תוכננו כך שיענו על הדרישות החוקיות: מניעת חריגות, חישוב מדויק של מס וביטוח, הבטחת זכויות העובד (חופשה, מחלה, הבראה) וכן עמידה בחובות המעסיק (הפקדות לפיקדון, דיווחים). בהמשך הפיתוח, חשוב לבצע עדכוני רגולציה שוטפים – למשל עדכון סכומי תקרה בתחילת כל שנה (המפורט בחוזרי מנהל ההגירה), עדכון שווי נקודות זיכוי, עדכון שכר מינימום וכו'. המערכת צריכה להיות גמישה מספיק לעדכונים – דרך טבלאות הגדרות או קונפיגורציה – כדי להישאר כלי אמין לאורך שנים.

תכתוב את כח הדרישות הפונקציונאליות