import {
  PrismaClient,
  Role,
  Sector,
  AgreementType,
  PayslipStatus,
  PayslipItemKod,
  AlertType,
  AlertCategory,
  MaritalStatus,
  AgreementStatus,
  DayType,
  MovementCategory,
  MovementSource,
  DeductionType,
  TaxCalculationType,
  SocialSecurityCalculationType,
} from "@prisma/client";
import bcrypt from "bcryptjs";
import {
  calculateOvertimePay,
  calculateIncomeTax,
  calculateNationalInsurance,
  calculateForeignWorkerDeductions,
  validateMinimumWageCompliance,
  checkVisaExpiryAlerts,
} from "../src/utils/payroll-calculations";
import {
  generateVisaExpiryAlerts,
  generateMissingForm101Alerts,
  generateOvertimeLimitAlerts,
  generateDepositComplianceAlerts,
} from "../src/utils/alert-management";

/**
 * Enhanced seed script with comprehensive payroll scenarios and associations
 * Including attendance agreements and edge cases
 * 
 * IMPORTANT: Before running this seed script, make sure to run the Prisma migrations:
 * npx prisma migrate dev --name add_associations
 * npx prisma generate
 * 
 * This script depends on the following new models:
 * - EmployeeRole
 * - SalaryTemplate
 * - SalaryAgreement
 * - Association
 * - AttendanceAgreement and related models
 */

// Enhanced seed script with comprehensive payroll scenarios
async function main() {
  const prisma = new PrismaClient();

  // Delete all data before seeding
  console.log("🗑️  Deleting all existing data...");
  try {
    // Delete in correct order to respect foreign key constraints
    await prisma.movement.deleteMany();
    await prisma.movementType.deleteMany();
    await prisma.location.deleteMany();
    await prisma.breakRule.deleteMany();
    await prisma.overtimeRule.deleteMany();
    await prisma.shift.deleteMany();
    await prisma.employeeAgreement.deleteMany();
    await prisma.attendanceAgreement.deleteMany();
    await prisma.alert.deleteMany();
    await prisma.payslipItem.deleteMany();
    await prisma.payslip.deleteMany();
    await prisma.form101.deleteMany();
    await prisma.leaveRecord.deleteMany();
    await prisma.bankAccount.deleteMany();
    await prisma.salaryRecord.deleteMany();
    await prisma.association.deleteMany();
    await prisma.employeeRole.deleteMany();
    await prisma.salaryTemplate.deleteMany();
    await prisma.salaryAgreement.deleteMany();
    // await prisma.deductionComponent.deleteMany(); // TODO: Uncomment after schema update
    await prisma.employee.deleteMany();
    await prisma.department.deleteMany();
    await prisma.user.deleteMany();
    await prisma.employer.deleteMany();
    await prisma.tenant.deleteMany();

    console.log("✅ All data deleted successfully");
  } catch (error) {
    console.error("❌ Error deleting data:", error);
    throw error;
  }

  const password = "*********";
  const saltRounds = 10;
  const hashedPassword = await bcrypt.hash(password, saltRounds);

  console.log("🚀 Starting enhanced seed with payroll improvements and attendance agreements...");

  try {
    // Create tenants
    const defaultTenant = await prisma.tenant.upsert({
      where: { name: "default" },
      update: {},
      create: { name: "default", plan: "PREMIUM" }
    });

    const talsTenant = await prisma.tenant.upsert({
      where: { name: "TALS" },
      update: {},
      create: { name: "TALS", plan: "PREMIUM" }
    });

    console.log("✅ Tenants created/updated");

    // Create employers
    const smartchiEmployer = await prisma.employer.upsert({
      where: {
        tenantId_name: {
          tenantId: defaultTenant.id,
          name: "אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע\"מ"
        }
      },
      update: {},
      create: {
        name: "אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע\"מ",
        identifier: "*********",
        companyId: "*********",
        tenantId: defaultTenant.id,
        taxId: "*********",
        niNumber: "*********",
        industry: Sector.CONSTRUCTION,
        payrollDay: 10,
        address: {
          street: "הברזל 30",
          city: "באר שבע",
          country: "ישראל",
          zipCode: "7670000"
        },
        contact: {
          name: "אברהם טורגמן",
          email: "<EMAIL>",
          phone: "050-2466626",
          position: "מנכ\"ל"
        }
      }
    });

    console.log("✅ Employers created");

    // Create departments
    const constructionDept = await prisma.department.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "עובדי בניין זרים"
        }
      },
      update: {},
      create: {
        name: "עובדי בניין זרים",
        code: "CONST_FOREIGN",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת עובדי בניין זרים"
      }
    });

    const adminDept = await prisma.department.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "הנהלה"
        }
      },
      update: {},
      create: {
        name: "הנהלה",
        code: "ADMIN",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        description: "מחלקת הנהלה"
      }
    });

    console.log("✅ Departments created");

    // Create attendance agreements
    const constructionAgreement = await prisma.attendanceAgreement.create({
      data: {
        tenantId: defaultTenant.id,
        code: "CONST_STD",
        name: "הסכם נוכחות עובדי בניין",
        description: "הסכם נוכחות סטנדרטי לעובדי בניין",
        status: AgreementStatus.ACTIVE,
        workDaysPerWeek: 6,
        hoursPerDay: 9,
        monthlyHours: 220,
        overtimeThreshold: 8,
        nightShiftStart: "22:00",
        nightShiftEnd: "06:00",
        weekendDays: [6], // Saturday only
      }
    });

    const officeAgreement = await prisma.attendanceAgreement.create({
      data: {
        tenantId: defaultTenant.id,
        code: "OFFICE_STD",
        name: "הסכם נוכחות עובדי משרד",
        description: "הסכם נוכחות סטנדרטי לעובדי משרד",
        status: AgreementStatus.ACTIVE,
        workDaysPerWeek: 5,
        hoursPerDay: 8,
        monthlyHours: 173,
        overtimeThreshold: 8,
        nightShiftStart: "20:00",
        nightShiftEnd: "06:00",
        weekendDays: [5, 6], // Friday and Saturday
      }
    });

    const flexibleAgreement = await prisma.attendanceAgreement.create({
      data: {
        tenantId: defaultTenant.id,
        code: "FLEX_2024",
        name: "הסכם נוכחות גמיש",
        description: "הסכם נוכחות גמיש למנהלים ועובדים בכירים",
        status: AgreementStatus.ACTIVE,
        workDaysPerWeek: 5,
        hoursPerDay: 0, // Flexible
        monthlyHours: 160,
        overtimeThreshold: 10,
        nightShiftStart: "23:00",
        nightShiftEnd: "05:00",
        weekendDays: [5, 6],
      }
    });

    console.log("✅ Attendance agreements created");

    // Create shifts for construction agreement
    const morningShift = await prisma.shift.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        code: "MORNING",
        name: "משמרת בוקר",
        description: "משמרת בוקר רגילה",
        startTime: "07:00",
        endTime: "16:00",
        breakMinutes: 60,
        isNightShift: false,
        isFlexible: false,
        color: "#4CAF50",
      }
    });

    const afternoonShift = await prisma.shift.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        code: "AFTERNOON",
        name: "משמרת אחה\"צ",
        description: "משמרת אחר הצהריים",
        startTime: "14:00",
        endTime: "23:00",
        breakMinutes: 60,
        isNightShift: false,
        isFlexible: false,
        color: "#FF9800",
      }
    });

    const nightShift = await prisma.shift.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        code: "NIGHT",
        name: "משמרת לילה",
        description: "משמרת לילה",
        startTime: "22:00",
        endTime: "07:00",
        breakMinutes: 60,
        isNightShift: true,
        isFlexible: false,
        color: "#9C27B0",
      }
    });

    console.log("✅ Shifts created");

    // Create overtime rules
    await prisma.overtimeRule.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        name: "שעות נוספות 125% - ימי חול",
        description: "2 שעות ראשונות ביום חול",
        fromHour: 8,
        toHour: 10,
        rate: 1.25,
        dayType: DayType.WEEKDAY,
        priority: 1,
      }
    });

    await prisma.overtimeRule.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        name: "שעות נוספות 150% - ימי חול",
        description: "מהשעה ה-11 ואילך ביום חול",
        fromHour: 10,
        rate: 1.5,
        dayType: DayType.WEEKDAY,
        priority: 2,
      }
    });

    await prisma.overtimeRule.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        name: "שעות שבת 150%",
        description: "כל השעות בשבת",
        fromHour: 0,
        rate: 1.5,
        dayType: DayType.WEEKEND,
        priority: 1,
      }
    });

    await prisma.overtimeRule.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        name: "שעות חג 200%",
        description: "כל השעות בחג",
        fromHour: 0,
        rate: 2.0,
        dayType: DayType.HOLIDAY,
        priority: 1,
      }
    });

    console.log("✅ Overtime rules created");

    // Create break rules
    await prisma.breakRule.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        name: "הפסקת צהריים חובה",
        description: "הפסקת צהריים של 60 דקות לאחר 6 שעות עבודה",
        minWorkHours: 6,
        breakDuration: 60,
        isPaid: false,
        isMandatory: true,
        canBeSplit: false,
      }
    });

    await prisma.breakRule.create({
      data: {
        tenantId: defaultTenant.id,
        agreementId: constructionAgreement.id,
        name: "הפסקות קצרות",
        description: "הפסקות קצרות של 15 דקות",
        minWorkHours: 4,
        breakDuration: 15,
        isPaid: true,
        isMandatory: false,
        canBeSplit: true,
        minSplitDuration: 5,
      }
    });

    console.log("✅ Break rules created");

    // Create locations
    const mainOffice = await prisma.location.create({
      data: {
        tenantId: defaultTenant.id,
        code: "MAIN_OFFICE",
        name: "משרד ראשי",
        description: "המשרד הראשי בבאר שבע",
        address: "הברזל 30, באר שבע",
        latitude: 31.2530,
        longitude: 34.7915,
        radius: 100,
        timezone: "Asia/Jerusalem",
        isActive: true,
      }
    });

    const constructionSite1 = await prisma.location.create({
      data: {
        tenantId: defaultTenant.id,
        code: "SITE_001",
        name: "אתר בנייה - פרויקט מגדלי הים",
        description: "אתר בנייה בתל אביב",
        address: "דרך הים 1, תל אביב",
        latitude: 32.0853,
        longitude: 34.7818,
        radius: 200,
        timezone: "Asia/Jerusalem",
        isActive: true,
      }
    });

    const constructionSite2 = await prisma.location.create({
      data: {
        tenantId: defaultTenant.id,
        code: "SITE_002",
        name: "אתר בנייה - שכונת הפארק",
        description: "אתר בנייה בירושלים",
        address: "דרך בית לחם 50, ירושלים",
        latitude: 31.7683,
        longitude: 35.2137,
        radius: 150,
        timezone: "Asia/Jerusalem",
        isActive: true,
      }
    });

    console.log("✅ Locations created");

    // Create movement types
    const checkIn = await prisma.movementType.create({
      data: {
        tenantId: defaultTenant.id,
        code: "CHECK_IN",
        name: "כניסה",
        description: "דיווח כניסה לעבודה",
        category: MovementCategory.CHECK_IN,
        isDefault: true,
        requiresApproval: false,
        color: "#4CAF50",
        icon: "login",
      }
    });

    const checkOut = await prisma.movementType.create({
      data: {
        tenantId: defaultTenant.id,
        code: "CHECK_OUT",
        name: "יציאה",
        description: "דיווח יציאה מעבודה",
        category: MovementCategory.CHECK_OUT,
        isDefault: true,
        requiresApproval: false,
        color: "#F44336",
        icon: "logout",
      }
    });

    const breakStart = await prisma.movementType.create({
      data: {
        tenantId: defaultTenant.id,
        code: "BREAK_START",
        name: "תחילת הפסקה",
        description: "דיווח תחילת הפסקה",
        category: MovementCategory.BREAK_START,
        isDefault: true,
        requiresApproval: false,
        color: "#FF9800",
        icon: "pause",
      }
    });

    const breakEnd = await prisma.movementType.create({
      data: {
        tenantId: defaultTenant.id,
        code: "BREAK_END",
        name: "סיום הפסקה",
        description: "דיווח סיום הפסקה",
        category: MovementCategory.BREAK_END,
        isDefault: true,
        requiresApproval: false,
        color: "#4CAF50",
        icon: "play",
      }
    });

    console.log("✅ Movement types created");

    // Create deduction components
    const deductionComponents = [
      // מס הכנסה
      {
        code: "TAX_INCOME",
        name: "מס הכנסה",
        description: "מס הכנסה לפי טבלאות מס הכנסה",
        deductionType: DeductionType.INCOME_TAX,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        rewardCode: "101",
        displayOrder: 1,
        group: "MANDATORY",
        isOneTime: false,
        isActive: true,
      },
      // ביטוח לאומי
      {
        code: "NI_EMPLOYEE",
        name: "ביטוח לאומי עובד",
        description: "חלק עובד בביטוח לאומי",
        deductionType: DeductionType.NATIONAL_INSURANCE,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        percentageOfSalary: 3.5,
        rewardCode: "102",
        displayOrder: 2,
        group: "MANDATORY",
        isOneTime: false,
        isActive: true,
      },
      {
        code: "NI_HEALTH",
        name: "ביטוח בריאות",
        description: "דמי ביטוח בריאות ממלכתי",
        deductionType: DeductionType.HEALTH_INSURANCE,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        percentageOfSalary: 5.0,
        rewardCode: "103",
        displayOrder: 3,
        group: "MANDATORY",
        isOneTime: false,
        isActive: true,
      },
      // פנסיה
      {
        code: "PENSION_EMP",
        name: "פנסיה עובד",
        description: "הפרשת עובד לקרן פנסיה",
        deductionType: DeductionType.PENSION,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: true,
        percentageOfSalary: 6.0,
        rewardCode: "104",
        displayOrder: 4,
        group: "PENSION",
        isOneTime: false,
        isActive: true,
      },
      {
        code: "PENSION_COMP",
        name: "פיצויים עובד",
        description: "הפרשת עובד לפיצויים",
        deductionType: DeductionType.PENSION,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: true,
        percentageOfSalary: 0.5,
        rewardCode: "105",
        displayOrder: 5,
        group: "PENSION",
        isOneTime: false,
        isActive: true,
      },
      // קרן השתלמות
      {
        code: "STUDY_FUND_EMP",
        name: "קרן השתלמות עובד",
        description: "הפרשת עובד לקרן השתלמות",
        deductionType: DeductionType.EDUCATION_FUND,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        percentageOfSalary: 2.5,
        rewardCode: "106",
        displayOrder: 6,
        group: "BENEFITS",
        isOneTime: false,
        isActive: true,
      },
      // ניכויים לעובדים זרים
      {
        code: "FW_HOUSING",
        name: "ניכוי מגורים - עובדים זרים",
        description: "ניכוי דמי מגורים לעובדים זרים",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        rewardCode: "201",
        displayOrder: 10,
        group: "FOREIGN_WORKER",
        isOneTime: false,
        isActive: true,
      },
      {
        code: "FW_MEDICAL",
        name: "ביטוח רפואי - עובדים זרים",
        description: "ביטוח רפואי פרטי לעובדים זרים",
        deductionType: DeductionType.HEALTH_INSURANCE,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        rewardCode: "202",
        displayOrder: 11,
        group: "FOREIGN_WORKER",
        isOneTime: false,
        isActive: true,
      },
      // הלוואות
      {
        code: "LOAN_REPAY",
        name: "החזר הלוואה",
        description: "החזר הלוואה מהמעסיק",
        deductionType: DeductionType.LOAN_REPAYMENT,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        rewardCode: "301",
        displayOrder: 20,
        group: "LOANS",
        isOneTime: false,
        isActive: true,
      },
      // ניכויים חד פעמיים
      {
        code: "FINE_LATE",
        name: "קנס איחור",
        description: "קנס על איחור לעבודה",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_LIABLE,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
        affectsPension: false,
        rewardCode: "401",
        displayOrder: 30,
        group: "PENALTIES",
        isOneTime: true,
        isActive: true,
      },
      {
        code: "UNION_FEE",
        name: "דמי חבר ועד",
        description: "דמי חבר בארגון עובדים",
        deductionType: DeductionType.OTHER,
        taxCalculation: TaxCalculationType.TAX_EXEMPT,
        socialSecurityCalculation: SocialSecurityCalculationType.SS_EXEMPT,
        affectsPension: false,
        percentageOfSalary: 0.8,
        rewardCode: "501",
        displayOrder: 40,
        group: "OTHER",
        isOneTime: false,
        isActive: true,
      },
    ];

    // TODO: Uncomment after schema update
    // for (const component of deductionComponents) {
    //   await prisma.deductionComponent.create({
    //     data: {
    //       ...component,
    //       tenantId: defaultTenant.id,
    //     }
    //   });
    // }

    // console.log(`✅ Created ${deductionComponents.length} deduction components`);

    // Create employee roles
    const constructionWorkerRole = await prisma.employeeRole.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "עובד בניין"
        }
      },
      update: {},
      create: {
        name: "עובד בניין",
        description: "עובד בניין זר",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        isActive: true
      }
    });

    const supervisorRole = await prisma.employeeRole.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "מנהל עבודה"
        }
      },
      update: {},
      create: {
        name: "מנהל עבודה",
        description: "מנהל עבודה באתר בנייה",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        isActive: true
      }
    });

    const adminRole = await prisma.employeeRole.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "מנהל אדמיניסטרטיבי"
        }
      },
      update: {},
      create: {
        name: "מנהל אדמיניסטרטיבי",
        description: "מנהל אדמיניסטרטיבי במשרד",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        isActive: true
      }
    });

    console.log("✅ Employee roles created");

    // Create salary templates
    const foreignWorkerTemplate = await prisma.salaryTemplate.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "תבנית שכר עובדים זרים"
        }
      },
      update: {},
      create: {
        name: "תבנית שכר עובדים זרים",
        description: "תבנית שכר בסיסית לעובדים זרים בענף הבנייה",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        isActive: true,
        components: {
          baseSalary: 6200,
          transportAllowance: 300,
          housingDeduction: 1200,
          healthInsuranceDeduction: 250,
          depositPercentage: 20
        }
      }
    });

    const supervisorTemplate = await prisma.salaryTemplate.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "תבנית שכר מנהלי עבודה"
        }
      },
      update: {},
      create: {
        name: "תבנית שכר מנהלי עבודה",
        description: "תבנית שכר למנהלי עבודה",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        isActive: true,
        components: {
          baseSalary: 12000,
          transportAllowance: 500,
          phoneAllowance: 300,
          bonusEligible: true
        }
      }
    });

    const adminTemplate = await prisma.salaryTemplate.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "תבנית שכר עובדי משרד"
        }
      },
      update: {},
      create: {
        name: "תבנית שכר עובדי משרד",
        description: "תבנית שכר לעובדי משרד ומנהלה",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        isActive: true,
        components: {
          baseSalary: 10000,
          transportAllowance: 400,
          phoneAllowance: 200,
          mealAllowance: 800
        }
      }
    });

    console.log("✅ Salary templates created");

    // Create salary agreements
    const constructionSalaryAgreement = await prisma.salaryAgreement.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "הסכם עובדי בניין 2024"
        }
      },
      update: {},
      create: {
        name: "הסכם עובדי בניין 2024",
        description: "הסכם קיבוצי לעובדי בניין",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        effectiveFrom: new Date("2024-01-01"),
        effectiveTo: new Date("2025-12-31"),
        isActive: true,
        terms: {
          minimumWage: 6200,
          hourlyRate: 33.3,
          overtimeMultipliers: {
            first2Hours: 1.25,
            additional: 1.5,
            weekend: 2.0
          },
          benefits: {
            pensionEmployer: 7.5,
            pensionEmployee: 6.0,
            compensationPercent: 8.33
          }
        }
      }
    });

    const officeSalaryAgreement = await prisma.salaryAgreement.upsert({
      where: {
        employerId_name: {
          employerId: smartchiEmployer.id,
          name: "הסכם עובדי משרד 2024"
        }
      },
      update: {},
      create: {
        name: "הסכם עובדי משרד 2024",
        description: "הסכם לעובדי משרד ומנהלה",
        employerId: smartchiEmployer.id,
        tenantId: defaultTenant.id,
        effectiveFrom: new Date("2024-01-01"),
        effectiveTo: new Date("2025-12-31"),
        isActive: true,
        terms: {
          minimumWage: 6500,
          hourlyRate: 35.0,
          overtimeMultipliers: {
            first2Hours: 1.25,
            additional: 1.5,
            weekend: 2.0
          },
          benefits: {
            pensionEmployer: 7.5,
            pensionEmployee: 6.0,
            compensationPercent: 8.33,
            studyFund: 7.5
          }
        }
      }
    });

    console.log("✅ Salary agreements created");

    // Create users
    await prisma.user.upsert({
      where: {
        tenantId_email: {
          tenantId: defaultTenant.id,
          email: "<EMAIL>"
        }
      },
      update: { password: hashedPassword },
      create: {
        email: "<EMAIL>",
        name: "אברהם טורגמן",
        password: hashedPassword,
        role: Role.OWNER,
        tenantId: defaultTenant.id,
        isActive: true
      }
    });

    await prisma.user.upsert({
      where: {
        tenantId_email: {
          tenantId: defaultTenant.id,
          email: "<EMAIL>"
        }
      },
      update: { password: hashedPassword },
      create: {
        email: "<EMAIL>",
        name: "מנהל שכר",
        password: hashedPassword,
        role: Role.ACCOUNTANT,
        tenantId: defaultTenant.id,
        employerId: smartchiEmployer.id,
        isActive: true
      }
    });

    console.log("✅ Users created");

    // Create comprehensive employee test cases with edge cases
    const currentDate = new Date();
    const testEmployees = [
      // Case 1: Foreign worker with visa expiring soon (Critical alert)
      {
        firstName: "Somchai",
        lastName: "Jaidee",
        nationalId: "FW001",
        startDate: new Date("2023-01-15"),
        birthDate: new Date("1985-03-20"),
        contact: { email: "<EMAIL>", phone: "050-1111111" },
        address: { street: "מגורי עובדים א", city: "אשדוד", country: "ישראל", zipCode: "7700000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Thailand",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VTHAI001",
        visaExpiry: new Date(currentDate.getTime() + 25 * 24 * 60 * 60 * 1000), // 25 days from now
        visaType: "B1-Construction",
        baseSalary: 6200, // Just above minimum wage
        workedHours: 220, // Excessive hours
        overtimeHours: { h125: 40, h150: 30, h175: 10, h200: 5 },
        attendanceAgreementId: constructionAgreement.id,
        shiftId: morningShift.id,
      },

      // Case 2: Foreign worker with expired visa (Critical compliance issue)
      {
        firstName: "Wei",
        lastName: "Zhang",
        nationalId: "FW002",
        startDate: new Date("2022-06-01"),
        birthDate: new Date("1990-07-15"),
        contact: { email: "<EMAIL>", phone: "050-2222222" },
        address: { street: "מגורי עובדים ב", city: "נתניה", country: "ישראל", zipCode: "4200000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "China",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.COLLECTIVE,
        visaNumber: "VCHN001",
        visaExpiry: new Date("2024-12-01"), // Already expired
        visaType: "B1-Construction",
        baseSalary: 5500, // Below minimum wage
        workedHours: 186,
        overtimeHours: { h125: 20, h150: 10, h175: 0, h200: 0 },
        attendanceAgreementId: constructionAgreement.id,
        shiftId: afternoonShift.id,
      },

      // Case 3: Night shift worker with night shift premium issues
      {
        firstName: "Giorgi",
        lastName: "Kapanadze",
        nationalId: "FW003",
        startDate: new Date("2024-01-01"),
        birthDate: new Date("1988-11-30"),
        contact: { email: "<EMAIL>", phone: "050-3333333" },
        address: { street: "מגורי עובדים ג", city: "חיפה", country: "ישראל", zipCode: "3300000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Georgia",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VGEO001",
        visaExpiry: new Date("2025-12-31"),
        visaType: "B1-Construction",
        baseSalary: 7800,
        workedHours: 200,
        overtimeHours: { h125: 25, h150: 15, h175: 5, h200: 0 },
        hasForm101: false, // Missing Form 101
        nightShiftHours: 80, // Works night shifts
        attendanceAgreementId: constructionAgreement.id,
        shiftId: nightShift.id,
      },

      // Case 4: Worker with split shifts and complex overtime
      {
        firstName: "Vasile",
        lastName: "Popescu",
        nationalId: "FW004",
        startDate: new Date("2023-03-15"),
        birthDate: new Date("1992-05-10"),
        contact: { email: "<EMAIL>", phone: "050-4444444" },
        address: { street: "מגורי עובדים ד", city: "באר שבע", country: "ישראל", zipCode: "8400000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Moldova",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VMOL001",
        visaExpiry: new Date("2025-08-15"),
        visaType: "B1-Construction",
        baseSalary: 8500,
        workedHours: 190,
        overtimeHours: { h125: 30, h150: 10, h175: 0, h200: 0 },
        depositIssue: true, // Will create insufficient deposit
        splitShift: true, // Works split shifts
        attendanceAgreementId: constructionAgreement.id,
        shiftId: morningShift.id,
      },

      // Case 5: Israeli employee with excessive deductions
      {
        firstName: "משה",
        lastName: "כהן",
        nationalId: "*********",
        startDate: new Date("2022-01-01"),
        birthDate: new Date("1980-01-15"),
        contact: { email: "<EMAIL>", phone: "050-5555555" },
        address: { street: "הרצל 45", city: "תל אביב", country: "ישראל", zipCode: "6100000" },
        departmentId: adminDept.id,
        isForeign: false,
        baseSalary: 15000,
        workedHours: 186,
        overtimeHours: { h125: 10, h150: 5, h175: 0, h200: 0 },
        excessiveDeductions: true, // Will create unusual deductions
        attendanceAgreementId: officeAgreement.id,
      },

      // Case 6: Foreign worker with all compliance issues
      {
        firstName: "Aziz",
        lastName: "Rahimov",
        nationalId: "FW005",
        startDate: new Date("2023-11-01"),
        birthDate: new Date("1995-09-20"),
        contact: { email: "<EMAIL>", phone: "050-6666666" },
        address: { street: "מגורי עובדים ה", city: "אילת", country: "ישראל", zipCode: "8800000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Uzbekistan",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VUZB001",
        visaExpiry: new Date(currentDate.getTime() + 45 * 24 * 60 * 60 * 1000), // 45 days
        visaType: "B1-Construction",
        baseSalary: 5700, // Below minimum
        workedHours: 250, // Excessive
        overtimeHours: { h125: 50, h150: 40, h175: 20, h200: 10 }, // Way over limits
        hasForm101: false,
        depositIssue: true,
        attendanceAgreementId: constructionAgreement.id,
        shiftId: morningShift.id,
      },

      // Case 7: Manager with flexible hours
      {
        firstName: "יוסף",
        lastName: "לוי",
        nationalId: "*********",
        startDate: new Date("2021-06-01"),
        birthDate: new Date("1975-08-22"),
        contact: { email: "<EMAIL>", phone: "050-7777777" },
        address: { street: "רוטשילד 100", city: "תל אביב", country: "ישראל", zipCode: "6100000" },
        departmentId: adminDept.id,
        isForeign: false,
        baseSalary: 25000,
        workedHours: 200, // Flexible hours
        overtimeHours: { h125: 0, h150: 0, h175: 0, h200: 0 }, // No overtime for managers
        attendanceAgreementId: flexibleAgreement.id,
      },

      // Case 8: Part-time worker with irregular hours
      {
        firstName: "Ion",
        lastName: "Gheorghe",
        nationalId: "FW008",
        startDate: new Date("2024-02-01"),
        birthDate: new Date("1987-04-15"),
        contact: { email: "<EMAIL>", phone: "050-8888888" },
        address: { street: "מגורי עובדים ו", city: "רמלה", country: "ישראל", zipCode: "7200000" },
        departmentId: constructionDept.id,
        isForeign: true,
        country: "Romania",
        sector: Sector.CONSTRUCTION,
        agreementType: AgreementType.PERSONAL,
        visaNumber: "VROM001",
        visaExpiry: new Date("2026-01-31"),
        visaType: "B1-Construction",
        baseSalary: 4000, // Part-time salary
        workedHours: 100, // Part-time hours
        overtimeHours: { h125: 5, h150: 2, h175: 0, h200: 0 },
        partTime: true,
        attendanceAgreementId: constructionAgreement.id,
        shiftId: morningShift.id,
      },
    ];

    // Create employees and their payroll data
    for (const empData of testEmployees) {
      const employee = await prisma.employee.create({
        data: {
          tenantId: defaultTenant.id,
          employerId: smartchiEmployer.id,
          firstName: empData.firstName,
          lastName: empData.lastName,
          nationalId: empData.nationalId,
          startDate: empData.startDate,
          birthDate: empData.birthDate,
          contact: empData.contact,
          status: "ACTIVE",
          address: empData.address,
          departmentId: empData.departmentId,
          isForeign: empData.isForeign,
          country: empData.country,
          sector: empData.sector,
          agreementType: empData.agreementType,
          isResidentForNI: !empData.isForeign,
          visaNumber: empData.visaNumber,
          visaExpiry: empData.visaExpiry,
          visaType: empData.visaType,
          baseSalary: empData.baseSalary,
        }
      });

      console.log(`✅ Created employee: ${employee.firstName} ${employee.lastName}`);

      // Create employee agreement association
      if (empData.attendanceAgreementId) {
        await prisma.employeeAgreement.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            agreementId: empData.attendanceAgreementId,
            startDate: empData.startDate,
            notes: `הסכם נוכחות - ${empData.shiftId ? 'משמרת קבועה' : 'שעות גמישות'}`,
          }
        });
      }

      // Create sample movements for the last 3 months
      if (empData.attendanceAgreementId) {
        const movements = [];
        const today = new Date();
        
        // Create movements for the last 90 days
        for (let daysAgo = 90; daysAgo >= 0; daysAgo--) {
          const date = new Date(today);
          date.setDate(date.getDate() - daysAgo);
          
          // Skip weekends for office workers
          if (empData.departmentId === adminDept.id && (date.getDay() === 0 || date.getDay() === 6)) {
            continue;
          }
          
          // Morning check-in
          const checkInTime = new Date(date);
          if (empData.shiftId === nightShift.id) {
            checkInTime.setHours(22, Math.floor(Math.random() * 15), 0, 0);
          } else if (empData.shiftId === afternoonShift.id) {
            checkInTime.setHours(14, Math.floor(Math.random() * 15), 0, 0);
          } else {
            checkInTime.setHours(7, Math.floor(Math.random() * 30), 0, 0);
          }
          
          movements.push({
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            typeId: checkIn.id,
            locationId: empData.departmentId === adminDept.id ? mainOffice.id : constructionSite1.id,
            timestamp: checkInTime,
            source: MovementSource.BIOMETRIC,
            isApproved: true,
          });
          
          // Check-out
          const checkOutTime = new Date(checkInTime);
          const hoursWorked = empData.partTime ? 5 : (empData.nightShiftHours ? 9 : 9);
          checkOutTime.setHours(checkOutTime.getHours() + hoursWorked + 1); // +1 for break
          
          movements.push({
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            typeId: checkOut.id,
            locationId: empData.departmentId === adminDept.id ? mainOffice.id : constructionSite1.id,
            timestamp: checkOutTime,
            source: MovementSource.BIOMETRIC,
            isApproved: true,
          });
        }
        
        // Batch create movements
        if (movements.length > 0) {
          await prisma.movement.createMany({ data: movements });
          console.log(`  ✅ Created ${movements.length} movements for ${employee.firstName} ${employee.lastName}`);
        }
      }

      // Create salary record
      await prisma.salaryRecord.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          basis: "MONTHLY",
          currency: "ILS",
          payFrequency: "MONTHLY",
          amount: empData.baseSalary,
          effectiveFrom: empData.startDate,
          positionType: "MONTHLY",
          positionPercentage: 100,
          standardMonthlyHours: 186,
          workedHours: empData.workedHours,
          hourlyRate: empData.baseSalary / 186,
        }
      });

      // Create bank account
      await prisma.bankAccount.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          bankName: "בנק הפועלים",
          bankCode: 12,
          branchCode: "456",
          branchNumber: 456,
          accountNumber: Math.floor(100000 + Math.random() * 900000).toString(),
          currency: "ILS",
          accountType: "CHECKING",
          isPrimary: true,
        }
      });

      // Create associations
      // Department association
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: "DEPARTMENT",
          departmentId: empData.departmentId,
          startDate: empData.startDate,
          notes: `שיוך מחלקה - ${empData.isForeign ? "עובד זר" : "עובד מקומי"}`,
        }
      });

      // Role association
      const roleId = empData.departmentId === constructionDept.id 
        ? constructionWorkerRole.id 
        : adminRole.id;
      
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: "ROLE",
          roleId: roleId,
          startDate: empData.startDate,
          notes: `שיוך תפקיד - ${roleId === constructionWorkerRole.id ? "עובד בניין" : "מנהל אדמיניסטרטיבי"}`,
        }
      });

      // Salary template association
      const templateId = empData.isForeign 
        ? foreignWorkerTemplate.id 
        : adminTemplate.id;
      
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: "SALARY_TEMPLATE",
          salaryTemplateId: templateId,
          startDate: empData.startDate,
          notes: `שיוך תבנית שכר - ${empData.isForeign ? "עובדים זרים" : "עובדי משרד"}`,
        }
      });

      // Salary agreement association
      const agreementId = empData.departmentId === constructionDept.id 
        ? constructionSalaryAgreement.id 
        : officeSalaryAgreement.id;
      
      await prisma.association.create({
        data: {
          tenantId: defaultTenant.id,
          employeeId: employee.id,
          associationType: "SALARY_AGREEMENT",
          salaryAgreementId: agreementId,
          startDate: empData.startDate,
          notes: `שיוך הסכם שכר - ${agreementId === constructionSalaryAgreement.id ? "הסכם בנייה" : "הסכם משרד"}`,
        }
      });

      console.log(`  ✅ Created associations for ${employee.firstName} ${employee.lastName}`);

      // Create Form 101 if applicable
      if (empData.hasForm101 !== false) {
        await prisma.form101.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            taxYear: 2024,
            maritalStatus: MaritalStatus.SINGLE,
            spouseWorks: false,
            childrenCount: 0,
            isMainEmployer: true,
            hasAdditionalIncome: false,
            signedAt: new Date(empData.startDate.getTime() + 7 * 24 * 60 * 60 * 1000),
          }
        });
      }

      // Create multiple payslips - 2 months back, with May 2025 unapproved
      const months = [
        { year: 2025, month: 3, status: PayslipStatus.PAID },     // March - paid
        { year: 2025, month: 4, status: PayslipStatus.APPROVED }, // April - approved
        { year: 2025, month: 5, status: PayslipStatus.CALCULATED }, // May - calculated (not approved)
      ];

      for (const period of months) {
        const payslipDate = new Date(period.year, period.month - 1, 1);
        const periodEnd = new Date(period.year, period.month, 0);

        // Calculate overtime pay with night shift premium
        const hourlyRate = empData.baseSalary / 186;
        let nightShiftPremium = 0;
        
        if (empData.nightShiftHours) {
          nightShiftPremium = empData.nightShiftHours * hourlyRate * 0.15; // 15% night shift premium
        }

        const overtimeCalc = calculateOvertimePay(
          empData.workedHours - (empData.overtimeHours?.h125 || 0) - (empData.overtimeHours?.h150 || 0) -
          (empData.overtimeHours?.h175 || 0) - (empData.overtimeHours?.h200 || 0),
          empData.overtimeHours?.h125 || 0,
          empData.overtimeHours?.h150 || 0,
          empData.overtimeHours?.h175 || 0,
          empData.overtimeHours?.h200 || 0,
          hourlyRate
        );

        const grossPay = overtimeCalc.totalPay + nightShiftPremium;

        // Calculate tax
        const taxCalc = calculateIncomeTax(
          grossPay,
          empData.isForeign ? 0 : 2.25, // Tax credits
          true,
          0
        );

        // Calculate National Insurance
        const niCalc = calculateNationalInsurance(
          grossPay,
          !empData.isForeign,
          new Date().getFullYear() - (empData.birthDate?.getFullYear() || 1990)
        );

        // Calculate foreign worker deductions
        let foreignWorkerDeductions = {
          housingDeduction: 0,
          transportDeduction: 0,
          healthInsuranceDeduction: 0,
          totalDeductions: 0
        };

        if (empData.isForeign && empData.sector) {
          foreignWorkerDeductions = calculateForeignWorkerDeductions(
            empData.country || "",
            empData.sector,
            grossPay,
            empData.address.zipCode
          );
        }

                 // Create payslip items
         const payslipItems: Array<{
           description: string;
           amount: number;
           type: "EARNING" | "DEDUCTION" | "EMPLOYER_CONTRIB" | "REIMBURSEMENT";
           kod?: PayslipItemKod;
           units?: number;
           rate?: number;
           percentage?: number;
           isDebit: boolean;
         }> = [
           {
             description: "שכר בסיס",
             amount: empData.baseSalary,
             type: "EARNING",
             kod: PayslipItemKod.K_0100,
             isDebit: false,
           },
         ];

        // Add night shift premium if applicable
        if (nightShiftPremium > 0) {
          payslipItems.push({
            description: "תוספת משמרת לילה",
            amount: nightShiftPremium,
            type: "EARNING",
            kod: PayslipItemKod.K_0170,
            isDebit: false,
          });
        }

        // Add overtime items
        if (overtimeCalc.overtime125Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 125%",
            amount: overtimeCalc.overtime125Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1001,
            units: empData.overtimeHours?.h125 || 0,
            rate: hourlyRate * 1.25,
            percentage: 125,
            isDebit: false,
          });
        }

        if (overtimeCalc.overtime150Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 150%",
            amount: overtimeCalc.overtime150Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1002,
            units: empData.overtimeHours?.h150 || 0,
            rate: hourlyRate * 1.50,
            percentage: 150,
            isDebit: false,
          });
        }

        if (overtimeCalc.overtime175Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 175%",
            amount: overtimeCalc.overtime175Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1003,
            units: empData.overtimeHours?.h175 || 0,
            rate: hourlyRate * 1.75,
            percentage: 175,
            isDebit: false,
          });
        }

        if (overtimeCalc.overtime200Pay > 0) {
          payslipItems.push({
            description: "שעות נוספות 200%",
            amount: overtimeCalc.overtime200Pay,
            type: "EARNING",
            kod: PayslipItemKod.K_1004,
            units: empData.overtimeHours?.h200 || 0,
            rate: hourlyRate * 2.00,
            percentage: 200,
            isDebit: false,
          });
        }

        // Add deductions
        payslipItems.push({
          description: "מס הכנסה",
          amount: taxCalc.finalTax,
          type: "DEDUCTION",
          kod: PayslipItemKod.K_TAX,
          isDebit: true,
        });

        payslipItems.push({
          description: "ביטוח לאומי עובד",
          amount: niCalc.employeeNI,
          type: "DEDUCTION",
          kod: PayslipItemKod.K_NI_EMP,
          isDebit: true,
        });

        payslipItems.push({
          description: "ביטוח בריאות",
          amount: niCalc.healthInsurance,
          type: "DEDUCTION",
          kod: PayslipItemKod.K_NI_HEALTH,
          isDebit: true,
        });

        // Foreign worker specific deductions
        if (empData.isForeign) {
          payslipItems.push({
            description: "ניכוי מגורים",
            amount: foreignWorkerDeductions.housingDeduction,
            type: "DEDUCTION",
            kod: PayslipItemKod.K_1020,
            isDebit: true,
          });

          payslipItems.push({
            description: "ניכוי ביטוח רפואי",
            amount: foreignWorkerDeductions.healthInsuranceDeduction,
            type: "DEDUCTION",
            kod: PayslipItemKod.K_1021,
            isDebit: true,
          });
        }

        // Add excessive deductions for test case
        if (empData.excessiveDeductions) {
          payslipItems.push({
            description: "קנס איחורים",
            amount: grossPay * 0.25, // 25% - unusual
            type: "DEDUCTION",
            isDebit: true,
          });

          payslipItems.push({
            description: "החזר הלוואה",
            amount: grossPay * 0.20, // 20% - high
            type: "DEDUCTION",
            isDebit: true,
          });
        }

        // Calculate totals
        const totalDeductions = payslipItems
          .filter(item => item.isDebit)
          .reduce((sum, item) => sum + item.amount, 0);

        let netPay = grossPay - totalDeductions;

        // Handle deposit for foreign workers
        let depositAmount = 0;
        if (empData.isForeign) {
          depositAmount = empData.depositIssue ? grossPay * 0.05 : grossPay * 0.20; // 5% if issue, 20% normal
          netPay -= depositAmount;
        }

        // Create payslip with edge cases
        const payslip = await prisma.payslip.create({
          data: {
            tenantId: defaultTenant.id,
            employeeId: employee.id,
            year: period.year,
            month: period.month,
            periodStart: payslipDate,
            periodEnd: periodEnd,
            status: period.status,
            grossPay: grossPay,
            netPay: Math.max(0, netPay), // Ensure non-negative
            taxDeducted: taxCalc.finalTax,
            insuranceDeducted: niCalc.employeeNI + niCalc.healthInsurance,
            otherDeductions: totalDeductions - taxCalc.finalTax - niCalc.employeeNI - niCalc.healthInsurance,
            healthInsurance: empData.isForeign ? foreignWorkerDeductions.healthInsuranceDeduction : undefined,
            pensionEmployee: empData.isForeign ? grossPay * 0.06 : undefined,
            pensionEmployer: empData.isForeign ? grossPay * 0.065 : undefined,
            severancePay: empData.isForeign ? grossPay * 0.0833 : undefined,
            netDeposit: depositAmount > 0 ? depositAmount : undefined,
            currency: "ILS",
            items: {
              create: payslipItems
            }
          }
        });

        console.log(`  📄 Created payslip for ${period.month}/${period.year} - Status: ${period.status}`);
      }
    }

    // Generate alerts based on the data
    console.log("\n🚨 Generating compliance alerts...");

    // Get all employees with their related data
    const allEmployees = await prisma.employee.findMany({
      where: { tenantId: defaultTenant.id },
      include: {
        form101s: true,
        payslips: {
          where: { year: 2025, month: 1 },
          include: { items: true }
        }
      }
    });

    // Generate visa expiry alerts
    const visaAlerts = generateVisaExpiryAlerts(
      allEmployees.map(emp => ({
        id: emp.id,
        firstName: emp.firstName,
        lastName: emp.lastName,
        isForeign: emp.isForeign,
        visaExpiry: emp.visaExpiry || undefined,
        country: emp.country || undefined,
      }))
    );

         // Generate Form 101 alerts
     const form101Alerts = generateMissingForm101Alerts(
       allEmployees.map(emp => ({
         id: emp.id,
         firstName: emp.firstName,
         lastName: emp.lastName,
         startDate: emp.startDate,
         form101: emp.form101s?.[0] ? {
           id: emp.form101s[0].id,
           signedAt: emp.form101s[0].signedAt || undefined
         } : null,
       }))
     );

    // Generate overtime alerts
    const overtimeAlerts = generateOvertimeLimitAlerts(
      allEmployees.flatMap(emp =>
        emp.payslips.map(payslip => ({
          employeeId: emp.id,
          employeeName: `${emp.firstName} ${emp.lastName}`,
          year: payslip.year,
          month: payslip.month,
          items: payslip.items.map(item => ({
            kod: item.kod || undefined,
            units: item.units ? Number(item.units) : undefined,
            percentage: item.percentage ? Number(item.percentage) : undefined,
          }))
        }))
      )
    );

    // Generate deposit compliance alerts
    const depositAlerts = generateDepositComplianceAlerts(
      allEmployees.map(emp => ({
        id: emp.id,
        firstName: emp.firstName,
        lastName: emp.lastName,
        isForeign: emp.isForeign,
        grossSalary: Number(emp.baseSalary || 0),
        netDeposit: emp.payslips[0]?.netDeposit ? Number(emp.payslips[0].netDeposit) : undefined,
        country: emp.country || undefined,
      }))
    );

    // Create alerts in database
    const allAlerts = [...visaAlerts, ...form101Alerts, ...overtimeAlerts, ...depositAlerts];

    for (const alert of allAlerts) {
             await prisma.alert.create({
         data: {
           tenantId: defaultTenant.id,
           employeeId: alert.employeeId,
           type: alert.type,
           category: alert.category,
           message: alert.message,
           context: alert.context as any, // Prisma JSON field
           dueDate: alert.dueDate,
           severity: alert.severity,
           isRead: false,
           isResolved: false,
         }
       });
    }

    console.log(`✅ Created ${allAlerts.length} compliance alerts`);

    // Summary report
    console.log("\n📊 Enhanced Seed Summary:");
    console.log("================");
    console.log(`Tenants: 2`);
    console.log(`Employers: 1`);
    console.log(`Departments: 2`);
    console.log(`Attendance Agreements: 3`);
    console.log(`Shifts: 3`);
    console.log(`Overtime Rules: 4`);
    console.log(`Break Rules: 2`);
    console.log(`Locations: 3`);
    console.log(`Movement Types: 4`);
    console.log(`Employee Roles: 3`);
    console.log(`Salary Templates: 3`);
    console.log(`Salary Agreements: 2`);
    console.log(`Deduction Components: ${deductionComponents.length}`);
    console.log(`Employees: ${testEmployees.length}`);
    console.log(`Payslips: ${testEmployees.length * 3}`);
    console.log(`Movements: ~${testEmployees.length * 90 * 2}`);
    console.log("\n🎯 Test Scenarios Created:");
    console.log("- Visa expiring soon (Critical)");
    console.log("- Expired visa");
    console.log("- Missing Form 101 (90+ days)");
    console.log("- Deposit compliance issues");
    console.log("- Excessive overtime hours");
    console.log("- Below minimum wage");
    console.log("- Night shift workers with premium");
    console.log("- Split shift workers");
    console.log("- Part-time workers");
    console.log("- Flexible hours managers");
    console.log("- May 2025 payslips not approved (edge case)");
    console.log("- 90 days of attendance movements");

    console.log("\n✅ Enhanced seed with attendance agreements completed successfully!");
    console.log(`\n🔑 Login credentials: password = ${password}`);

  } catch (error) {
    console.error("❌ Error in enhanced seed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => console.log("\n🎉 Enhanced seed completed!"))
  .catch((e) => {
    console.error("💥 Enhanced seed failed:", e);
    process.exit(1);
  });