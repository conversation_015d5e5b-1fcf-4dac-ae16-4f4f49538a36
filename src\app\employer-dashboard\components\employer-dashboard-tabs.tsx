"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";
import { UsersTable } from "@/app/owner-dashboard/components/UsersTable";

import { AuditLogTable } from "@/app/owner-dashboard/components/audit-log-table";
import type { Report, AuditLog } from "@/types";
import type { User, UserFormValues } from "@/app/owner-dashboard/components/UsersTable/types";
import { ReportsList } from "@/app/owner-dashboard/components/reports-list";

export type EmployerDashboardTabsProps = {
  users?: User[];
  isUsersLoading: boolean;
  usersPage: number;
  setUsersPage: (page: number) => void;
  createUser?: (data: UserFormValues) => Promise<void>;
  isCreatingUser?: boolean;

  reports?: Report[];
  isReportsLoading: boolean;
  generateReport?: (data: { reportId: string }) => void;
  isGeneratingReport?: boolean;

  logs?: <PERSON><PERSON><PERSON><PERSON>[];
  isLogsLoading: boolean;
  auditLogsPage: number;
  setAuditLogsPage: (page: number) => void;
  auditLogsActionFilter: "all" | "create" | "update" | "delete";
  setAuditLogsActionFilter: (value: "all" | "create" | "update" | "delete") => void;

  isLoading?: boolean;
  visibleTabs?: ("users" | "reports" | "logs" | "associations")[];
};

export function EmployerDashboardTabs({
  users,
  isUsersLoading,
  usersPage,
  setUsersPage,
  createUser,
  isCreatingUser,

  reports,
  isReportsLoading,
  generateReport,
  isGeneratingReport,

  logs,
  isLogsLoading,
  auditLogsPage,
  setAuditLogsPage,
  auditLogsActionFilter,
  setAuditLogsActionFilter,

  isLoading = false,
  visibleTabs = ["users", "reports", "logs"],
}: EmployerDashboardTabsProps) {
  if (isLoading) {
    return <EmployerDashboardTabsSkeleton visibleTabs={visibleTabs} />;
  }

  return (
    <Tabs defaultValue={visibleTabs[0]} className="w-full">
      <TabsList>
        {visibleTabs.includes("users") && <TabsTrigger value="users">משתמשים</TabsTrigger>}
        {visibleTabs.includes("reports") && <TabsTrigger value="reports">דוחות</TabsTrigger>}
        {visibleTabs.includes("logs") && <TabsTrigger value="logs">יומני ביקורת</TabsTrigger>}
        {visibleTabs.includes("associations") && <TabsTrigger value="associations">שיוכים</TabsTrigger>}
      </TabsList>

      {visibleTabs.includes("users") && (
        <TabsContent value="users">
          <UsersTable
            users={users}
            isLoading={isUsersLoading}
            createUser={createUser}
            isCreating={isCreatingUser}
          />
        </TabsContent>
      )}

      {visibleTabs.includes("reports") && (
        <TabsContent value="reports">
          <ReportsList
            reports={reports}
            isLoading={isReportsLoading}
            generateReport={generateReport}
            isGenerating={isGeneratingReport}
          />
        </TabsContent>
      )}

      {visibleTabs.includes("logs") && (
        <TabsContent value="logs">
          <AuditLogTable
            logs={logs}
            isLoading={isLogsLoading}
          />
        </TabsContent>
      )}

      {visibleTabs.includes("associations") && (
        <TabsContent value="associations">
          <div className="p-4 text-center">
            <a href="/employer-dashboard/associations" className="text-blue-600 hover:underline">
              פתח מסך ניהול שיוכים
            </a>
          </div>
        </TabsContent>
      )}
    </Tabs>
  );
}

function EmployerDashboardTabsSkeleton({ visibleTabs }: { visibleTabs: string[] }) {
  return (
    <div className="w-full">
      <div className="mb-4 flex border-b border-gray-200">
        {visibleTabs.map((tab) => (
          <Skeleton key={tab} className="mx-1 h-10 w-24" />
        ))}
      </div>
      <div className="p-4">
        <div className="mb-4 flex justify-between">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-9 w-32" />
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="h-12 w-full" />
          ))}
        </div>
      </div>
    </div>
  );
}
