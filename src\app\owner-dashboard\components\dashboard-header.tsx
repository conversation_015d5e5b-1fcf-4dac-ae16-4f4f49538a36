"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { useDashboardStore } from "../hooks/useDashboardStore";

type DashboardHeaderProps = {
  isLoading?: boolean;
  refreshData: () => void;
};

export function DashboardHeader({ isLoading = false, refreshData }: DashboardHeaderProps) {
  const { selectedPeriod, setSelectedPeriod } = useDashboardStore();
  if (isLoading) {
    return <DashboardHeaderSkeleton />;
  }
  
  return (
    <header className="flex justify-between items-center mb-2">
      <h1 className="text-3xl font-bold tracking-tight">לוח בקרת מנהל מערכת</h1>
    </header>
  );
}

function DashboardHeaderSkeleton() {
  return (
    <header className="flex justify-between items-center mb-2">
      <Skeleton className="h-10 w-64" />
    </header>
  );
} 