"use client";

import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/rtl-components";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";
import type { AuditLog } from "@/types";
import { useDashboardStore } from "../hooks/useDashboardStore";

type AuditLogTableProps = {
  logs?: AuditLog[];
  isLoading: boolean;
};

export function AuditLogTable({ logs, isLoading }: AuditLogTableProps) {
  const {
    auditLogsPage: page,
    setAuditLogsPage: setPage,
    auditLogsActionFilter: actionFilter,
    setAuditLogsActionFilter: setActionFilter,
  } = useDashboardStore();
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">יומני ביקורת</h2>
        <Select 
          defaultValue="all"
          value={actionFilter}
          onValueChange={(value: "all" | "create" | "update" | "delete") => setActionFilter(value)}
        >
          <SelectTrigger className="w-40">
            <SelectValue placeholder="סינון" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">כל הפעולות</SelectItem>
            <SelectItem value="create">יצירה</SelectItem>
            <SelectItem value="update">עדכון</SelectItem>
            <SelectItem value="delete">מחיקה</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <Card>
        <CardContent className="p-0">
          {isLoading ? (
            <AuditLogTableSkeleton />
          ) : logs && logs.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>זמן</TableHead>
                  <TableHead>משתמש</TableHead>
                  <TableHead>מעסיק</TableHead>
                  <TableHead>פעולה</TableHead>
                  <TableHead>ישות</TableHead>
                  <TableHead>פרטים</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{new Date(log.timestamp).toLocaleString("he-IL")}</TableCell>
                    <TableCell>{log.userEmail}</TableCell>
                    <TableCell>{log.employerName}</TableCell>
                    <TableCell>
                      {log.actionType === "create" && "יצירה"}
                      {log.actionType === "update" && "עדכון"}
                      {log.actionType === "delete" && "מחיקה"}
                    </TableCell>
                    <TableCell>{log.entityType}</TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm">הצג</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="p-4">אין יומני ביקורת זמינים</div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between py-4">
          <Button 
            variant="outline"
            onClick={() => setPage(Math.max(1, page - 1))}
            disabled={page === 1}
          >
            הקודם
          </Button>
          <Button 
            variant="outline"
            onClick={() => setPage(page + 1)}
          >
            הבא
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

function AuditLogTableSkeleton() {
  return (
    <div className="p-4">
      <div className="flex flex-col space-y-3">
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </div>
      </div>
    </div>
  );
} 