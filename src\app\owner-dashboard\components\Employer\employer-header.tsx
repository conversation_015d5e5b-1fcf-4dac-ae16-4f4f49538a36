"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { ArrowRight, Send, Edit, Trash, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/rtl-components";
import { Badge } from "@/components/ui/rtl-components";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useEmployer } from "@/app/owner-dashboard/hooks/useEmployer";
import { toast } from "sonner";
import { type Employer } from "@/schema/employer";
interface EmployerHeaderProps {
  employer: Employer;
}

export function EmployerHeader({ employer }: EmployerHeaderProps) {
  const params = useParams();
  const router = useRouter();
  const employerId = params["employer-id"] as string;
  const { deleteEmployer, isDeleting } = useEmployer(employerId);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteInitiated, setDeleteInitiated] = useState(false);
  const [deleteError, setDeleteError] = useState<Error | null>(null);

  const handleDelete = async () => {
    try {
      // Reset any previous errors
      setDeleteError(null);
      setDeleteInitiated(true);
      
      // Make sure we have a valid ID
      if (!employerId) {
        const error = new Error("Missing employer ID");
        console.error("Error deleting employer:", error);
        setDeleteError(error);
        setDeleteInitiated(false);
        toast.error("Failed to delete employer: Missing ID");
        return;
      }
      
      // Call the delete function with proper ID and await its completion
      await deleteEmployer({ id: employerId });
      setShowDeleteDialog(false);
    } catch (error) {
      console.error("Error in delete handler:", error);
      setDeleteError(error instanceof Error ? error : new Error(String(error)));
      setDeleteInitiated(false);
      toast.error("Failed to delete employer. Please try again.");
    }
  };

  // Effect for handling redirection after successful deletion
  useEffect(() => {
    // Only redirect on successful deletion (deleteInitiated = true, isDeleting = false, no error)
    if (deleteInitiated && !isDeleting && !deleteError) {
      setDeleteError(null);
      // Redirect when the deletion is complete
      router.push("/owner-dashboard/employers");
    }
  }, [deleteInitiated, isDeleting, router, deleteError]);

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 space-x-reverse">
          <Link href="/owner-dashboard/employers">
            <Button variant="ghost" className="p-0">
              <ArrowRight className="me-2 h-4 w-4" />
              חזרה לרשימת מעסיקים
            </Button>
          </Link>
          <span className="text-muted-foreground">/</span>
          <h1 className="text-3xl font-bold tracking-tight">{employer.name}</h1>
          <Badge variant={employer.status === "active" ? "default" : "secondary"} className="ms-2">
            {employer.status === "active" ? "פעיל" : "לא פעיל"}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Send className="me-2 h-4 w-4" />
            שליחת עדכון
          </Button>
          <Link href={`/owner-dashboard/employers/${employer.id}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="me-2 h-4 w-4" />
              עריכה
            </Button>
          </Link>
          <Button 
            variant="destructive" 
            size="sm" 
            onClick={() => setShowDeleteDialog(true)}
            disabled={isDeleting}
          >
            <Trash className="me-2 h-4 w-4" />
            מחיקה
          </Button>
        </div>
      </div>

      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              אישור מחיקת מעסיק
            </DialogTitle>
            <DialogDescription>
              האם אתה בטוח שברצונך למחוק את המעסיק &quot;{employer.name}&quot;? פעולה זו אינה ניתנת לביטול.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:justify-start">
            <Button
              type="button"
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting ? "מוחק..." : "אישור מחיקה"}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={isDeleting}
            >
              ביטול
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
} 