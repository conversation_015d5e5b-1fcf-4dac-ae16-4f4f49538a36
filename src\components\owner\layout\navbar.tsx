"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  Building2, 
  Calendar, 
  ChevronDown, 
  HelpCircle, 
  LogOut, 
  Menu, 
  MessageSquareText, 
  Settings, 
  User, 
  X, 
  FileEdit,
  Bell,
  RefreshCw
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/rtl-components";
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/rtl-components";

interface NavbarProps {
  userName?: string;
}

export default function OwnerNavbar({ userName = "משתמש מערכת" }: NavbarProps) {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>("4");
  const [selectedYear, setSelectedYear] = useState<string>("2023");
  
  const months = [
    { value: "1", label: "ינואר" },
    { value: "2", label: "פברואר" },
    { value: "3", label: "מרץ" },
    { value: "4", label: "אפריל" },
    { value: "5", label: "מאי" },
    { value: "6", label: "יוני" },
    { value: "7", label: "יולי" },
    { value: "8", label: "אוגוסט" },
    { value: "9", label: "ספטמבר" },
    { value: "10", label: "אוקטובר" },
    { value: "11", label: "נובמבר" },
    { value: "12", label: "דצמבר" },
  ];
  
  const years = ["2023", "2024", "2025", "2026"];
  
  const navLinks = [
    { href: "/owner-dashboard", label: "דאשבורד", icon: <Building2 className="h-5 w-5" /> },
    { href: "/owner-dashboard/employers", label: "מעסיקים", icon: <Building2 className="h-5 w-5" /> },
    { href: "/owner-dashboard/employer/update", label: "עדכון מעסיק", icon: <FileEdit className="h-5 w-5" /> },
    { href: "/owner-dashboard/preferences", label: "העדפות", icon: <Settings className="h-5 w-5" /> },
    { href: "/owner-dashboard/support", label: "תמיכה", icon: <MessageSquareText className="h-5 w-5" /> },
    { href: "/owner-dashboard/updates", label: "עדכונים וחדשות", icon: <Bell className="h-5 w-5" /> },
  ];

  const isActive = (path: string) => pathname === path;

  return (
    <nav dir="rtl" className="bg-background border-b border-border sticky top-0 z-50">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and mobile menu button */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center text-primary font-semibold text-lg">
              <span className="hidden md:block">מערכת שכר חכמה</span>
              <span className="block md:hidden">שכר</span>
            </div>
            <div className="hidden md:mr-6 md:flex md:items-center md:space-x-4 md:space-x-reverse rtl:space-x-reverse">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`px-3 py-2 rounded-md text-sm font-medium flex items-center ${
                    isActive(link.href)
                      ? "bg-primary/10 text-primary"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  }`}
                >
                  <span className="ml-2">{link.icon}</span>
                  {link.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Right-side controls for desktop */}
          <div className="hidden md:flex items-center gap-4">
            {/* Period Selector */}
            <div className="flex items-center gap-2">
              <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                <SelectTrigger className="w-28">
                  <SelectValue placeholder="חודש" />
                </SelectTrigger>
                <SelectContent>
                  {months.map((month) => (
                    <SelectItem key={month.value} value={month.value}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger className="w-24">
                  <SelectValue placeholder="שנה" />
                </SelectTrigger>
                <SelectContent>
                  {years.map((year) => (
                    <SelectItem key={year} value={year}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <div className="flex flex-col">
                <Button variant="ghost" size="sm" className="h-6 px-1">
                  <ChevronDown className="h-4 w-4 rotate-180" />
                </Button>
                <Button variant="ghost" size="sm" className="h-6 px-1">
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </div>
              
              <Button variant="outline" size="icon" className="ml-2">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
            
            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/avatars/user.png" alt={userName} />
                    <AvatarFallback>{userName.charAt(0)}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel className="text-right">{userName}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-right cursor-pointer">
                  <User className="ml-2 h-4 w-4" />
                  <span>פרופיל</span>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-right cursor-pointer">
                  <Settings className="ml-2 h-4 w-4" />
                  <span>הגדרות</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-right cursor-pointer">
                  <LogOut className="ml-2 h-4 w-4" />
                  <span>יציאה</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Mobile menu button */}
          <div className="flex md:hidden items-center">
            <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="ml-2">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-72">
                <div className="flex flex-col h-full">
                  <div className="flex justify-between items-center py-4">
                    <div className="text-lg font-semibold">תפריט</div>
                    <Button variant="ghost" size="icon" onClick={() => setMobileMenuOpen(false)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    {navLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        onClick={() => setMobileMenuOpen(false)}
                        className={`px-3 py-3 rounded-md text-sm font-medium flex items-center ${
                          isActive(link.href)
                            ? "bg-primary/10 text-primary"
                            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                        }`}
                      >
                        <span className="ml-3">{link.icon}</span>
                        {link.label}
                      </Link>
                    ))}
                  </div>
                  
                  <div className="mt-4 border-t border-border pt-4">
                    <div className="flex flex-col gap-2 mb-4">
                      <span className="text-sm font-medium">בחירת תקופה</span>
                      <div className="flex gap-2">
                        <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="חודש" />
                          </SelectTrigger>
                          <SelectContent>
                            {months.map((month) => (
                              <SelectItem key={month.value} value={month.value}>
                                {month.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        
                        <Select value={selectedYear} onValueChange={setSelectedYear}>
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="שנה" />
                          </SelectTrigger>
                          <SelectContent>
                            {years.map((year) => (
                              <SelectItem key={year} value={year}>
                                {year}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-auto border-t border-border pt-4">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src="/avatars/user.png" alt={userName} />
                        <AvatarFallback>{userName.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">{userName}</span>
                        <Link 
                          href="/logout" 
                          className="text-xs text-muted-foreground hover:text-primary"
                        >
                          יציאה
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
