"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogHeader,
	<PERSON><PERSON>Title,
	DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectTrigger,
	SelectValue,
	SelectContent,
	SelectItem,
} from "@/components/ui/select";
import { useCreateFormula, useUpdateFormula } from "../hooks";

const formulaSchema = z.object({
	id: z.string().optional(),
	name: z.string().min(1),
	description: z.string().optional(),
	type: z.enum(["TAX", "DEDUCTION", "PENSION", "BENEFIT", "OTHER"]),
	startDate: z.string(),
	endDate: z.string().optional(),
	status: z.enum(["ACTIVE", "INACTIVE", "DRAFT"]),
	formulaCode: z.string().min(1),
});

export type FormulaFormValues = z.infer<typeof formulaSchema>;

interface FormulaFormModalProps {
	open: boolean;
	onClose: () => void;
	initialData?: FormulaFormValues;
}

export function FormulaFormModal({
	open,
	onClose,
	initialData,
}: FormulaFormModalProps) {
	const createFormula = useCreateFormula();
	const updateFormula = useUpdateFormula();

	const {
		register,
		handleSubmit,
		formState: { errors },
	} = useForm<FormulaFormValues>({
		resolver: zodResolver(formulaSchema),
		defaultValues: initialData ?? {
			name: "",
			description: "",
			type: "TAX",
			startDate: "",
			endDate: "",
			status: "DRAFT",
			formulaCode: "",
		},
	});

	const onSubmit = (values: FormulaFormValues) => {
		if (values.id) {
			updateFormula.mutate(values as FormulaFormValues & { id: string }, {
				onSuccess: onClose,
			});
		} else {
			createFormula.mutate(values, { onSuccess: onClose });
		}
	};

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="sm:max-w-lg rtl:text-right">
				<DialogHeader>
					<DialogTitle>
						{initialData ? "עריכת נוסחה" : "הוספת נוסחה"}
					</DialogTitle>
				</DialogHeader>
				<form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
					<Input placeholder="שם הנוסחה" {...register("name")} />
					{errors.name && <p className="text-red-600 text-sm">שדה חובה</p>}
					<Textarea placeholder="תיאור" {...register("description")} />
					<Select
						defaultValue={initialData?.type ?? "TAX"}
						onValueChange={(val) => {
							(register("type").onChange as any)({ target: { value: val } });
						}}
					>
						<SelectTrigger>
							<SelectValue placeholder="סוג" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="TAX">חישוב מס</SelectItem>
							<SelectItem value="DEDUCTION">חישוב ניכוי</SelectItem>
							<SelectItem value="PENSION">חישוב פנסיה</SelectItem>
							<SelectItem value="BENEFIT">חישוב זכאות</SelectItem>
							<SelectItem value="OTHER">אחר</SelectItem>
						</SelectContent>
					</Select>
					<div className="flex gap-2">
						<Input type="month" className="flex-1" {...register("startDate")} />
						<Input type="month" className="flex-1" {...register("endDate")} />
					</div>
					<Select
						defaultValue={initialData?.status ?? "DRAFT"}
						onValueChange={(val) => {
							(register("status").onChange as any)({ target: { value: val } });
						}}
					>
						<SelectTrigger>
							<SelectValue placeholder="סטטוס" />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value="ACTIVE">פעיל</SelectItem>
							<SelectItem value="INACTIVE">לא פעיל</SelectItem>
							<SelectItem value="DRAFT">טיוטה</SelectItem>
						</SelectContent>
					</Select>
					<Textarea
						placeholder="קוד נוסחה"
						className="font-mono"
						rows={6}
						{...register("formulaCode")}
					/>
					<DialogFooter>
						<Button type="submit">{initialData ? "שמירה" : "הוספה"}</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
