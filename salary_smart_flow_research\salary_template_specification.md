# מפרט מסך תבנית שכר (Salary Template)

## מטרת המסך
מסך תבנית שכר מאפשר למנהל המערכת להגדיר, לערוך ולנהל תבניות שכר שונות עבור קבוצות עובדים. תבניות אלה מגדירות את מבנה השכר, רכיבי התשלום, הניכויים, הזכאויות והחישובים הרלוונטיים לחישוב שכר העובדים.

## מבנה המסך

### כותרת ראשית
- **כותרת:** "תבנית שכר"
- **כפתורי פעולה ראשיים:**
  - "הוספת תבנית שכר" - ליצירת תבנית חדשה
  - "בחירת תבנית שכר" - לב<PERSON><PERSON>רת תבנית קיימת

### אזור פרטי תבנית
- **שם תבנית שכר** - שדה טקסט להזנת שם התבנית
- **תנית בחירת תבנית** - שדה בחירה מרשימה
- **קבלן** - שדה בחירה מרשימה (אופציונלי)
- **כפתור רשימה** - להצגת רשימת תבניות קיימות

### תיבות סימון להגדרות מיוחדות
- **שכר מרצים** - תיבת סימון להגדרת תבנית כמיועדת למרצים
- **הטבה אילת למעסיק** - תיבת סימון להגדרת הטבת מס אילת
- **הגדרת מרכיבים המהווים בסיס...** - תיבת סימון להגדרות בסיס חישוב

### אזור צורת חישוב משכורת קבועה
- **כותרת:** "צורת חישוב משכורת קבועה: סעיף 14"
- **שדות הגדרה:**
  - שדה בחירה לסוג חישוב
  - אפשרות להגדרת אחוזים או סכומים קבועים

### אזור קופות גמל
- **כפתור:** "הוספת קופת גמל" - להוספת קופת גמל חדשה לתבנית
- **טבלת קופות גמל** עם העמודות:
  - מרכיב ניכוי - שם מרכיב הניכוי
  - קבלן - שם הקבלן הרלוונטי
  - שיעור/סכום - סכום או אחוז הניכוי
  - כמות - ערך כמותי
  - סכום - סכום כספי

### אזור תשלומים
- **כותרת:** "תשלומים"
- **טבלת תשלומים** עם העמודות:
  - מרכיב תשלום - שם מרכיב התשלום
  - קבלן - שם הקבלן הרלוונטי
  - מילוי - אפשרויות מילוי אוטומטי
  - סכום - סכום כספי
  - כמות - ערך כמותי
  - שיעור/סכום - סכום או אחוז התשלום
  - פעיל - סימון האם המרכיב פעיל

### אזור זקיפות שווי
- **כותרת:** "זקיפות שווי"
- **טבלת זקיפות שווי** עם העמודות:
  - מרכיב שווי - שם מרכיב השווי
  - קבלן - שם הקבלן הרלוונטי
  - שיעור/סכום - סכום או אחוז השווי
  - כמות - ערך כמותי
  - סכום - סכום כספי
  - פיצוי - סימון האם יש פיצוי
  - גילום - סימון האם יש גילום
  - ניכויים נטו - סימון לניכויים נטו

### אזור ניכויים נוספים
- **כותרת:** "ניכויים נוספים"
- **טבלת ניכויים נוספים** עם העמודות:
  - מרכיב ניכוי - שם מרכיב הניכוי
  - קבלן - שם הקבלן הרלוונטי
  - שיעור/סכום - סכום או אחוז הניכוי
  - כמות - ערך כמותי
  - סכום - סכום כספי
  - פעיל עד תאריך - תאריך תפוגה
  - פעיל מתאריך - תאריך תחילת תוקף
  - יורד - סימון האם הניכוי יורד
  - חיוב - סימון לחיוב

### אזור הודעות והערות
- **כותרת:** "הודעות והערות"
- **שדות הגדרה:**
  - תוקף ההודעה מתאריך - תאריך תחילת תוקף
  - תוקף ההודעה עד תאריך - תאריך סיום תוקף
  - תוכן ההודעה - שדה טקסט להזנת תוכן ההודעה

### הגדרות נוספות
- **לכל היותר השכר הממוצע במשק** - תיבת סימון להגבלת השכר
- **עדכון תבנית כל חודש** - תיבת סימון לעדכון אוטומטי
- **תאריך תחילת תוקף** - שדות להזנת יום, חודש ושנה
- **תוקף לפי וותק ושיוכים** - תיבת סימון לתוקף מותנה

### כפתורי פעולה
- **הוספה** - להוספת רכיבים לתבנית
- **שמירה** - לשמירת התבנית (לא נראה בתצלום המסך אך סביר שקיים)
- **ביטול** - לביטול השינויים (לא נראה בתצלום המסך אך סביר שקיים)

## פירוט הפרשות
המסך כולל אזור המאפשר הגדרת פרטי הפרשות לקופות גמל, פנסיה וקרנות השתלמות:

### שדות הגדרת הפרשות
1. **סוג הפרשה** - בחירה מרשימה נפתחת
2. **אחוז הפרשת מעסיק** - שדה מספרי להזנת אחוז הפרשת המעסיק
3. **אחוז הפרשת עובד** - שדה מספרי להזנת אחוז הפרשת העובד
4. **תקרה** - הגדרת תקרה להפרשה
5. **תאריכי תוקף** - הגדרת תאריכי תחילה וסיום לתוקף ההפרשה

## קשרים ותלויות
1. **קשר למסך מרכיבי תשלום** - תבנית השכר משתמשת ברכיבי תשלום מוגדרים
2. **קשר למסך מרכיבי שווי** - תבנית השכר משתמשת ברכיבי שווי מוגדרים
3. **קשר למסך מרכיבי ניכוי** - תבנית השכר משתמשת ברכיבי ניכוי מוגדרים
4. **קשר להסכמי שכר** - תבנית השכר מיישמת הסכמי שכר מוגדרים
5. **קשר לעובדים** - תבנית השכר מוחלת על עובדים בהתאם לשיוכם

## תרחישי שימוש עיקריים
1. **יצירת תבנית שכר חדשה** - למשל, תבנית שכר לעובדים בכירים
2. **עדכון תבנית שכר קיימת** - שינוי רכיבי תשלום, ניכויים או הגדרות אחרות
3. **הוספת רכיבי תשלום לתבנית** - הוספת רכיבי תשלום כמו בונוס או החזר הוצאות
4. **הגדרת הפרשות פנסיוניות** - קביעת אחוזי הפרשה לפנסיה, קופות גמל וקרנות השתלמות
5. **הגדרת זקיפות שווי** - הוספת רכיבי שווי כמו רכב צמוד או טלפון נייד

## הרשאות
- גישה למסך זה מוגבלת למשתמשים בעלי הרשאות ניהול מערכת או ניהול שכר
- צפייה ועריכה של תבניות שכר דורשת הרשאות מתאימות

## התנהגות המסך
1. **טעינה ראשונית** - בטעינת המסך מוצגת תבנית ברירת מחדל או שדות ריקים ליצירת תבנית חדשה
2. **בחירת תבנית קיימת** - לחיצה על כפתור "בחירת תבנית שכר" מציגה רשימת תבניות קיימות לבחירה
3. **הוספת רכיבים** - לחיצה על כפתורי הוספה פותחת אפשרות להוספת רכיבים לתבנית
4. **עריכת רכיבים** - לחיצה על כפתורי עריכה מאפשרת שינוי הגדרות של רכיבים קיימים
5. **שמירה** - לאחר עריכה או הוספה, שמירת הנתונים מעדכנת את התבנית

## הערות נוספות
- המסך מאפשר גמישות רבה בהגדרת מבנה שכר מותאם לקבוצות עובדים שונות
- ניתן להגדיר תבניות שכר שונות לפי סוגי עובדים, מחלקות או תפקידים
- המערכת תומכת בהגדרת תנאי תוקף מורכבים לתבניות שכר, כולל תלות בוותק העובד ושיוכים ארגוניים
- תבניות השכר מהוות את הבסיס לחישוב תלושי השכר של העובדים
