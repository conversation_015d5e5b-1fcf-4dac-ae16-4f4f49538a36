/**
 * Association Form Modal
 * 
 * This component has been updated to use real data from the database via tRPC instead of mock data.
 * It now includes enhanced validation according to the specification.
 * 
 * IMPLEMENTATION STATUS:
 * 
 * 1. COMPLETED: Updated component to use data fetching hooks from tRPC endpoints
 * 2. COMPLETED: Added loading states for entity data
 * 3. COMPLETED: Added proper handling of changing association types
 * 
 * TO FINISH IMPLEMENTATION:
 * 
 * 1. Run proper Prisma migrations to apply the schema changes
 *    - Fix the permission error with Prisma client generation
 *    - OR manually restart the application after migrations
 * 
 * 2. Fix type errors in the associations router (src/server/api/routers/associations.ts)
 *    - This requires proper Prisma client types that will be available after migration
 * 
 * 3. Update entity types in this component if needed once the database is properly synced
 * 
 * TEMPORARY BEHAVIOR:
 * 
 * Until the database is properly set up, this component will show loading states but might not
 * display actual data. The form will still work for display purposes but won't save correctly
 * until the database issues are resolved.
 */

"use client";

import React, { useEffect, useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { format, isAfter, isBefore, parseISO } from "date-fns";
import { 
  useCreateAssociation, 
  useUpdateAssociation, 
  useDepartments,
  useEmployeeRoles,
  useSalaryTemplates,
  useSalaryAgreements,
  useEmployees,
  useAssociations
} from "../hooks";
import type { Association, AssociationType } from "../hooks";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ExclamationTriangleIcon } from "@radix-ui/react-icons";
import { Check, ChevronsUpDown, Search, User, CreditCard, Briefcase, Building2, FileText, HandshakeIcon } from "lucide-react";
import { cn } from "@/lib/utils";

// Helper to translate association types to Hebrew
const associationTypeToHebrew = {
  DEPARTMENT: "מחלקה",
  ROLE: "תפקיד",
  SALARY_TEMPLATE: "תבנית שכר",
  SALARY_AGREEMENT: "הסכם שכר",
};

// Define the form schema with Zod
const formSchema = z.object({
  associationType: z.enum(["DEPARTMENT", "ROLE", "SALARY_TEMPLATE", "SALARY_AGREEMENT"]),
  employeeId: z.string().min(1, { message: "חובה לבחור עובד" }),
  associatedEntityId: z.string().min(1, { message: "חובה לבחור ישות" }),
  startDate: z.string().min(1, { message: "חובה להזין תאריך התחלה" }),
  endDate: z.string().optional(),
  notes: z.string().optional(),
}).refine((data) => {
  // If endDate is provided, ensure it's after startDate
  if (data.endDate && data.startDate) {
    return isAfter(parseISO(data.endDate), parseISO(data.startDate));
  }
  return true;
}, {
  message: "תאריך הסיום חייב להיות מאוחר מתאריך ההתחלה",
  path: ["endDate"],
});

type FormValues = z.infer<typeof formSchema>;

interface AssociationFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  employerId: string;
  onSuccess: () => void;
  association?: Association;
}

export function AssociationFormModal({
  isOpen,
  onClose,
  employerId,
  onSuccess,
  association,
}: AssociationFormModalProps) {
  const isEditing = !!association;
  const { mutate: createAssociation, isPending: isCreating } = useCreateAssociation(employerId);
  const { mutate: updateAssociation, isPending: isUpdating } = useUpdateAssociation(employerId);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);
  const [openEmployeeCombobox, setOpenEmployeeCombobox] = useState(false);
  const [employeeSearchValue, setEmployeeSearchValue] = useState("");
  const [openEntityCombobox, setOpenEntityCombobox] = useState(false);
  const [entitySearchValue, setEntitySearchValue] = useState("");

  // Fetch data from API
  const { data: departments, isLoading: isLoadingDepartments } = useDepartments(employerId);
  const { data: roles, isLoading: isLoadingRoles } = useEmployeeRoles(employerId);
  const { data: templates, isLoading: isLoadingTemplates } = useSalaryTemplates(employerId);
  const { data: agreements, isLoading: isLoadingAgreements } = useSalaryAgreements(employerId);
  const { data: employees, isLoading: isLoadingEmployees } = useEmployees(employerId);
  
  // Fetch existing associations to check for overlaps
  const { data: existingAssociations } = useAssociations(1, employerId);

  const isPending = isCreating || isUpdating;
  const isLoadingEntities = 
    isLoadingDepartments || 
    isLoadingRoles || 
    isLoadingTemplates || 
    isLoadingAgreements || 
    isLoadingEmployees;

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      associationType: "DEPARTMENT",
      employeeId: "",
      associatedEntityId: "",
      startDate: format(new Date(), "yyyy-MM-dd"),
      endDate: "",
      notes: "",
    },
  });

  // Set form values when editing an existing association
  useEffect(() => {
    if (association) {
      form.reset({
        associationType: association.associationType,
        employeeId: association.employeeId,
        associatedEntityId: association.associatedEntityId,
        startDate: association.startDate,
        endDate: association.endDate || "",
        notes: association.notes || "",
      });
    }
  }, [association, form]);
  
  // Check for validation warnings (non-blocking warnings that still allow submission)
  const watchedEmployeeId = form.watch("employeeId");
  const watchedAssociationType = form.watch("associationType");
  const watchedAssociatedEntityId = form.watch("associatedEntityId");
  const watchedStartDate = form.watch("startDate");
  const watchedEndDate = form.watch("endDate");
  
  useEffect(() => {
    const warnings: string[] = [];
    
    // Skip validation if form is not complete
    if (!watchedEmployeeId || !watchedAssociationType || !watchedAssociatedEntityId || !watchedStartDate) {
      setValidationWarnings([]);
      return;
    }
    
    // Check for overlapping associations
    const existingData = existingAssociations?.data || [];
    const currentEmployeeId = watchedEmployeeId;
    const currentType = watchedAssociationType;
    const currentStartDate = watchedStartDate;
    const currentEndDate = watchedEndDate;
    
    const overlappingAssociations = existingData.filter((a: any) => {
      // Skip the current association when editing
      if (isEditing && a.id === association?.id) return false;
      
      // Check if same employee and same type
      if (a.employeeId !== currentEmployeeId || a.associationType !== currentType) return false;
      
      // Check for date overlap
      const aStart = parseISO(a.startDate);
      const aEnd = a.endDate ? parseISO(a.endDate) : null;
      const currentStart = parseISO(currentStartDate);
      const currentEnd = currentEndDate ? parseISO(currentEndDate) : null;
      
      // No end date for either means they overlap
      if (!aEnd && !currentEnd) return true;
      
      // If current has no end date but existing does
      if (!currentEnd && aEnd) {
        return !isBefore(currentStart, aStart); // Current starts before or at the same time as existing
      }
      
      // If existing has no end date but current does
      if (currentEnd && !aEnd) {
        return !isAfter(currentEnd, aStart); // Current ends after or at the same time as existing starts
      }
      
      // Both have end dates - check for overlap
      if (currentEnd && aEnd) {
        return (
          (isBefore(currentStart, aEnd) || currentStart.getTime() === aEnd.getTime()) && 
          (isAfter(currentEnd, aStart) || currentEnd.getTime() === aStart.getTime())
        );
      }
      
      return false;
    });
    
    if (overlappingAssociations.length > 0) {
      const overlappingNames = overlappingAssociations.map((a: any) => `${a.associatedEntityName} (${formatDateRange(a.startDate, a.endDate)})`);
      warnings.push(`קיימים שיוכים חופפים לעובד זה: ${overlappingNames.join(', ')}`);
    }
    
    setValidationWarnings(warnings);
  }, [watchedEmployeeId, watchedAssociationType, watchedAssociatedEntityId, watchedStartDate, watchedEndDate, existingAssociations?.data, isEditing, association?.id]);

  const onSubmit = (values: FormValues) => {
    // Convert form values for API
    const apiValues = {
      ...values,
      departmentId: values.associationType === "DEPARTMENT" ? values.associatedEntityId : undefined,
      roleId: values.associationType === "ROLE" ? values.associatedEntityId : undefined,
      salaryTemplateId: values.associationType === "SALARY_TEMPLATE" ? values.associatedEntityId : undefined,
      salaryAgreementId: values.associationType === "SALARY_AGREEMENT" ? values.associatedEntityId : undefined,
    };

    if (isEditing && association) {
      updateAssociation(
        { 
          id: association.id,
          ...apiValues
        },
        {
          onSuccess: () => {
            toast.success("השיוך עודכן בהצלחה");
            onSuccess();
          },
          onError: (error) => {
            toast.error("אירעה שגיאה בעדכון השיוך");
            console.error(error);
          },
        }
      );
    } else {
      createAssociation(apiValues, {
        onSuccess: () => {
          toast.success("השיוך נוצר בהצלחה");
          onSuccess();
        },
        onError: (error) => {
          toast.error("אירעה שגיאה ביצירת השיוך");
          console.error(error);
        },
      });
    }
  };

  // Get entities based on selected type
  const associationType = form.watch("associationType") as AssociationType;
  
  // Helper function to format date range for display
  function formatDateRange(startDate: string, endDate: string | null) {
    const start = format(parseISO(startDate), "dd/MM/yyyy");
    return endDate ? `${start} - ${format(parseISO(endDate), "dd/MM/yyyy")}` : `${start} - ללא הגבלה`;
  }
  
  // Get the appropriate entities based on the association type
  const getEntities = () => {
    switch (associationType) {
      case "DEPARTMENT":
        return departments || [];
      case "ROLE":
        return roles || [];
      case "SALARY_TEMPLATE":
        return templates || [];
      case "SALARY_AGREEMENT":
        return agreements || [];
      default:
        return [];
    }
  };

  const entities = getEntities();

  // Filter employees based on search
  const filteredEmployees = React.useMemo(() => {
    if (!employees || !employeeSearchValue) return employees || [];
    
    const searchLower = employeeSearchValue.toLowerCase();
    return employees.filter((employee: any) => {
      const fullName = `${employee.firstName} ${employee.lastName}`.toLowerCase();
      const nationalId = employee.nationalId?.toLowerCase() || "";
      const passportNumber = employee.visaNumber?.toLowerCase() || "";
      
      return fullName.includes(searchLower) || 
             nationalId.includes(searchLower) || 
             passportNumber.includes(searchLower);
    });
  }, [employees, employeeSearchValue]);

  // Filter entities based on search
  const filteredEntities = React.useMemo(() => {
    if (!entities || !entitySearchValue) return entities;
    
    const searchLower = entitySearchValue.toLowerCase();
    return entities.filter((entity: any) => 
      entity.name.toLowerCase().includes(searchLower) ||
      (entity.description && entity.description.toLowerCase().includes(searchLower))
    );
  }, [entities, entitySearchValue]);

  // Get icon for association type
  const getAssociationIcon = (type: AssociationType) => {
    switch (type) {
      case "DEPARTMENT":
        return <Building2 className="h-4 w-4" />;
      case "ROLE":
        return <Briefcase className="h-4 w-4" />;
      case "SALARY_TEMPLATE":
        return <FileText className="h-4 w-4" />;
      case "SALARY_AGREEMENT":
        return <HandshakeIcon className="h-4 w-4" />;
      default:
        return null;
    }
  };

  // Reset associated entity when type changes
  const previousAssociationType = React.useRef(associationType);
  
  useEffect(() => {
    // Only reset if the type actually changed and we're not in the initial load
    if (previousAssociationType.current && previousAssociationType.current !== associationType) {
      form.setValue("associatedEntityId", "");
      setEntitySearchValue(""); // Reset entity search
    }
    previousAssociationType.current = associationType;
  }, [associationType, form]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? "עריכת שיוך" : "הוספת שיוך חדש"}</DialogTitle>
          <DialogDescription>
            {isEditing ? "ערוך את פרטי השיוך הקיים" : "הגדר שיוך חדש בין עובד לישות במערכת"}
          </DialogDescription>
        </DialogHeader>
        
        {validationWarnings.length > 0 && (
          <Alert className="mb-4">
            <ExclamationTriangleIcon className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside">
                {validationWarnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="associationType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>סוג שיוך</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isPending || isLoadingEntities}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="בחר סוג שיוך" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="DEPARTMENT">
                        <div className="flex items-center gap-2">
                          <Building2 className="h-4 w-4" />
                          מחלקה
                        </div>
                      </SelectItem>
                      <SelectItem value="ROLE">
                        <div className="flex items-center gap-2">
                          <Briefcase className="h-4 w-4" />
                          תפקיד
                        </div>
                      </SelectItem>
                      <SelectItem value="SALARY_TEMPLATE">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          תבנית שכר
                        </div>
                      </SelectItem>
                      <SelectItem value="SALARY_AGREEMENT">
                        <div className="flex items-center gap-2">
                          <HandshakeIcon className="h-4 w-4" />
                          הסכם שכר
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    לכל עובד ניתן לשייך מחלקה אחת, תפקיד אחד, תבנית שכר אחת והסכם שכר אחד בכל זמן נתון
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="employeeId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>עובד</FormLabel>
                  <Popover open={openEmployeeCombobox} onOpenChange={setOpenEmployeeCombobox}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={openEmployeeCombobox}
                          className={cn(
                            "w-full justify-between",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={isPending || isLoadingEmployees}
                        >
                          {field.value ? (
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              {employees?.find((e: any) => e.id === field.value)?.firstName}{" "}
                              {employees?.find((e: any) => e.id === field.value)?.lastName}
                            </div>
                          ) : (
                            <span>בחר עובד...</span>
                          )}
                          <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput 
                          placeholder="חפש לפי שם, ת.ז. או דרכון..." 
                          value={employeeSearchValue}
                          onValueChange={setEmployeeSearchValue}
                        />
                        <CommandList>
                          <CommandEmpty>לא נמצאו עובדים</CommandEmpty>
                          <CommandGroup>
                            {isLoadingEmployees ? (
                              <CommandItem disabled>
                                <Search className="ml-2 h-4 w-4 animate-spin" />
                                טוען עובדים...
                              </CommandItem>
                            ) : (
                              filteredEmployees?.slice(0, 10).map((employee: any) => (
                                <CommandItem
                                  key={employee.id}
                                  value={employee.id}
                                  onSelect={() => {
                                    field.onChange(employee.id);
                                    setOpenEmployeeCombobox(false);
                                    setEmployeeSearchValue("");
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "ml-2 h-4 w-4",
                                      field.value === employee.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  <div className="flex flex-col">
                                    <div className="flex items-center gap-2">
                                      <User className="h-4 w-4" />
                                      <span className="font-medium">
                                        {employee.firstName} {employee.lastName}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                      <span className="flex items-center gap-1">
                                        <CreditCard className="h-3 w-3" />
                                        {employee.nationalId}
                                      </span>
                                      {employee.visaNumber && (
                                        <span className="flex items-center gap-1">
                                          דרכון: {employee.visaNumber}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </CommandItem>
                              ))
                            )}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="associatedEntityId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>
                    {associationType === "DEPARTMENT" && "מחלקה"}
                    {associationType === "ROLE" && "תפקיד"}
                    {associationType === "SALARY_TEMPLATE" && "תבנית שכר"}
                    {associationType === "SALARY_AGREEMENT" && "הסכם שכר"}
                  </FormLabel>
                  <Popover open={openEntityCombobox} onOpenChange={setOpenEntityCombobox}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={openEntityCombobox}
                          className={cn(
                            "w-full justify-between",
                            !field.value && "text-muted-foreground"
                          )}
                          disabled={isPending || isLoadingEntities}
                        >
                          {field.value ? (
                            <div className="flex items-center gap-2">
                              {getAssociationIcon(associationType)}
                              {entities?.find((e: any) => e.id === field.value)?.name}
                            </div>
                          ) : (
                            <span>בחר {associationTypeToHebrew[associationType]}...</span>
                          )}
                          <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput 
                          placeholder={`חפש ${associationTypeToHebrew[associationType]}...`}
                          value={entitySearchValue}
                          onValueChange={setEntitySearchValue}
                        />
                        <CommandList>
                          <CommandEmpty>לא נמצאו תוצאות</CommandEmpty>
                          <CommandGroup>
                            {isLoadingEntities ? (
                              <CommandItem disabled>
                                <Search className="ml-2 h-4 w-4 animate-spin" />
                                טוען...
                              </CommandItem>
                            ) : filteredEntities.length === 0 ? (
                              <CommandItem disabled>לא נמצאו תוצאות</CommandItem>
                            ) : (
                              filteredEntities.map((entity: any) => (
                                <CommandItem
                                  key={entity.id}
                                  value={entity.id}
                                  onSelect={() => {
                                    field.onChange(entity.id);
                                    setOpenEntityCombobox(false);
                                    setEntitySearchValue("");
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      "ml-2 h-4 w-4",
                                      field.value === entity.id ? "opacity-100" : "opacity-0"
                                    )}
                                  />
                                  <div className="flex items-center gap-2">
                                    {getAssociationIcon(associationType)}
                                    <div className="flex flex-col">
                                      <span className="font-medium">{entity.name}</span>
                                      {entity.description && (
                                        <span className="text-sm text-muted-foreground">
                                          {entity.description}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </CommandItem>
                              ))
                            )}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>תאריך התחלה</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>תאריך סיום (אופציונלי)</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        value={field.value || ""}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormDescription>
                      השאר ריק עבור שיוך ללא הגבלת זמן
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>הערות (אופציונלי)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      value={field.value || ""}
                      placeholder="הוסף הערות נוספות כאן..."
                      className="resize-none"
                      disabled={isPending}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isPending}
              >
                ביטול
              </Button>
              <Button
                type="submit"
                disabled={isPending}
              >
                {isPending
                  ? isEditing
                    ? "מעדכן..."
                    : "מוסיף..."
                  : isEditing
                  ? "עדכן"
                  : "הוסף"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 