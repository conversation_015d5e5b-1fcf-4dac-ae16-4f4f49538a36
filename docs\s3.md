# עבודה עם Amazon S3
# Working with Amazon S3

מסמך זה מתאר את האופן שבו המערכת משתמשת ב-Amazon S3 לאחסון ושיתוף קבצים.
This document describes how the system uses Amazon S3 for file storage and sharing.

## ארכיטקטורה כללית
## General Architecture

המערכת משתמשת ב-Amazon S3 לאחסון ושיתוף מסמכים, כאשר הגישה נעשית באמצעות מספר שכבות:
The system uses Amazon S3 for document storage and sharing, with access through several layers:

1. **שכבת שרת** - מודול `s3.ts` המכיל פונקציות עזר לעבודה מול S3
   **Server Layer** - `s3.ts` module containing helper functions for working with S3
2. **שכבת API** - אנדפוינט להנפקת URL חתומים
   **API Layer** - Endpoint for generating signed URLs
3. **שכבת לקוח** - קומ<PERSON>וננטה להצגת מסמכים מ-S3
   **Client Layer** - Component for displaying documents from S3

## ארגון קבצים
## File Organization

כל הקבצים הקשורים למשכורות מועלים אוטומטית לתיקייה ייעודית עם תחילית `_talsalary/`. המערכת מוסיפה את התחילית הזו באופן אוטומטי בכל פעולות ההעלאה, ולכן אין צורך לציין אותה באופן ידני.
All salary-related files are automatically uploaded to a dedicated folder with the prefix `_talsalary/`. The system adds this prefix automatically in all upload operations, so there is no need to specify it manually.

### מבנה תיקיות ייעודיות
### Dedicated Folder Structure

המערכת משתמשת במבנה תיקיות קבוע עבור סוגי קבצים שונים:
The system uses a fixed folder structure for different file types:

1. **קבצי משכורות** - `_talsalary/{מפתח הקובץ}`
   **Salary files** - `_talsalary/{file key}`
2. **תמונות פרופיל של מעסיקים** - `employer-profiles/{מזהה מעסיק}/{מזהה ייחודי}.{סיומת}`
   **Employer profile pictures** - `employer-profiles/{employer ID}/{unique ID}.{extension}`
3. **מסמכי מעסיקים** - `employer-docs/{מזהה מעסיק}/{מזהה ייחודי}-{שם הקובץ}`
   **Employer documents** - `employer-docs/{employer ID}/{unique ID}-{file name}`

## מודל הנתונים
## Data Model

המערכת מאחסנת את המידע הקשור לקבצי S3 במספר מודלים:
The system stores S3 file-related information in several models:

### Document

מודל זה מייצג מסמך המאוחסן ב-S3:
This model represents a document stored in S3: