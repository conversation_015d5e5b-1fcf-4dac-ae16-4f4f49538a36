import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { 
  createEmployerSchema, 
  getEmployersSchema, 
  updateEmployerSchema,
  uploadEmployerDocumentSchema,
  uploadProfilePictureSchema 
} from "@/schema/employer";
import { getSignedUploadUrl, getPublicUrl, deleteFile } from "@/server/s3";
import { randomUUID } from "crypto";
import path from "path";
import { Prisma } from "@prisma/client";

// Helper function to generate a unique file path in S3
function generateS3Key(
  employerId: string,
  fileName: string,
  prefix: string = "employer-docs"
) {
  // ensure filename is safe and bounded
  const safeName = path.basename(fileName).slice(0, 100);
  const uniqueFileName = `${randomUUID()}-${safeName}`;
  return `${prefix}/${employerId}/${uniqueFileName}`;
}

export const employerRouter = createTRPCRouter({
  getAll: protectedProcedure
    .input(getEmployersSchema)
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Calculate pagination
        const skip = (input.page - 1) * input.limit;
        
        // Build where clause based on filters
        const where: Prisma.EmployerWhereInput = {
          tenantId: user.tenantId
        };
        
        // Add search filter if provided
        if (input.search) {
          where.OR = [
            { name: { contains: input.search, mode: 'insensitive' } },
            { identifier: { contains: input.search, mode: 'insensitive' } }
          ];
        }
        
        // Status field doesn't exist in the Employer model
        // Commenting out this filter as it causes a Prisma validation error
        // if (input.status && input.status !== "all") {
        //   where.status = input.status;
        // }
        
        // Get total count for pagination
        const totalCount = await db.employer.count({ where });
        
        // Get employers 
        const employers = await db.employer.findMany({
          where,
          select: {
            id: true,
            name: true,
            identifier: true,
            taxId: true,
            registrationDate: true,
            accountManager: true,
            payrollDay: true,
            profilePictureUrl: true,
            employees: {
              select: {
                id: true
              }
            },
            // Include document count
            _count: {
              select: {
                documents: true
              }
            }
          },
          orderBy: {
            name: "asc"
          },
          skip,
          take: input.limit
        });
        
        // Get the latest payslips for each employer
        const employerIds = employers.map(e => e.id);
        const latestPayslips = await db.payslip.groupBy({
          by: ['employeeId'],
          where: {
            employee: {
              employerId: {
                in: employerIds
              }
            }
          },
          _max: {
            year: true,
            month: true
          },
          orderBy: [
            { _max: { year: 'desc' } },
            { _max: { month: 'desc' } }
          ]
        });
        
        // Create a map of employee ID to their latest payslip period
        const employeePayslipMap: Record<string, { year: number, month: number }> = {};
        for (const payslip of latestPayslips) {
          if (payslip.employeeId && 
              payslip._max && 
              typeof payslip._max.year === 'number' && 
              typeof payslip._max.month === 'number') {
            employeePayslipMap[payslip.employeeId] = {
              year: payslip._max.year,
              month: payslip._max.month
            };
          }
        }
        
        // Get employees with their employers to map back
        const employees = await db.employee.findMany({
          where: {
            employerId: {
              in: employerIds
            }
          },
          select: {
            id: true,
            employerId: true
          }
        });
        
        // Create a map of employer ID to their latest payslip period
        const employerPayslipMap: Record<string, { year: number, month: number }> = {};
        for (const employee of employees) {
          const latestPayslip = employeePayslipMap[employee.id];
          if (!latestPayslip) continue;
          
          const currentEmployerLatestPayslip = employerPayslipMap[employee.employerId];
          
          if (!currentEmployerLatestPayslip || 
              latestPayslip.year > currentEmployerLatestPayslip.year || 
              (latestPayslip.year === currentEmployerLatestPayslip.year && 
               latestPayslip.month > currentEmployerLatestPayslip.month)) {
            employerPayslipMap[employee.employerId] = latestPayslip;
          }
        }
        
        return {
          employers: employers.map((employer) => {
            // Format period for last payslip
            const latestPayslip = employerPayslipMap[employer.id];
            const lastPayslipPeriod = latestPayslip
              ? `${latestPayslip.month}/${latestPayslip.year}`
              : "N/A";
            
            return {
              id: employer.id,
              name: employer.name,
              identifier: employer.identifier || "",
              taxId: employer.taxId || "",
              profilePictureUrl: employer.profilePictureUrl || null,
              registrationDate: employer.registrationDate
                ? employer.registrationDate.toISOString()
                : undefined,
              accountManager: employer.accountManager,
              payrollDay: employer.payrollDay,
              employeeCount: employer.employees.length,
              documentCount: employer._count.documents,
              // Since status doesn't exist in the database, determine based on employee count
              // or other logic as appropriate for your application
              status: employer.employees.length > 0 ? "active" : "inactive",
              lastPayslip: lastPayslipPeriod
            };
          }),
          totalCount,
          pageCount: Math.ceil(totalCount / input.limit)
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error fetching employers");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employers",
          cause: error,
        });
      }
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const employerId = input.id;

        const isPrivileged =
          session.user.role === "OWNER" || session.user.role === "ADMIN";
        if (!isPrivileged && session.user.employerId && employerId !== session.user.employerId) {
          throw new TRPCError({ code: "FORBIDDEN", message: "Employer mismatch" });
        }
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Get employer with additional data
        const employer = await db.employer.findFirst({
          where: {
            id: employerId,
            tenantId: user.tenantId
          },
          include: {
            employees: true,
            documents: {
              select: {
                id: true,
                title: true,
                fileName: true,
                fileType: true,
                fileSize: true,
                url: true,
                category: true,
                uploadedAt: true,
                updatedAt: true
              }
            }
          }
        });
        
        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Get the latest payslip for this employer
        const latestPayslip = await db.payslip.findFirst({
          where: {
            employee: {
              employerId: employerId
            }
          },
          select: {
            year: true,
            month: true
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' }
          ]
        });
        
        // Format period for last payslip
        const lastPayslipPeriod = latestPayslip
          ? `${latestPayslip.month}/${latestPayslip.year}`
          : "N/A";
        
        return {
          id: employer.id,
          name: employer.name,
          identifier: employer.identifier || "",
          taxId: employer.taxId || "",
          profilePictureUrl: employer.profilePictureUrl || null,
          registrationDate: employer.registrationDate
            ? employer.registrationDate.toISOString()
            : undefined,
          accountManager: employer.accountManager,
          payrollDay: employer.payrollDay,
          employeeCount: employer.employees.length,
          // Since status doesn't exist in the database, determine based on employee count
          // or other logic as appropriate for your application
          status: employer.employees.length > 0 ? "active" : "inactive",
          lastPayslip: lastPayslipPeriod,
          documents: employer.documents,
          // Map other properties from the employer model
          address: employer.address,
          contact: employer.contact,
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.id },
          "Error fetching employer"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employer",
          cause: error,
        });
      }
    }),

  create: protectedProcedure
    .input(createEmployerSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Create the new employer
        const employer = await db.employer.create({
          data: {
            name: input.name,
            identifier: input.identifier,
            taxId: input.taxId,
            profilePictureUrl: input.profilePictureUrl,
            registrationDate: input.registrationDate ? new Date(input.registrationDate) : undefined,
            accountManager: input.accountManager,
            payrollDay: input.payrollDay,
            address: input.address,
            contact: input.contact,
            tenant: {
              connect: {
                id: user.tenantId
              }
            }
          }
        });
        
        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "CREATE",
            modelName: "Employer",
            recordId: employer.id,
            newValues: {
              name: input.name,
              identifier: input.identifier,
              taxId: input.taxId,
              profilePictureUrl: input.profilePictureUrl,
              registrationDate: input.registrationDate,
              accountManager: input.accountManager,
              payrollDay: input.payrollDay
            }
          }
        });
        
        return {
          id: employer.id,
          name: employer.name,
          identifier: employer.identifier || "",
          taxId: employer.taxId || "",
          profilePictureUrl: employer.profilePictureUrl || null,
          registrationDate: employer.registrationDate
            ? employer.registrationDate.toISOString()
            : undefined,
          accountManager: employer.accountManager,
          payrollDay: employer.payrollDay,
          employeeCount: 0,
          documentCount: 0,
          // Since status doesn't exist in the database, use inactive since there are no employees yet
          status: "inactive",
          lastPayslip: "N/A",
        };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id }, "Error creating employer");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create employer",
          cause: error,
        });
      }
    }),

  update: protectedProcedure
    .input(updateEmployerSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        const employerId = input.id;
        
        // Prepare data for update
        const updateData: {
          name?: string;
          identifier?: string;
          taxId?: string;
          registrationDate?: Date;
          accountManager?: string;
          payrollDay?: number;
          profilePictureUrl?: string;
          address?: {
            street?: string;
            city?: string;
            zipCode?: string;
            country?: string;
          };
          contact?: {
            name?: string;
            email?: string;
            phone?: string;
          };
        } = {};
        if (input.name !== undefined) updateData.name = input.name;
        if (input.identifier !== undefined) updateData.identifier = input.identifier;
        if (input.taxId !== undefined) updateData.taxId = input.taxId;
        if (input.registrationDate !== undefined)
          updateData.registrationDate = new Date(input.registrationDate);
        if (input.accountManager !== undefined)
          updateData.accountManager = input.accountManager;
        if (input.payrollDay !== undefined) updateData.payrollDay = input.payrollDay;
        if (input.profilePictureUrl !== undefined) updateData.profilePictureUrl = input.profilePictureUrl;
        if (input.address !== undefined) updateData.address = input.address;
        if (input.contact !== undefined) updateData.contact = input.contact;

        // Guard: No fields to update - check before any database calls
        if (Object.keys(updateData).length === 0) {
          throw new TRPCError({ code: "BAD_REQUEST", message: "No fields to update" });
        }
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const existingEmployer = await db.employer.findFirst({
          where: {
            id: employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!existingEmployer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Update the employer
        const updatedEmployer = await db.employer.update({
          where: {
            id: employerId
          },
          data: updateData,
          include: {
            employees: true,
            _count: {
              select: {
                documents: true
              }
            }
          }
        });
        
        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "UPDATE",
            modelName: "Employer",
            recordId: employerId,
            oldValues: {
              name: existingEmployer.name,
              identifier: existingEmployer.identifier,
              taxId: existingEmployer.taxId,
              profilePictureUrl: existingEmployer.profilePictureUrl,
              registrationDate: existingEmployer.registrationDate,
              accountManager: existingEmployer.accountManager,
              payrollDay: existingEmployer.payrollDay
            },
            newValues: updateData
          }
        });
        
        // Get the latest payslip for this employer
        const latestPayslip = await db.payslip.findFirst({
          where: {
            employee: {
              employerId: employerId
            }
          },
          select: {
            year: true,
            month: true
          },
          orderBy: [
            { year: 'desc' },
            { month: 'desc' }
          ]
        });
        
        // Format period for last payslip
        const lastPayslipPeriod = latestPayslip
          ? `${latestPayslip.month}/${latestPayslip.year}`
          : "N/A";
        
        return {
          success: true,
          employer: {
            id: updatedEmployer.id,
            name: updatedEmployer.name,
            identifier: updatedEmployer.identifier || "",
            taxId: updatedEmployer.taxId || "",
            profilePictureUrl: updatedEmployer.profilePictureUrl || null,
            registrationDate: updatedEmployer.registrationDate
              ? updatedEmployer.registrationDate.toISOString()
              : undefined,
            accountManager: updatedEmployer.accountManager,
            payrollDay: updatedEmployer.payrollDay,
            employeeCount: updatedEmployer.employees.length,
            documentCount: updatedEmployer._count.documents,
            // Since status doesn't exist in the database, determine based on employee count
            status: updatedEmployer.employees.length > 0 ? "active" : "inactive",
            lastPayslip: lastPayslipPeriod
          }
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.id },
          "Error updating employer"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update employer",
          cause: error,
        });
      }
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string().min(1, { message: "Employer ID is required" }) }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session, logger } = ctx;
        const userId = session.user.id;
        const employerId = input.id;
        
        if (!employerId || employerId.trim() === "") {
          logger.error({ userId, employerId }, "Invalid employer ID provided");
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Invalid employer ID",
          });
        }
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          logger.warn({ userId }, "User not found");
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const existingEmployer = await db.employer.findFirst({
          where: {
            id: employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!existingEmployer) {
          logger.warn({ userId, employerId }, "Employer not found");
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Check for existing employees
        const employeeCount = await db.employee.count({
          where: {
            employerId: employerId
          }
        });
        
        logger.info({ userId, employerId, employeeCount }, "Checked employee count before deletion");
        
        if (employeeCount > 0) {
          logger.warn({ userId, employerId, employeeCount }, "Cannot delete employer with existing employees");
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Cannot delete employer with existing employees",
          });
        }
        
        // Wrap all cleanup and deletion in a single transaction for atomicity
        await db.$transaction(async (trx) => {
          // 1. Bank accounts
          const bankAccountsCount = await trx.bankAccount.count({ where: { employerId } });
          if (bankAccountsCount > 0) {
            logger.info({ userId, employerId, bankAccountsCount }, "Deleting related bank accounts");
            await trx.bankAccount.deleteMany({ where: { employerId } });
          }
          // 2. Documents
          const documentsCount = await trx.document.count({ where: { employerId } });
          if (documentsCount > 0) {
            logger.info({ userId, employerId, documentsCount }, "Updating related documents");
            await trx.document.updateMany({ where: { employerId }, data: { employerId: null } });
          }
          // 3. Form102 records
          const form102Count = await trx.form102.count({ where: { employerId } });
          if (form102Count > 0) {
            logger.info({ userId, employerId, form102Count }, "Deleting Form102 records");
            await trx.form102.deleteMany({ where: { employerId } });
          }
          // 4. Form126 records
          const form126Count = await trx.form126.count({ where: { employerId } });
          if (form126Count > 0) {
            logger.info({ userId, employerId, form126Count }, "Deleting Form126 records");
            await trx.form126.deleteMany({ where: { employerId } });
          }
          // 5. Reports
          const reportsCount = await trx.report.count({ where: { employerId } });
          if (reportsCount > 0) {
            logger.info({ userId, employerId, reportsCount }, "Updating related reports");
            await trx.report.updateMany({ where: { employerId }, data: { employerId: null } });
          }
          // Now delete the employer
          logger.info({ userId, employerId }, "Deleting employer record");
          await trx.employer.delete({ where: { id: employerId } });
        });
        
        logger.info({ userId, employerId }, "Employer deleted successfully");
        
        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "DELETE",
            modelName: "Employer",
            recordId: employerId,
            oldValues: { id: employerId, name: existingEmployer.name }
          }
        });
        
        logger.info({ userId, employerId }, "Audit log created for employer deletion");
        
        return { success: true };
      } catch (error) {
        ctx.logger?.error({ err: error, userId: ctx.session.user.id, employerId: input.id }, "Error deleting employer");
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete employer",
          cause: error,
        });
      }
    }),

  // New procedure for getting a signed upload URL for profile picture
  getProfilePictureUploadUrl: protectedProcedure
    .input(z.object({ 
      employerId: z.string().min(1),
      fileType: z.string().min(1)
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Generate a unique key for S3
        const fileKey = `employer-profiles/${input.employerId}/${randomUUID()}.${input.fileType.split('/')[1] || 'jpg'}`;
        
        // Get a signed upload URL
        const signedUrl = await getSignedUploadUrl(fileKey, input.fileType, 3600);
        
        // Return the URL and the file key (for updating the profile later)
        return {
          signedUrl,
          fileKey,
          publicUrl: getPublicUrl(fileKey)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.employerId },
          "Error generating signed URL"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate upload URL",
          cause: error,
        });
      }
    }),

  // New procedure for getting a signed upload URL for employer documents
  getDocumentUploadUrl: protectedProcedure
    .input(z.object({ 
      employerId: z.string().min(1),
      fileName: z.string().min(1),
      fileType: z.string().min(1)
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Sanitize and truncate fileName
        let safeFileName = path.basename(input.fileName);
        if (safeFileName.length > 100) {
          const ext = path.extname(safeFileName);
          safeFileName = safeFileName.slice(0, 100 - ext.length) + ext;
        }
        
        // Generate a unique key for S3
        const fileKey = generateS3Key(input.employerId, safeFileName);
        
        // Get a signed upload URL
        const signedUrl = await getSignedUploadUrl(fileKey, input.fileType, 3600);
        
        // Return the URL and the file key
        return {
          signedUrl,
          fileKey,
          publicUrl: getPublicUrl(fileKey)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.employerId },
          "Error generating signed URL"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate upload URL",
          cause: error,
        });
      }
    }),

  // New procedure for registering a document after upload
  registerDocument: protectedProcedure
    .input(z.object({
      employerId: z.string().min(1),
      title: z.string().optional(),
      fileName: z.string().min(1),
      fileType: z.string().min(1),
      fileSize: z.number().min(1),
      fileKey: z.string().min(1),
      category: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Validate fileKey prefix
        if (!input.fileKey.startsWith(`employer-docs/${input.employerId}/`)) {
          throw new TRPCError({ code: "FORBIDDEN", message: "Invalid fileKey" });
        }
        
        // Get the public URL for the file
        const publicUrl = getPublicUrl(input.fileKey);
        
        // Create a document record in the database
        const document = await db.document.create({
          data: {
            tenantId: user.tenantId,
            title: input.title || input.fileName,
            fileName: input.fileName,
            fileType: input.fileType,
            fileSize: input.fileSize,
            url: publicUrl,
            s3Key: input.fileKey,
            category: input.category || "general",
            referenceModel: "Employer",
            referenceId: input.employerId,
            employerId: input.employerId,
            mimeType: input.fileType,
            uploadedBy: userId,
            metadata: {
              uploadedBy: userId
            }
          }
        });
        
        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "CREATE",
            modelName: "Document",
            recordId: document.id,
            newValues: { 
              title: document.title,
              fileName: document.fileName,
              employerId: document.employerId,
              s3Key: document.s3Key
            }
          }
        });
        
        return {
          success: true,
          document: {
            id: document.id,
            title: document.title,
            fileName: document.fileName,
            fileType: document.fileType,
            fileSize: document.fileSize,
            url: document.url,
            s3Key: document.s3Key,
            category: document.category,
            uploadedAt: document.uploadedAt
          }
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.employerId },
          "Error registering document"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to register document",
          cause: error,
        });
      }
    }),

  // New procedure for deleting a document
  deleteDocument: protectedProcedure
    .input(z.object({ documentId: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if document exists and belongs to the user's tenant
        const document = await db.document.findFirst({
          where: {
            id: input.documentId,
            tenantId: user.tenantId
          }
        });
        
        if (!document) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Document not found",
          });
        }
        
        // Delete from S3 if there's a key
        if (document.s3Key) {
          try {
            await deleteFile(document.s3Key);
          } catch (s3Error) {
            ctx.logger?.error(
              { err: s3Error, userId, documentId: input.documentId },
              "Error deleting file from S3"
            );
          }
        }
        // Fallback to metadata for older documents
        else if (document.metadata && typeof document.metadata === 'object' && 's3Key' in document.metadata) {
          try {
            const s3Key = document.metadata.s3Key as string;
            await deleteFile(s3Key);
          } catch (s3Error) {
            ctx.logger?.error(
              { err: s3Error, userId, documentId: input.documentId },
              "Error deleting file from S3"
            );
          }
        }
        
        // Delete the document record
        await db.document.delete({
          where: { id: input.documentId }
        });
        
        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "DELETE",
            modelName: "Document",
            recordId: input.documentId,
            oldValues: { 
              id: document.id,
              title: document.title, 
              fileName: document.fileName,
              s3Key: document.s3Key
            }
          }
        });
        
        return { success: true };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, documentId: input.documentId },
          "Error deleting document"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete document",
          cause: error,
        });
      }
    }),

  // New procedure for updating profile picture
  updateProfilePicture: protectedProcedure
    .input(z.object({
      employerId: z.string().min(1),
      profilePictureUrl: z.string().min(1),
      profilePictureKey: z.string().min(1)
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Delete old profile picture if exists
        if (employer.profilePictureKey) {
          try {
            await deleteFile(employer.profilePictureKey);
          } catch (s3Error) {
            ctx.logger?.error(
              { err: s3Error, userId, employerId: input.employerId },
              "Error deleting previous profile picture from S3"
            );
          }
        }
        
        // Update the profile picture URL
        const updatedEmployer = await db.employer.update({
          where: { id: input.employerId },
          data: { 
            profilePictureUrl: input.profilePictureUrl,
            profilePictureKey: input.profilePictureKey
          },
          select: {
            id: true,
            name: true,
            profilePictureUrl: true,
            profilePictureKey: true
          }
        });
        
        // Create audit log entry
        await db.auditLog.create({
          data: {
            tenantId: user.tenantId,
            userId: userId,
            action: "UPDATE",
            modelName: "Employer",
            recordId: input.employerId,
            oldValues: { 
              profilePictureUrl: employer.profilePictureUrl,
              profilePictureKey: employer.profilePictureKey
            },
            newValues: { 
              profilePictureUrl: input.profilePictureUrl,
              profilePictureKey: input.profilePictureKey
            }
          }
        });
        
        return {
          success: true,
          employer: updatedEmployer
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.employerId },
          "Error updating profile picture"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update profile picture",
          cause: error,
        });
      }
    }),

  // New procedure for listing employer documents
  getDocuments: protectedProcedure
    .input(z.object({
      employerId: z.string().min(1),
      category: z.string().optional(),
      page: z.number().int().positive().default(1),
      limit: z.number().int().positive().default(10)
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Calculate pagination
        const skip = (input.page - 1) * input.limit;
        
        // Build where clause
        const where: Prisma.DocumentWhereInput = {
          tenantId: user.tenantId,
          employerId: input.employerId
        };
        
        // Add category filter if provided
        if (input.category) {
          where.category = input.category;
        }
        
        // Get total count for pagination
        const totalCount = await db.document.count({ where });
        
        // Get documents
        const documents = await db.document.findMany({
          where,
          select: {
            id: true,
            title: true,
            fileName: true,
            fileType: true,
            fileSize: true,
            url: true,
            category: true,
            uploadedAt: true,
            updatedAt: true
          },
          orderBy: {
            uploadedAt: "desc"
          },
          skip,
          take: input.limit
        });
        
        return {
          documents,
          totalCount,
          pageCount: Math.ceil(totalCount / input.limit)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.employerId },
          "Error fetching documents"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch documents",
          cause: error,
        });
      }
    }),

  // New procedure for fetching employees of an employer
  getEmployees: protectedProcedure
    .input(z.object({
      employerId: z.string().min(1),
      page: z.number().int().positive().default(1),
      limit: z.number().int().positive().default(10),
      search: z.string().optional(),
    }))
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;
        
        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true }
        });
        
        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }
        
        // Check if employer exists and belongs to the user's tenant
        const employer = await db.employer.findFirst({
          where: {
            id: input.employerId,
            tenantId: user.tenantId
          }
        });
        
        if (!employer) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Employer not found",
          });
        }
        
        // Calculate pagination
        const skip = (input.page - 1) * input.limit;
        
        // Build where clause
        const where: Prisma.EmployeeWhereInput = {
          employerId: input.employerId
        };
        
        // Add search filter if provided (minimum 2 characters to avoid too many queries)
        if (input.search && input.search.trim().length >= 2) {
          const searchTerm = input.search.trim();
          where.OR = [
            { firstName: { contains: searchTerm, mode: 'insensitive' } },
            { lastName: { contains: searchTerm, mode: 'insensitive' } },
            { nationalId: { contains: searchTerm, mode: 'insensitive' } }
          ];
        }
        
        // Get total count for pagination
        const totalCount = await db.employee.count({ where });
        
        // Get employees
        const employees = await db.employee.findMany({
          where,
          select: {
            id: true,
            firstName: true,
            lastName: true,
            nationalId: true,
            status: true,
            contact: true,
            startDate: true,
            endDate: true,
            baseSalary: true,
            profilePictureUrl: true,
            createdAt: true,
            updatedAt: true,
            // Include department info
            department: {
              select: {
                id: true,
                name: true
              }
            },
            // Include latest payslip info
            payslips: {
              orderBy: [
                { year: 'desc' },
                { month: 'desc' }
              ],
              take: 1,
              select: {
                year: true,
                month: true,
                grossPay: true,
                netPay: true,
                status: true
              }
            }
          },
          orderBy: [
            { status: 'desc' },
            { lastName: 'asc' },
            { firstName: 'asc' }
          ],
          skip,
          take: input.limit
        });
        
        return {
          employees: employees.map(employee => ({
            ...employee,
            fullName: `${employee.firstName} ${employee.lastName}`,
            email: employee.contact && typeof employee.contact === 'object' && 'email' in employee.contact 
              ? (employee.contact as any).email 
              : null,
            phone: employee.contact && typeof employee.contact === 'object' && 'phone' in employee.contact 
              ? (employee.contact as any).phone 
              : null,
            latestPayslip: employee.payslips[0] ? {
              period: `${employee.payslips[0].month}/${employee.payslips[0].year}`,
              grossSalary: employee.payslips[0].grossPay,
              netSalary: employee.payslips[0].netPay,
              status: employee.payslips[0].status
            } : null
          })),
          totalCount,
          pageCount: Math.ceil(totalCount / input.limit)
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id, employerId: input.employerId },
          "Error fetching employees"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch employees",
          cause: error,
        });
      }
    })
}); 