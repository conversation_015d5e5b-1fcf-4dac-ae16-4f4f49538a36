"use client";

import React from "react";
import { cn } from "@/lib/utils";

// Original component imports
import { Button as OriginalButton, buttonVariants } from "@/components/ui/button";
import { Badge as OriginalBadge } from "@/components/ui/badge";
import { 
  Tabs as OriginalTabs, 
  <PERSON><PERSON>Content as OriginalTabsContent, 
  <PERSON><PERSON><PERSON><PERSON> as OriginalTabsList, 
  TabsTrigger as OriginalTabsTrigger
} from "@/components/ui/tabs";
import { 
  Table as OriginalTable, 
  TableBody as OriginalTableBody, 
  TableCell as OriginalTableCell, 
  TableHead as OriginalTableHead, 
  TableHeader as OriginalTableHeader, 
  TableRow as OriginalTableRow,
  TableFooter as OriginalTableFooter,
  TableCaption as OriginalTableCaption
} from "@/components/ui/table";
import { 
  Select as OriginalSelect, 
  SelectContent as OriginalSelectContent, 
  SelectItem as OriginalSelectItem, 
  SelectTrigger as OriginalSelectTrigger, 
  SelectValue as OriginalSelectValue,
  SelectGroup as OriginalSelectGroup,
  SelectLabel as OriginalSelectLabel,
  SelectScrollDownButton as OriginalSelectScrollDownButton,
  SelectScrollUpButton as OriginalSelectScrollUpButton,
  SelectSeparator as OriginalSelectSeparator
} from "@/components/ui/select";
import { 
  Alert as OriginalAlert, 
  AlertDescription as OriginalAlertDescription, 
  AlertTitle as OriginalAlertTitle 
} from "@/components/ui/alert";
import {
  AlertDialog as OriginalAlertDialog,
  AlertDialogPortal as OriginalAlertDialogPortal,
  AlertDialogOverlay as OriginalAlertDialogOverlay,
  AlertDialogTrigger as OriginalAlertDialogTrigger,
  AlertDialogContent as OriginalAlertDialogContent,
  AlertDialogHeader as OriginalAlertDialogHeader,
  AlertDialogFooter as OriginalAlertDialogFooter,
  AlertDialogTitle as OriginalAlertDialogTitle,
  AlertDialogDescription as OriginalAlertDialogDescription,
  AlertDialogAction as OriginalAlertDialogAction,
  AlertDialogCancel as OriginalAlertDialogCancel
} from "@/components/ui/alert-dialog";
import {
  Dialog as OriginalDialog,
  DialogClose as OriginalDialogClose,
  DialogContent as OriginalDialogContent,
  DialogDescription as OriginalDialogDescription,
  DialogFooter as OriginalDialogFooter,
  DialogHeader as OriginalDialogHeader,
  DialogOverlay as OriginalDialogOverlay,
  DialogPortal as OriginalDialogPortal,
  DialogTitle as OriginalDialogTitle,
  DialogTrigger as OriginalDialogTrigger
} from "@/components/ui/dialog";
import {
  Sheet as OriginalSheet,
  SheetTrigger as OriginalSheetTrigger,
  SheetPortal as OriginalSheetPortal,
  SheetClose as OriginalSheetClose,
  SheetContent as OriginalSheetContent,
  SheetHeader as OriginalSheetHeader,
  SheetFooter as OriginalSheetFooter,
  SheetTitle as OriginalSheetTitle,
  SheetDescription as OriginalSheetDescription
} from "@/components/ui/sheet";
import {
  Command as OriginalCommand,
  CommandDialog as OriginalCommandDialog,
  CommandInput as OriginalCommandInput,
  CommandList as OriginalCommandList,
  CommandEmpty as OriginalCommandEmpty,
  CommandGroup as OriginalCommandGroup,
  CommandItem as OriginalCommandItem,
  CommandShortcut as OriginalCommandShortcut,
  CommandSeparator as OriginalCommandSeparator
} from "@/components/ui/command";
import {
  DropdownMenu as OriginalDropdownMenu,
  DropdownMenuPortal as OriginalDropdownMenuPortal,
  DropdownMenuTrigger as OriginalDropdownMenuTrigger,
  DropdownMenuContent as OriginalDropdownMenuContent,
  DropdownMenuGroup as OriginalDropdownMenuGroup,
  DropdownMenuLabel as OriginalDropdownMenuLabel,
  DropdownMenuItem as OriginalDropdownMenuItem,
  DropdownMenuCheckboxItem as OriginalDropdownMenuCheckboxItem,
  DropdownMenuRadioGroup as OriginalDropdownMenuRadioGroup,
  DropdownMenuRadioItem as OriginalDropdownMenuRadioItem,
  DropdownMenuSeparator as OriginalDropdownMenuSeparator,
  DropdownMenuShortcut as OriginalDropdownMenuShortcut,
  DropdownMenuSub as OriginalDropdownMenuSub,
  DropdownMenuSubTrigger as OriginalDropdownMenuSubTrigger,
  DropdownMenuSubContent as OriginalDropdownMenuSubContent
} from "@/components/ui/dropdown-menu";
import {
  ContextMenu as OriginalContextMenu,
  ContextMenuTrigger as OriginalContextMenuTrigger,
  ContextMenuContent as OriginalContextMenuContent,
  ContextMenuItem as OriginalContextMenuItem,
  ContextMenuCheckboxItem as OriginalContextMenuCheckboxItem,
  ContextMenuRadioItem as OriginalContextMenuRadioItem,
  ContextMenuLabel as OriginalContextMenuLabel,
  ContextMenuSeparator as OriginalContextMenuSeparator,
  ContextMenuShortcut as OriginalContextMenuShortcut,
  ContextMenuGroup as OriginalContextMenuGroup,
  ContextMenuPortal as OriginalContextMenuPortal,
  ContextMenuSub as OriginalContextMenuSub,
  ContextMenuSubContent as OriginalContextMenuSubContent,
  ContextMenuSubTrigger as OriginalContextMenuSubTrigger,
  ContextMenuRadioGroup as OriginalContextMenuRadioGroup
} from "@/components/ui/context-menu";
import {
  Breadcrumb as OriginalBreadcrumb,
  BreadcrumbList as OriginalBreadcrumbList,
  BreadcrumbItem as OriginalBreadcrumbItem,
  BreadcrumbLink as OriginalBreadcrumbLink,
  BreadcrumbPage as OriginalBreadcrumbPage,
  BreadcrumbSeparator as OriginalBreadcrumbSeparator,
  BreadcrumbEllipsis as OriginalBreadcrumbEllipsis
} from "@/components/ui/breadcrumb";
import {
  Pagination as OriginalPagination,
  PaginationContent as OriginalPaginationContent,
  PaginationLink as OriginalPaginationLink,
  PaginationItem as OriginalPaginationItem,
  PaginationPrevious as OriginalPaginationPrevious,
  PaginationNext as OriginalPaginationNext,
  PaginationEllipsis as OriginalPaginationEllipsis
} from "@/components/ui/pagination";

// RTL Button
export const Button = React.forwardRef<
  React.ElementRef<typeof OriginalButton>,
  React.ComponentPropsWithoutRef<typeof OriginalButton>
>(function Button({ className, ...rest }, ref) {
  return (
    <OriginalButton
      ref={ref}
      {...rest}
      dir="rtl"
      className={cn("flex-row-reverse text-right", className)}
    />
  );
});
Button.displayName = "Button";

// RTL Badge
export const Badge = React.forwardRef<
  React.ElementRef<typeof OriginalBadge>,
  React.ComponentPropsWithoutRef<typeof OriginalBadge>
>(function Badge({ className, ...rest }, ref) {
  return (
    <OriginalBadge
      ref={ref}
      {...rest}
      dir="rtl" 
      className={cn("flex-row-reverse text-right", className)}
    />
  );
});
Badge.displayName = "Badge";

// RTL Tabs Components
export const Tabs = React.forwardRef<
  React.ElementRef<typeof OriginalTabs>,
  React.ComponentPropsWithoutRef<typeof OriginalTabs>
>(function Tabs(props, ref) {
  return <OriginalTabs ref={ref} {...props} dir="rtl" />;
});
Tabs.displayName = "Tabs";

export const TabsContent = React.forwardRef<
  React.ElementRef<typeof OriginalTabsContent>,
  React.ComponentPropsWithoutRef<typeof OriginalTabsContent>
>(function TabsContent(props, ref) {
  return <OriginalTabsContent ref={ref} {...props} />;
});
TabsContent.displayName = "TabsContent";

export const TabsList = React.forwardRef<
  React.ElementRef<typeof OriginalTabsList>,
  React.ComponentPropsWithoutRef<typeof OriginalTabsList>
>(function TabsList({ className, ...props }, ref) {
  return (
    <OriginalTabsList 
      ref={ref}
      {...props}
      className={cn("rtl:flex-row-reverse", className)}
    />
  );
});
TabsList.displayName = "TabsList";

export const TabsTrigger = React.forwardRef<
  React.ElementRef<typeof OriginalTabsTrigger>,
  React.ComponentPropsWithoutRef<typeof OriginalTabsTrigger>
>(function TabsTrigger(props, ref) {
  return <OriginalTabsTrigger ref={ref} {...props} />;
});
TabsTrigger.displayName = "TabsTrigger";

// RTL Table Components
export const Table = React.forwardRef<
  React.ElementRef<typeof OriginalTable>,
  React.ComponentPropsWithoutRef<typeof OriginalTable>
>(function Table(props, ref) {
  return <OriginalTable ref={ref} {...props} dir="rtl" />;
});
Table.displayName = "Table";

export const TableHeader = React.forwardRef<
  React.ElementRef<typeof OriginalTableHeader>,
  React.ComponentPropsWithoutRef<typeof OriginalTableHeader>
>(function TableHeader(props, ref) {
  return <OriginalTableHeader ref={ref} {...props} />;
});
TableHeader.displayName = "TableHeader";

export const TableBody = React.forwardRef<
  React.ElementRef<typeof OriginalTableBody>,
  React.ComponentPropsWithoutRef<typeof OriginalTableBody>
>(function TableBody(props, ref) {
  return <OriginalTableBody ref={ref} {...props} />;
});
TableBody.displayName = "TableBody";

export const TableRow = React.forwardRef<
  React.ElementRef<typeof OriginalTableRow>,
  React.ComponentPropsWithoutRef<typeof OriginalTableRow>
>(function TableRow(props, ref) {
  return <OriginalTableRow ref={ref} {...props} />;
});
TableRow.displayName = "TableRow";

export const TableHead = React.forwardRef<
  React.ElementRef<typeof OriginalTableHead>,
  React.ComponentPropsWithoutRef<typeof OriginalTableHead>
>(function TableHead({ className, ...props }, ref) {
  return (
    <OriginalTableHead 
      ref={ref}
      {...props}
      className={cn("text-right", className)}
    />
  );
});
TableHead.displayName = "TableHead";

export const TableCell = React.forwardRef<
  React.ElementRef<typeof OriginalTableCell>,
  React.ComponentPropsWithoutRef<typeof OriginalTableCell>
>(function TableCell({ className, ...props }, ref) {
  return (
    <OriginalTableCell
      ref={ref} 
      {...props}
      className={cn("text-right", className)}
    />
  );
});
TableCell.displayName = "TableCell";

// For TableFooter, we'll remove the unnecessary dir attribute per feedback
export const TableFooter = React.forwardRef<
  React.ElementRef<typeof OriginalTableFooter>,
  React.ComponentPropsWithoutRef<typeof OriginalTableFooter>
>(function TableFooter({ className, ...props }, ref) {
  return (
    <OriginalTableFooter
      ref={ref}
      {...props}
      className={cn("text-right", className)}
    />
  );
});
TableFooter.displayName = "TableFooter";

// For TableCaption, we'll remove the unnecessary dir attribute per feedback
export const TableCaption = React.forwardRef<
  React.ElementRef<typeof OriginalTableCaption>,
  React.ComponentPropsWithoutRef<typeof OriginalTableCaption>
>(function TableCaption({ className, ...props }, ref) {
  return (
    <OriginalTableCaption 
      ref={ref}
      {...props}
      className={cn("text-right", className)}
    />
  );
});
TableCaption.displayName = "TableCaption";

// RTL Select Components
export function Select(props: React.ComponentProps<typeof OriginalSelect>) {
  return <OriginalSelect {...props} dir="rtl" />;
}

export const SelectTrigger = React.forwardRef<
  React.ElementRef<typeof OriginalSelectTrigger>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectTrigger>
>(function SelectTrigger({ className, ...props }, ref) {
  return (
    <OriginalSelectTrigger 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", className)}
    />
  );
});
SelectTrigger.displayName = "SelectTrigger";

export const SelectValue = React.forwardRef<
  React.ElementRef<typeof OriginalSelectValue>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectValue>
>(function SelectValue({ className, ...props }, ref) {
  return <OriginalSelectValue ref={ref} {...props} dir="rtl" className={cn("text-right", className)} />;
});
SelectValue.displayName = "SelectValue";

export const SelectContent = React.forwardRef<
  React.ElementRef<typeof OriginalSelectContent>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectContent>
>(function SelectContent({ className, ...props }, ref) {
  return <OriginalSelectContent ref={ref} {...props} dir="rtl" className={cn("text-right", className)} />;
});
SelectContent.displayName = "SelectContent";

export const SelectItem = React.forwardRef<
  React.ElementRef<typeof OriginalSelectItem>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectItem>
>(function SelectItem({ className, ...props }, ref) {
  return (
    <OriginalSelectItem 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
SelectItem.displayName = "SelectItem";

export const SelectGroup = React.forwardRef<
  React.ElementRef<typeof OriginalSelectGroup>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectGroup>
>(function SelectGroup({ className, ...props }, ref) {
  return <OriginalSelectGroup ref={ref} {...props} dir="rtl" className={cn("text-right", className)} />;
});
SelectGroup.displayName = "SelectGroup";

export const SelectLabel = React.forwardRef<
  React.ElementRef<typeof OriginalSelectLabel>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectLabel>
>(function SelectLabel({ className, ...props }, ref) {
  return (
    <OriginalSelectLabel 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
SelectLabel.displayName = "SelectLabel";

export const SelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof OriginalSelectScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectScrollDownButton>
>(function SelectScrollDownButton(props, ref) {
  return <OriginalSelectScrollDownButton ref={ref} {...props} />;
});
SelectScrollDownButton.displayName = "SelectScrollDownButton";

export const SelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof OriginalSelectScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectScrollUpButton>
>(function SelectScrollUpButton(props, ref) {
  return <OriginalSelectScrollUpButton ref={ref} {...props} />;
});
SelectScrollUpButton.displayName = "SelectScrollUpButton";

export const SelectSeparator = React.forwardRef<
  React.ElementRef<typeof OriginalSelectSeparator>,
  React.ComponentPropsWithoutRef<typeof OriginalSelectSeparator>
>(function SelectSeparator(props, ref) {
  return <OriginalSelectSeparator ref={ref} {...props} />;
});
SelectSeparator.displayName = "SelectSeparator";

// RTL Alert Components
export const Alert = React.forwardRef<
  React.ElementRef<typeof OriginalAlert>,
  React.ComponentPropsWithoutRef<typeof OriginalAlert>
>(function Alert({ className, ...props }, ref) {
  return (
    <OriginalAlert 
      ref={ref}
      {...props}
      className={cn("grid-cols-[1fr_calc(var(--spacing)*4)] text-right", className)}
      dir="rtl"
    />
  );
});
Alert.displayName = "Alert";

export const AlertTitle = React.forwardRef<
  React.ElementRef<typeof OriginalAlertTitle>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertTitle>
>(function AlertTitle({ className, ...props }, ref) {
  return (
    <OriginalAlertTitle 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
AlertTitle.displayName = "AlertTitle";

export const AlertDescription = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDescription>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDescription>
>(function AlertDescription({ className, ...props }, ref) {
  return (
    <OriginalAlertDescription 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
AlertDescription.displayName = "AlertDescription";

// RTL AlertDialog Components
export function AlertDialog(props: React.ComponentProps<typeof OriginalAlertDialog>) {
  return <OriginalAlertDialog {...props} />;
}

export function AlertDialogPortal(props: React.ComponentProps<typeof OriginalAlertDialogPortal>) {
  return <OriginalAlertDialogPortal {...props} />;
}

export const AlertDialogOverlay = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogOverlay>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogOverlay>
>(function AlertDialogOverlay(props, ref) {
  return <OriginalAlertDialogOverlay ref={ref} {...props} />;
});
AlertDialogOverlay.displayName = "AlertDialogOverlay";

export const AlertDialogTrigger = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogTrigger>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogTrigger>
>(function AlertDialogTrigger(props, ref) {
  return <OriginalAlertDialogTrigger ref={ref} {...props} />;
});
AlertDialogTrigger.displayName = "AlertDialogTrigger";

export const AlertDialogContent = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogContent>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogContent>
>(function AlertDialogContent({ className, ...props }, ref) {
  return (
    <OriginalAlertDialogContent 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
AlertDialogContent.displayName = "AlertDialogContent";

export const AlertDialogHeader = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogHeader>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogHeader>
>(function AlertDialogHeader({ className, ...props }, ref) {
  return (
    <OriginalAlertDialogHeader 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
AlertDialogHeader.displayName = "AlertDialogHeader";

export const AlertDialogFooter = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogFooter>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogFooter>
>(function AlertDialogFooter({ className, ...props }, ref) {
  return (
    <OriginalAlertDialogFooter 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", className)}
    />
  );
});
AlertDialogFooter.displayName = "AlertDialogFooter";

export const AlertDialogTitle = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogTitle>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogTitle>
>(function AlertDialogTitle({ className, ...props }, ref) {
  return (
    <OriginalAlertDialogTitle 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
AlertDialogTitle.displayName = "AlertDialogTitle";

export const AlertDialogDescription = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogDescription>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogDescription>
>(function AlertDialogDescription({ className, ...props }, ref) {
  return (
    <OriginalAlertDialogDescription 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
AlertDialogDescription.displayName = "AlertDialogDescription";

export const AlertDialogAction = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogAction>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogAction>
>(function AlertDialogAction(props, ref) {
  return <OriginalAlertDialogAction ref={ref} {...props} />;
});
AlertDialogAction.displayName = "AlertDialogAction";

export const AlertDialogCancel = React.forwardRef<
  React.ElementRef<typeof OriginalAlertDialogCancel>,
  React.ComponentPropsWithoutRef<typeof OriginalAlertDialogCancel>
>(function AlertDialogCancel(props, ref) {
  return <OriginalAlertDialogCancel ref={ref} {...props} />;
});
AlertDialogCancel.displayName = "AlertDialogCancel";

// RTL Dialog Components
export function Dialog(props: React.ComponentProps<typeof OriginalDialog>) {
  return <OriginalDialog {...props} />;
}

export function DialogPortal(props: React.ComponentProps<typeof OriginalDialogPortal>) {
  return <OriginalDialogPortal {...props} />;
}

export const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof OriginalDialogOverlay>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogOverlay>
>(function DialogOverlay(props, ref) {
  return <OriginalDialogOverlay ref={ref} {...props} />;
});
DialogOverlay.displayName = "DialogOverlay";

export const DialogTrigger = React.forwardRef<
  React.ElementRef<typeof OriginalDialogTrigger>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogTrigger>
>(function DialogTrigger(props, ref) {
  return <OriginalDialogTrigger ref={ref} {...props} />;
});
DialogTrigger.displayName = "DialogTrigger";

export const DialogClose = React.forwardRef<
  React.ElementRef<typeof OriginalDialogClose>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogClose>
>(function DialogClose(props, ref) {
  return <OriginalDialogClose ref={ref} {...props} />;
});
DialogClose.displayName = "DialogClose";

export const DialogContent = React.forwardRef<
  React.ElementRef<typeof OriginalDialogContent>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogContent>
>(function DialogContent({ className, ...props }, ref) {
  return (
    <OriginalDialogContent 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
DialogContent.displayName = "DialogContent";

export const DialogHeader = React.forwardRef<
  React.ElementRef<typeof OriginalDialogHeader>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogHeader>
>(function DialogHeader({ className, ...props }, ref) {
  return (
    <OriginalDialogHeader 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
DialogHeader.displayName = "DialogHeader";

export const DialogFooter = React.forwardRef<
  React.ElementRef<typeof OriginalDialogFooter>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogFooter>
>(function DialogFooter({ className, ...props }, ref) {
  return (
    <OriginalDialogFooter 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", className)}
    />
  );
});
DialogFooter.displayName = "DialogFooter";

export const DialogTitle = React.forwardRef<
  React.ElementRef<typeof OriginalDialogTitle>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogTitle>
>(function DialogTitle({ className, ...props }, ref) {
  return (
    <OriginalDialogTitle 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
DialogTitle.displayName = "DialogTitle";

export const DialogDescription = React.forwardRef<
  React.ElementRef<typeof OriginalDialogDescription>,
  React.ComponentPropsWithoutRef<typeof OriginalDialogDescription>
>(function DialogDescription({ className, ...props }, ref) {
  return (
    <OriginalDialogDescription 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
DialogDescription.displayName = "DialogDescription";

// RTL Sheet Components
export function Sheet(props: React.ComponentProps<typeof OriginalSheet>) {
  return <OriginalSheet {...props} />;
}

export function SheetPortal(props: React.ComponentProps<typeof OriginalSheetPortal>) {
  return <OriginalSheetPortal {...props} />;
}

export const SheetTrigger = React.forwardRef<
  React.ElementRef<typeof OriginalSheetTrigger>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetTrigger>
>(function SheetTrigger(props, ref) {
  return <OriginalSheetTrigger ref={ref} {...props} />;
});
SheetTrigger.displayName = "SheetTrigger";

export const SheetClose = React.forwardRef<
  React.ElementRef<typeof OriginalSheetClose>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetClose>
>(function SheetClose(props, ref) {
  return <OriginalSheetClose ref={ref} {...props} />;
});
SheetClose.displayName = "SheetClose";

export const SheetContent = React.forwardRef<
  React.ElementRef<typeof OriginalSheetContent>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetContent>
>(function SheetContent({ className, ...props }, ref) {
  return (
    <OriginalSheetContent 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
SheetContent.displayName = "SheetContent";

export const SheetHeader = React.forwardRef<
  React.ElementRef<typeof OriginalSheetHeader>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetHeader>
>(function SheetHeader({ className, ...props }, ref) {
  return (
    <OriginalSheetHeader 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
SheetHeader.displayName = "SheetHeader";

export const SheetFooter = React.forwardRef<
  React.ElementRef<typeof OriginalSheetFooter>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetFooter>
>(function SheetFooter({ className, ...props }, ref) {
  return (
    <OriginalSheetFooter 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", className)}
    />
  );
});
SheetFooter.displayName = "SheetFooter";

export const SheetTitle = React.forwardRef<
  React.ElementRef<typeof OriginalSheetTitle>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetTitle>
>(function SheetTitle({ className, ...props }, ref) {
  return (
    <OriginalSheetTitle 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
SheetTitle.displayName = "SheetTitle";

export const SheetDescription = React.forwardRef<
  React.ElementRef<typeof OriginalSheetDescription>,
  React.ComponentPropsWithoutRef<typeof OriginalSheetDescription>
>(function SheetDescription({ className, ...props }, ref) {
  return (
    <OriginalSheetDescription 
      ref={ref}
      {...props}
      dir="rtl"
      className={cn("text-right", className)}
    />
  );
});
SheetDescription.displayName = "SheetDescription";

// RTL Command Components
export function Command(props: React.ComponentProps<typeof OriginalCommand>) {
  return <OriginalCommand {...props} dir="rtl" className={cn("text-right", props.className)} />;
}

export function CommandDialog(props: React.ComponentProps<typeof OriginalCommandDialog>) {
  return <OriginalCommandDialog {...props} />;
}

export function CommandInput(props: React.ComponentProps<typeof OriginalCommandInput>) {
  return (
    <OriginalCommandInput 
      {...props}
      dir="rtl"
      className={cn("text-right", props.className)}
    />
  );
}

export function CommandList(props: React.ComponentProps<typeof OriginalCommandList>) {
  return <OriginalCommandList {...props} />;
}

export function CommandEmpty(props: React.ComponentProps<typeof OriginalCommandEmpty>) {
  return (
    <OriginalCommandEmpty 
      {...props}
      dir="rtl"
      className={cn("text-right", props.className)}
    />
  );
}

export function CommandGroup(props: React.ComponentProps<typeof OriginalCommandGroup>) {
  return <OriginalCommandGroup {...props} dir="rtl" />;
}

export function CommandItem(props: React.ComponentProps<typeof OriginalCommandItem>) {
  return (
    <OriginalCommandItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function CommandShortcut(props: React.ComponentProps<typeof OriginalCommandShortcut>) {
  return <OriginalCommandShortcut {...props} />;
}

export function CommandSeparator(props: React.ComponentProps<typeof OriginalCommandSeparator>) {
  return <OriginalCommandSeparator {...props} />;
}

// RTL DropdownMenu Components
export function DropdownMenu(props: React.ComponentProps<typeof OriginalDropdownMenu>) {
  return <OriginalDropdownMenu {...props} dir="rtl" />;
}

export function DropdownMenuPortal(props: React.ComponentProps<typeof OriginalDropdownMenuPortal>) {
  return <OriginalDropdownMenuPortal {...props} />;
}

export function DropdownMenuTrigger(props: React.ComponentProps<typeof OriginalDropdownMenuTrigger>) {
  return <OriginalDropdownMenuTrigger {...props} />;
}

export function DropdownMenuContent(props: React.ComponentProps<typeof OriginalDropdownMenuContent>) {
  return (
    <OriginalDropdownMenuContent 
      {...props}
      className={cn("text-right", props.className)}
    />
  );
}

export function DropdownMenuGroup(props: React.ComponentProps<typeof OriginalDropdownMenuGroup>) {
  return <OriginalDropdownMenuGroup {...props} />;
}

export function DropdownMenuLabel(props: React.ComponentProps<typeof OriginalDropdownMenuLabel>) {
  return (
    <OriginalDropdownMenuLabel 
      {...props}
      dir="rtl"
      className={cn("text-right", props.className)}
    />
  );
}

export function DropdownMenuItem(props: React.ComponentProps<typeof OriginalDropdownMenuItem>) {
  return (
    <OriginalDropdownMenuItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function DropdownMenuCheckboxItem(props: React.ComponentProps<typeof OriginalDropdownMenuCheckboxItem>) {
  return (
    <OriginalDropdownMenuCheckboxItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function DropdownMenuRadioGroup(props: React.ComponentProps<typeof OriginalDropdownMenuRadioGroup>) {
  return <OriginalDropdownMenuRadioGroup {...props} />;
}

export function DropdownMenuRadioItem(props: React.ComponentProps<typeof OriginalDropdownMenuRadioItem>) {
  return (
    <OriginalDropdownMenuRadioItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function DropdownMenuSeparator(props: React.ComponentProps<typeof OriginalDropdownMenuSeparator>) {
  return <OriginalDropdownMenuSeparator {...props} />;
}

export function DropdownMenuShortcut(props: React.ComponentProps<typeof OriginalDropdownMenuShortcut>) {
  return <OriginalDropdownMenuShortcut {...props} />;
}

export function DropdownMenuSub(props: React.ComponentProps<typeof OriginalDropdownMenuSub>) {
  return <OriginalDropdownMenuSub {...props} />;
}

export function DropdownMenuSubTrigger(props: React.ComponentProps<typeof OriginalDropdownMenuSubTrigger>) {
  return (
    <OriginalDropdownMenuSubTrigger 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function DropdownMenuSubContent(props: React.ComponentProps<typeof OriginalDropdownMenuSubContent>) {
  return (
    <OriginalDropdownMenuSubContent 
      {...props}
      className={cn("text-right", props.className)}
    />
  );
}

// RTL ContextMenu Components
export function ContextMenu(props: React.ComponentProps<typeof OriginalContextMenu>) {
  return <OriginalContextMenu {...props} dir="rtl" />;
}

export function ContextMenuTrigger(props: React.ComponentProps<typeof OriginalContextMenuTrigger>) {
  return <OriginalContextMenuTrigger {...props} />;
}

export function ContextMenuContent(props: React.ComponentProps<typeof OriginalContextMenuContent>) {
  return (
    <OriginalContextMenuContent 
      {...props}
      className={cn("text-right", props.className)}
    />
  );
}

export function ContextMenuItem(props: React.ComponentProps<typeof OriginalContextMenuItem>) {
  return (
    <OriginalContextMenuItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function ContextMenuCheckboxItem(props: React.ComponentProps<typeof OriginalContextMenuCheckboxItem>) {
  return (
    <OriginalContextMenuCheckboxItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function ContextMenuRadioItem(props: React.ComponentProps<typeof OriginalContextMenuRadioItem>) {
  return (
    <OriginalContextMenuRadioItem 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function ContextMenuLabel(props: React.ComponentProps<typeof OriginalContextMenuLabel>) {
  return (
    <OriginalContextMenuLabel 
      {...props}
      dir="rtl"
      className={cn("text-right", props.className)}
    />
  );
}

export function ContextMenuSeparator(props: React.ComponentProps<typeof OriginalContextMenuSeparator>) {
  return <OriginalContextMenuSeparator {...props} />;
}

export function ContextMenuShortcut(props: React.ComponentProps<typeof OriginalContextMenuShortcut>) {
  return <OriginalContextMenuShortcut {...props} />;
}

export function ContextMenuGroup(props: React.ComponentProps<typeof OriginalContextMenuGroup>) {
  return <OriginalContextMenuGroup {...props} />;
}

export function ContextMenuPortal(props: React.ComponentProps<typeof OriginalContextMenuPortal>) {
  return <OriginalContextMenuPortal {...props} />;
}

export function ContextMenuSub(props: React.ComponentProps<typeof OriginalContextMenuSub>) {
  return <OriginalContextMenuSub {...props} />;
}

export function ContextMenuSubContent(props: React.ComponentProps<typeof OriginalContextMenuSubContent>) {
  return (
    <OriginalContextMenuSubContent 
      {...props}
      className={cn("text-right", props.className)}
    />
  );
}

export function ContextMenuSubTrigger(props: React.ComponentProps<typeof OriginalContextMenuSubTrigger>) {
  return (
    <OriginalContextMenuSubTrigger 
      {...props}
      dir="rtl"
      className={cn("text-right flex-row-reverse", props.className)}
    />
  );
}

export function ContextMenuRadioGroup(props: React.ComponentProps<typeof OriginalContextMenuRadioGroup>) {
  return <OriginalContextMenuRadioGroup {...props} />;
}

// RTL Breadcrumb Components
export function Breadcrumb(props: React.ComponentProps<typeof OriginalBreadcrumb>) {
  return <OriginalBreadcrumb {...props} dir="rtl" />;
}

export function BreadcrumbList(props: React.ComponentProps<typeof OriginalBreadcrumbList>) {
  return (
    <OriginalBreadcrumbList 
      {...props}
      className={cn("flex-row-reverse", props.className)}
    />
  );
}

export function BreadcrumbItem(props: React.ComponentProps<typeof OriginalBreadcrumbItem>) {
  return (
    <OriginalBreadcrumbItem 
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", props.className)}
    />
  );
}

export function BreadcrumbLink(props: React.ComponentProps<typeof OriginalBreadcrumbLink>) {
  return <OriginalBreadcrumbLink {...props} />;
}

export function BreadcrumbPage(props: React.ComponentProps<typeof OriginalBreadcrumbPage>) {
  return <OriginalBreadcrumbPage {...props} />;
}

export function BreadcrumbSeparator(props: React.ComponentProps<typeof OriginalBreadcrumbSeparator>) {
  return <OriginalBreadcrumbSeparator {...props} />;
}

export function BreadcrumbEllipsis(props: React.ComponentProps<typeof OriginalBreadcrumbEllipsis>) {
  return <OriginalBreadcrumbEllipsis {...props} />;
}

// RTL Pagination Components
export function Pagination(props: React.ComponentProps<typeof OriginalPagination>) {
  return <OriginalPagination {...props} dir="rtl" />;
}

export function PaginationContent(props: React.ComponentProps<typeof OriginalPaginationContent>) {
  return (
    <OriginalPaginationContent 
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", props.className)}
    />
  );
}

export function PaginationItem(props: React.ComponentProps<typeof OriginalPaginationItem>) {
  return <OriginalPaginationItem {...props} />;
}

export function PaginationLink(props: React.ComponentProps<typeof OriginalPaginationLink>) {
  return <OriginalPaginationLink {...props} />;
}

export function PaginationPrevious(props: React.ComponentProps<typeof OriginalPaginationPrevious>) {
  return (
    <OriginalPaginationPrevious 
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", props.className)}
    />
  );
}

export function PaginationNext(props: React.ComponentProps<typeof OriginalPaginationNext>) {
  return (
    <OriginalPaginationNext 
      {...props}
      dir="rtl"
      className={cn("flex-row-reverse", props.className)}
    />
  );
}

export function PaginationEllipsis(props: React.ComponentProps<typeof OriginalPaginationEllipsis>) {
  return <OriginalPaginationEllipsis {...props} />;
}

// Re-export original variants and functions
export { buttonVariants }; 