# שיפורי חיפוש עובדים

## מה שופר

### 1. Debouncing בצד הלקוח (Frontend)
- **Hook חדש**: `useEmployerEmployeesWithDebounce` 
- **עיכוב של 300ms** לפני שליחת בקשה לשרת
- **מניעת בקשות מיותרות** בזמן הקלדה מהירה
- **שמירת מצב הקודם** בזמן טעינה חדשה

### 2. אופטימיזציה בצד השרת (Backend)
- **חיפוש מינימלי**: רק עבור 2+ תווים
- **Trimming** של רווחים מיותרים
- **חיפוש רגיש לרישיות** (case-insensitive)
- **חיפוש בשדות מרובים**: שם פרטי, שם משפחה, ת.ז.

### 3. שיפורי UX
- **אינדיקטור טעינה** בזמן חיפוש
- **הודעה מנחה** לחיפוש קצר מדי
- **הודעות מתאימות** כשאין תוצאות
- **איפוס עמוד** אוטומטי בחיפוש חדש

## קבצים שהשתנו

### `src/app/employer-dashboard/hooks.ts`
```typescript
// Hook חדש עם debouncing
export const useEmployerEmployeesWithDebounce = (
  employerId: string,
  initialPage: number = 1,
) => {
  // Debounce של 300ms
  const debouncedSetSearch = useDebouncedCallback(
    (value: string) => {
      if (value.length === 0 || value.length >= 2) {
        setDebouncedSearch(value);
        setPage(1);
      }
    },
    300
  );
  // ...
}
```

### `src/app/employer-dashboard/components/employer-employees-table.tsx`
- שימוש ב-hook החדש
- אינדיקטור טעינה
- הודעות משתמש מתאימות

### `src/server/api/routers/employer.ts`
```typescript
// חיפוש מינימלי של 2+ תווים
if (input.search && input.search.trim().length >= 2) {
  const searchTerm = input.search.trim();
  where.OR = [
    { firstName: { contains: searchTerm, mode: 'insensitive' } },
    { lastName: { contains: searchTerm, mode: 'insensitive' } },
    { nationalId: { contains: searchTerm, mode: 'insensitive' } }
  ];
}
```

## תלויות חדשות
- `use-debounce`: ספרייה לdebouncing

## יתרונות
1. **ביצועים משופרים**: פחות בקשות לשרת
2. **חוויית משתמש טובה יותר**: אין "קפיצות" בתוצאות
3. **חיסכון ברשת**: debouncing מונע בקשות מיותרות
4. **משוב ויזואלי**: המשתמש יודע מתי החיפוש מתבצע
5. **הנחיות ברורות**: המשתמש יודע איך להשתמש בחיפוש

## איך זה עובד
1. המשתמש מקליד בשדה החיפוש
2. אם פחות מ-2 תווים - מוצגת הודעה מנחה
3. אחרי 300ms ללא הקלדה - נשלחת בקשה לשרת
4. השרת מחפש רק אם יש 2+ תווים
5. התוצאות מוצגות עם אינדיקטור טעינה
6. אם אין תוצאות - מוצגת הודעה מתאימה 