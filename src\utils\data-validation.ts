import { isValidIsraeliBusinessId } from "./validation";

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  correctedValue?: unknown;
}

export interface DataConsistencyReport {
  employeeId: string;
  employeeName: string;
  issues: Array<{
    field: string;
    severity: 'ERROR' | 'WARNING' | 'INFO';
    description: string;
    currentValue: unknown;
    suggestedValue?: unknown;
  }>;
  overallScore: number; // 0-100, where 100 is perfect
}

/**
 * Validate Israeli national ID (Teudat Zehut)
 */
export function validateIsraeliNationalId(id: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Remove any non-digit characters for validation
  const cleanId = id.replace(/\D/g, '');
  
  if (cleanId.length !== 9) {
    errors.push('תעודת זהות חייבת להכיל בדיוק 9 ספרות');
    return { isValid: false, errors, warnings };
  }

  // Calculate checksum using Luhn algorithm variant for Israeli IDs
  const digits = cleanId.split('').map(Number);
  let sum = 0;

  for (let i = 0; i < 9; i++) {
    let digit = digits[i];
    
    if (digit === undefined) continue;
    
    // Multiply by 1 or 2 alternately
    if (i % 2 === 1) {
      digit *= 2;
      if (digit > 9) {
        digit = Math.floor(digit / 10) + (digit % 10);
      }
    }
    
    sum += digit;
  }

  const isValid = sum % 10 === 0;
  
  if (!isValid) {
    errors.push('תעודת זהות לא תקינה - ספרת ביקורת שגויה');
  }

  return {
    isValid,
    errors,
    warnings,
    correctedValue: cleanId
  };
}

/**
 * Validate employee ID format consistency
 */
export function validateEmployeeIdFormat(employeeId: string): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let correctedValue: string | undefined;

  // Check if contains quotes or other non-numeric characters
  const hasQuotes = employeeId.includes('"') || employeeId.includes("'");
  const hasNonNumeric = /[^\d]/.test(employeeId);

  if (hasQuotes) {
    warnings.push('מספר עובד מכיל מרכאות - יוסרו אוטומטית');
    correctedValue = employeeId.replace(/["']/g, '');
  }

  if (hasNonNumeric && !hasQuotes) {
    errors.push('מספר עובד חייב להכיל רק ספרות');
  }

  // Check length
  const cleanId = correctedValue || employeeId.replace(/\D/g, '');
  if (cleanId.length < 1) {
    errors.push('מספר עובד לא יכול להיות ריק');
  } else if (cleanId.length > 10) {
    warnings.push('מספר עובד ארוך מהרגיל (מעל 10 ספרות)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    correctedValue: correctedValue || employeeId
  };
}

/**
 * Validate currency format and consistency
 */
export function validateCurrencyFormat(amount: number | string, currency: string = 'ILS'): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  let correctedValue: number | undefined;

  // Convert string to number if needed
  let numericAmount: number;
  if (typeof amount === 'string') {
    // Remove currency symbols and commas
    const cleanAmount = amount.replace(/[₪$€,\s]/g, '');
    numericAmount = parseFloat(cleanAmount);
    
    if (isNaN(numericAmount)) {
      errors.push('סכום לא תקין - לא ניתן להמיר למספר');
      return { isValid: false, errors, warnings };
    }
    
    correctedValue = numericAmount;
  } else {
    numericAmount = amount;
  }

  // Validate decimal places (max 2 for currency)
  const decimalPlaces = (numericAmount.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) {
    warnings.push('יותר מ-2 ספרות אחרי הנקודה העשרונית');
    correctedValue = Math.round(numericAmount * 100) / 100;
  }

  // Validate reasonable ranges
  if (numericAmount < 0) {
    warnings.push('סכום שלילי');
  } else if (numericAmount > 1000000) {
    warnings.push('סכום גבוה מאוד (מעל מיליון)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    correctedValue
  };
}

/**
 * Validate work hours to days ratio
 */
export function validateWorkHoursToDaysRatio(
  workedHours: number,
  workedDays: number
): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (workedDays <= 0) {
    errors.push('מספר ימי עבודה חייב להיות חיובי');
    return { isValid: false, errors, warnings };
  }

  const hoursPerDay = workedHours / workedDays;

  if (hoursPerDay < 4) {
    warnings.push(`שעות עבודה ליום נמוכות מהרגיל (${hoursPerDay.toFixed(1)} שעות)`);
  } else if (hoursPerDay > 12) {
    errors.push(`שעות עבודה ליום חורגות מהמותר (${hoursPerDay.toFixed(1)} שעות)`);
  } else if (hoursPerDay > 10) {
    warnings.push(`שעות עבודה ליום גבוהות (${hoursPerDay.toFixed(1)} שעות)`);
  }

  // Check for reasonable working days per month
  if (workedDays > 31) {
    errors.push('מספר ימי עבודה חורג ממספר הימים בחודש');
  } else if (workedDays > 26) {
    warnings.push('מספר ימי עבודה גבוה מהרגיל (מעל 26 ימים)');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Validate payslip calculation consistency
 */
export function validatePayslipCalculations(payslipData: {
  grossPay: number;
  netPay: number;
  taxDeducted: number;
  insuranceDeducted: number;
  otherDeductions?: number;
  allowances?: number;
}): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  const { grossPay, netPay, taxDeducted, insuranceDeducted, otherDeductions = 0, allowances = 0 } = payslipData;

  // Check for negative values
  if (grossPay < 0) errors.push('שכר ברוטו לא יכול להיות שלילי');
  if (netPay < 0) errors.push('שכר נטו לא יכול להיות שלילי');
  if (taxDeducted < 0) errors.push('ניכוי מס לא יכול להיות שלילי');
  if (insuranceDeducted < 0) errors.push('ניכוי ביטוח לא יכול להיות שלילי');

  // Calculate expected net pay
  const totalDeductions = taxDeducted + insuranceDeducted + otherDeductions;
  const calculatedNet = grossPay - totalDeductions + allowances;
  const variance = Math.abs(calculatedNet - netPay);

  // Check calculation consistency (allow 1 NIS variance for rounding)
  if (variance > 1) {
    errors.push(`אי התאמה בחישוב השכר הנטו: הפרש של ${variance.toFixed(2)} ש"ח`);
  }

  // Check if deductions are reasonable
  const deductionPercentage = (totalDeductions / grossPay) * 100;
  if (deductionPercentage > 70) {
    errors.push(`ניכויים חורגים (${deductionPercentage.toFixed(1)}% מהשכר הברוטו)`);
  } else if (deductionPercentage > 50) {
    warnings.push(`ניכויים גבוהים (${deductionPercentage.toFixed(1)}% מהשכר הברוטו)`);
  }

  // Check tax percentage reasonableness
  const taxPercentage = (taxDeducted / grossPay) * 100;
  if (taxPercentage > 50) {
    warnings.push(`אחוז מס גבוה מהרגיל (${taxPercentage.toFixed(1)}%)`);
  } else if (taxPercentage < 5 && grossPay > 6000) {
    warnings.push(`אחוז מס נמוך מהצפוי (${taxPercentage.toFixed(1)}%)`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    correctedValue: calculatedNet
  };
}

/**
 * Validate foreign worker specific data
 */
export function validateForeignWorkerData(workerData: {
  isForeign: boolean;
  country?: string;
  visaExpiry?: Date;
  visaNumber?: string;
  sector?: string;
  housingDeduction?: number;
  transportDeduction?: number;
}): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!workerData.isForeign) {
    return { isValid: true, errors, warnings };
  }

  // Check required fields for foreign workers
  if (!workerData.country) {
    errors.push('מדינת מוצא חסרה עבור עובד זר');
  }

  if (!workerData.visaExpiry) {
    errors.push('תאריך פקיעת אשרה חסר עבור עובד זר');
  } else {
    const now = new Date();
    const daysUntilExpiry = Math.ceil(
      (workerData.visaExpiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysUntilExpiry < 0) {
      errors.push('אשרת העבודה פגה');
    } else if (daysUntilExpiry <= 30) {
      warnings.push(`אשרת העבודה תפוג בעוד ${daysUntilExpiry} ימים`);
    }
  }

  if (!workerData.visaNumber) {
    warnings.push('מספר אשרה חסר');
  }

  if (!workerData.sector) {
    warnings.push('ענף תעסוקה חסר');
  }

  // Validate deductions
  if (workerData.housingDeduction !== undefined) {
    if (workerData.housingDeduction < 0) {
      errors.push('ניכוי מגורים לא יכול להיות שלילי');
    } else if (workerData.housingDeduction > 2500) {
      warnings.push('ניכוי מגורים גבוה מהרגיל');
    }
  }

  if (workerData.transportDeduction !== undefined) {
    if (workerData.transportDeduction < 0) {
      errors.push('ניכוי תחבורה לא יכול להיות שלילי');
    } else if (workerData.transportDeduction > 1000) {
      warnings.push('ניכוי תחבורה גבוה מהרגיל');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Generate comprehensive data consistency report for an employee
 */
export function generateDataConsistencyReport(employeeData: {
  id: string;
  firstName: string;
  lastName: string;
  nationalId: string;
  isForeign: boolean;
  country?: string;
  visaExpiry?: Date;
  visaNumber?: string;
  sector?: string;
  grossSalary: number;
  workedHours?: number;
  workedDays?: number;
  housingDeduction?: number;
  transportDeduction?: number;
  payslips?: Array<{
    grossPay: number;
    netPay: number;
    taxDeducted: number;
    insuranceDeducted: number;
    otherDeductions?: number;
    allowances?: number;
  }>;
}): DataConsistencyReport {
  const issues: DataConsistencyReport['issues'] = [];
  let totalScore = 100;

  // Validate employee ID format
  const employeeIdValidation = validateEmployeeIdFormat(employeeData.id);
  if (!employeeIdValidation.isValid) {
    issues.push({
      field: 'employeeId',
      severity: 'ERROR',
      description: employeeIdValidation.errors.join(', '),
      currentValue: employeeData.id,
      suggestedValue: employeeIdValidation.correctedValue
    });
    totalScore -= 15;
  } else if (employeeIdValidation.warnings.length > 0) {
    issues.push({
      field: 'employeeId',
      severity: 'WARNING',
      description: employeeIdValidation.warnings.join(', '),
      currentValue: employeeData.id,
      suggestedValue: employeeIdValidation.correctedValue
    });
    totalScore -= 5;
  }

  // Validate national ID
  const nationalIdValidation = validateIsraeliNationalId(employeeData.nationalId);
  if (!nationalIdValidation.isValid) {
    issues.push({
      field: 'nationalId',
      severity: 'ERROR',
      description: nationalIdValidation.errors.join(', '),
      currentValue: employeeData.nationalId,
      suggestedValue: nationalIdValidation.correctedValue
    });
    totalScore -= 20;
  }

  // Validate foreign worker data
  const foreignWorkerValidation = validateForeignWorkerData({
    isForeign: employeeData.isForeign,
    country: employeeData.country,
    visaExpiry: employeeData.visaExpiry,
    visaNumber: employeeData.visaNumber,
    sector: employeeData.sector,
    housingDeduction: employeeData.housingDeduction,
    transportDeduction: employeeData.transportDeduction
  });

  if (!foreignWorkerValidation.isValid) {
    issues.push({
      field: 'foreignWorkerData',
      severity: 'ERROR',
      description: foreignWorkerValidation.errors.join(', '),
      currentValue: 'נתוני עובד זר',
    });
    totalScore -= 15;
  }

  if (foreignWorkerValidation.warnings.length > 0) {
    issues.push({
      field: 'foreignWorkerData',
      severity: 'WARNING',
      description: foreignWorkerValidation.warnings.join(', '),
      currentValue: 'נתוני עובד זר',
    });
    totalScore -= 5;
  }

  // Validate work hours to days ratio
  if (employeeData.workedHours && employeeData.workedDays) {
    const hoursValidation = validateWorkHoursToDaysRatio(
      employeeData.workedHours,
      employeeData.workedDays
    );

    if (!hoursValidation.isValid) {
      issues.push({
        field: 'workHours',
        severity: 'ERROR',
        description: hoursValidation.errors.join(', '),
        currentValue: `${employeeData.workedHours} שעות / ${employeeData.workedDays} ימים`,
      });
      totalScore -= 10;
    }

    if (hoursValidation.warnings.length > 0) {
      issues.push({
        field: 'workHours',
        severity: 'WARNING',
        description: hoursValidation.warnings.join(', '),
        currentValue: `${employeeData.workedHours} שעות / ${employeeData.workedDays} ימים`,
      });
      totalScore -= 3;
    }
  }

  // Validate salary amount
  const salaryValidation = validateCurrencyFormat(employeeData.grossSalary);
  if (!salaryValidation.isValid) {
    issues.push({
      field: 'grossSalary',
      severity: 'ERROR',
      description: salaryValidation.errors.join(', '),
      currentValue: employeeData.grossSalary,
      suggestedValue: salaryValidation.correctedValue
    });
    totalScore -= 10;
  }

  if (salaryValidation.warnings.length > 0) {
    issues.push({
      field: 'grossSalary',
      severity: 'WARNING',
      description: salaryValidation.warnings.join(', '),
      currentValue: employeeData.grossSalary,
      suggestedValue: salaryValidation.correctedValue
    });
    totalScore -= 3;
  }

  // Validate payslip calculations
  if (employeeData.payslips && employeeData.payslips.length > 0) {
    employeeData.payslips.forEach((payslip, index) => {
      const payslipValidation = validatePayslipCalculations(payslip);
      
      if (!payslipValidation.isValid) {
        issues.push({
          field: `payslip_${index}`,
          severity: 'ERROR',
          description: `תלוש ${index + 1}: ${payslipValidation.errors.join(', ')}`,
          currentValue: payslip.netPay,
          suggestedValue: payslipValidation.correctedValue
        });
        totalScore -= 8;
      }

      if (payslipValidation.warnings.length > 0) {
        issues.push({
          field: `payslip_${index}`,
          severity: 'WARNING',
          description: `תלוש ${index + 1}: ${payslipValidation.warnings.join(', ')}`,
          currentValue: payslip.netPay,
        });
        totalScore -= 2;
      }
    });
  }

  return {
    employeeId: employeeData.id,
    employeeName: `${employeeData.firstName} ${employeeData.lastName}`,
    issues,
    overallScore: Math.max(0, totalScore)
  };
}

/**
 * Batch validate multiple employees and generate summary report
 */
export function generateBatchValidationReport(
  employees: Array<{
    id: string;
    firstName: string;
    lastName: string;
    nationalId: string;
    isForeign: boolean;
    country?: string;
    visaExpiry?: Date;
    visaNumber?: string;
    sector?: string;
    grossSalary: number;
    workedHours?: number;
    workedDays?: number;
    housingDeduction?: number;
    transportDeduction?: number;
  }>
): {
  totalEmployees: number;
  validEmployees: number;
  employeesWithErrors: number;
  employeesWithWarnings: number;
  averageScore: number;
  criticalIssues: number;
  reports: DataConsistencyReport[];
} {
  const reports = employees.map(employee => generateDataConsistencyReport(employee));
  
  const validEmployees = reports.filter(report => 
    report.issues.filter(issue => issue.severity === 'ERROR').length === 0
  ).length;
  
  const employeesWithErrors = reports.filter(report => 
    report.issues.some(issue => issue.severity === 'ERROR')
  ).length;
  
  const employeesWithWarnings = reports.filter(report => 
    report.issues.some(issue => issue.severity === 'WARNING')
  ).length;
  
  const averageScore = reports.reduce((sum, report) => sum + report.overallScore, 0) / reports.length;
  
  const criticalIssues = reports.reduce((sum, report) => 
    sum + report.issues.filter(issue => issue.severity === 'ERROR').length, 0
  );

  return {
    totalEmployees: employees.length,
    validEmployees,
    employeesWithErrors,
    employeesWithWarnings,
    averageScore,
    criticalIssues,
    reports
  };
} 