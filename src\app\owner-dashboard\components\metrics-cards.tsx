"use client";

import { Building2, Users, Receipt, ArrowUp, ArrowDown, BarChart3 } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";

type MetricsType = {
  activeEmployers: number;
  totalEmployees: number;
  payslipsGenerated: number;
  totalSalaryValue: string;
  metrics: {
    employers: { value: number; change: number; changeType: string };
    employees: { value: number; change: number; changeType: string };
    payslips: { value: number; change: number; changeType: string };
    salaryValue: { value: string; change: number; changeType: string };
  };
};

type MetricsCardsProps = {
  metrics?: MetricsType;
  isLoading: boolean;
};

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 80 } }
};

const MotionCard = motion(Card);

export function MetricsCards({ metrics, isLoading }: MetricsCardsProps) {
  const [counters, setCounters] = useState({
    employers: 0,
    employees: 0,
    payslips: 0,
    salary: ""
  });

  // Animated counter effect
  useEffect(() => {
    if (!metrics) return;

    const duration = 1500;
    let animationFrameId: number;
    const salaryNumericValue = Number(metrics.totalSalaryValue.replace(/[^0-9.]/g, ''));
    
    const start = performance.now();
    const tick = (now: number) => {
      const progress = Math.min((now - start) / duration, 1);
      
      setCounters({
        employers: Math.floor(progress * metrics.activeEmployers),
        employees: Math.floor(progress * metrics.totalEmployees),
        payslips: Math.floor(progress * metrics.payslipsGenerated),
        salary: new Intl.NumberFormat('he-IL', { style: 'currency', currency: 'ILS' })
                .format(progress * salaryNumericValue)
      });

      if (progress < 1) {
        animationFrameId = requestAnimationFrame(tick);
      } else {
        setCounters({
          employers: metrics.activeEmployers,
          employees: metrics.totalEmployees,
          payslips: metrics.payslipsGenerated,
          salary: metrics.totalSalaryValue
        });
      }
    };
    
    animationFrameId = requestAnimationFrame(tick);

    return () => cancelAnimationFrame(animationFrameId);
  }, [metrics]);

  if (isLoading) {
    return <MetricsCardsSkeleton />;
  }

  if (!metrics) {
    return <div>אין נתונים זמינים</div>;
  }

  return (
    <motion.div 
      className="grid gap-4 md:grid-cols-2 lg:grid-cols-4"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <MotionCard 
        variants={item}
        className="group hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30"
        whileHover={{ y: -5 }}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">מעסיקים פעילים</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:scale-125 transition-all duration-300" />
        </CardHeader>
        <CardContent>
          <motion.div 
            className="text-2xl font-bold group-hover:text-primary transition-colors duration-300"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            {counters.employers}
          </motion.div>
          <p className="text-xs text-muted-foreground">
            <span className={`${metrics.metrics.employers.changeType === "increase" ? "text-green-500" : "text-red-500"} inline-flex items-center group-hover:font-bold transition-all duration-300`}>
              {metrics.metrics.employers.changeType === "increase" ? (
                <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              ) : (
                <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              )}
              {metrics.metrics.employers.change > 0 ? "+" : ""}{metrics.metrics.employers.change}
            </span>{" "}
            <span className="group-hover:opacity-75 transition-opacity duration-300">מאז החודש שעבר</span>
          </p>
          <div className="w-0 group-hover:w-full h-0.5 bg-primary mt-3 transition-all duration-500 rounded-full opacity-0 group-hover:opacity-100" />
        </CardContent>
      </MotionCard>
      
      <MotionCard 
        variants={item}
        className="group hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30"
        whileHover={{ y: -5 }}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">סך כל העובדים</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:scale-125 transition-all duration-300" />
        </CardHeader>
        <CardContent>
          <motion.div 
            className="text-2xl font-bold group-hover:text-primary transition-colors duration-300"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            {counters.employees}
          </motion.div>
          <p className="text-xs text-muted-foreground">
            <span className={`${metrics.metrics.employees.changeType === "increase" ? "text-green-500" : "text-red-500"} inline-flex items-center group-hover:font-bold transition-all duration-300`}>
              {metrics.metrics.employees.changeType === "increase" ? (
                <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              ) : (
                <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              )}
              {metrics.metrics.employees.change > 0 ? "+" : ""}{metrics.metrics.employees.change}
            </span>{" "}
            <span className="group-hover:opacity-75 transition-opacity duration-300">מאז החודש שעבר</span>
          </p>
          <div className="w-0 group-hover:w-full h-0.5 bg-primary mt-3 transition-all duration-500 rounded-full opacity-0 group-hover:opacity-100" />
        </CardContent>
      </MotionCard>
      
      <MotionCard 
        variants={item}
        className="group hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30"
        whileHover={{ y: -5 }}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">תלושי שכר שהופקו</CardTitle>
          <Receipt className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:scale-125 transition-all duration-300" />
        </CardHeader>
        <CardContent>
          <motion.div 
            className="text-2xl font-bold group-hover:text-primary transition-colors duration-300"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            {counters.payslips}
          </motion.div>
          <p className="text-xs text-muted-foreground">
            <span className={`${metrics.metrics.payslips.changeType === "increase" ? "text-green-500" : "text-muted-foreground"} inline-flex items-center group-hover:font-bold transition-all duration-300`}>
              {metrics.metrics.payslips.changeType === "increase" ? (
                <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              ) : (
                <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              )}
              {metrics.metrics.payslips.change > 0 ? "+" : ""}{metrics.metrics.payslips.change}%
            </span>{" "}
            <span className="group-hover:opacity-75 transition-opacity duration-300">מהתקופה הקודמת</span>
          </p>
          <div className="w-0 group-hover:w-full h-0.5 bg-primary mt-3 transition-all duration-500 rounded-full opacity-0 group-hover:opacity-100" />
        </CardContent>
      </MotionCard>
      
      <MotionCard 
        variants={item}
        className="group hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30"
        whileHover={{ y: -5 }}
      >
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">ערך שכר כולל</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:scale-125 transition-all duration-300" />
        </CardHeader>
        <CardContent>
          <motion.div 
            className="text-2xl font-bold group-hover:text-primary transition-colors duration-300"
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 100 }}
          >
            {counters.salary}
          </motion.div>
          <p className="text-xs text-muted-foreground">
            <span className={`${metrics.metrics.salaryValue.changeType === "increase" ? "text-green-500" : "text-red-500"} inline-flex items-center group-hover:font-bold transition-all duration-300`}>
              {metrics.metrics.salaryValue.changeType === "increase" ? (
                <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              ) : (
                <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
              )}
              {metrics.metrics.salaryValue.change > 0 ? "+" : ""}{metrics.metrics.salaryValue.change}%
            </span>{" "}
            <span className="group-hover:opacity-75 transition-opacity duration-300">מהתקופה הקודמת</span>
          </p>
          <div className="w-0 group-hover:w-full h-0.5 bg-primary mt-3 transition-all duration-500 rounded-full opacity-0 group-hover:opacity-100" />
        </CardContent>
      </MotionCard>
    </motion.div>
  );
}

function MetricsCardsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="group hover:shadow-md transition-all duration-300">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24 group-hover:w-28 transition-all duration-500" />
              <Skeleton className="h-4 w-4 rounded-full group-hover:scale-110 transition-all duration-300" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2 group-hover:w-20 transition-all duration-500" />
              <Skeleton className="h-3 w-32 group-hover:w-36 transition-all duration-500" />
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
} 