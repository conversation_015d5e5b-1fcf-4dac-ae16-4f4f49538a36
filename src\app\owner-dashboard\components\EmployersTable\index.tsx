"use client";

import { Card } from "@/components/ui/card";
import { EmployersHeader } from "./employers-header";
import { EmployersTableView } from "./employers-table-view";
import { EmployersTablePagination } from "./employers-table-pagination";
import { type EmployersTableProps } from "./types";
import { useDashboardStore } from "../../hooks/useDashboardStore";

export function EmployersTable({
  employers,
  isLoading,
  createEmployer,
  isCreating
}: EmployersTableProps) {
  const { employersPage: page, setEmployersPage: setPage } = useDashboardStore();
  return (
    <div className="space-y-4">
      <EmployersHeader 
        isCreating={isCreating} 
        createEmployer={createEmployer}
      />
      
      <Card>
        <EmployersTableView 
          employers={employers} 
          isLoading={isLoading} 
        />
        <EmployersTablePagination 
          page={page} 
          setPage={setPage} 
        />
      </Card>
    </div>
  );
}

// Export types for convenience
export * from "./types"; 