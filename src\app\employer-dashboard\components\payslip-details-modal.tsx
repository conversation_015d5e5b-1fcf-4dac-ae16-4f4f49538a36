"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit2, Save, X, Download, Send, FileText, CreditCard } from "lucide-react";
import { toast } from "sonner";

interface PayslipDetailsModalProps {
  payslip: any;
  isOpen: boolean;
  onClose: () => void;
  onUpdate?: (payslipId: string, updates: any) => Promise<void>;
  onApprove?: (payslipId: string) => Promise<void>;
  onGeneratePDF?: (payslipId: string) => Promise<void>;
  onSendToEmployee?: (payslipId: string) => Promise<void>;
  onApproveAndPay?: (payslipId: string) => Promise<void>;
}

export function PayslipDetailsModal({
  payslip,
  isOpen,
  onClose,
  onUpdate,
  onApprove,
  onGeneratePDF,
  onSendToEmployee,
  onApproveAndPay
}: PayslipDetailsModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedPayslip, setEditedPayslip] = useState(payslip || {});
  const [isLoading, setIsLoading] = useState(false);

  // Update edited payslip when payslip prop changes
  useEffect(() => {
    if (payslip) {
      setEditedPayslip(payslip);
    }
  }, [payslip]);

  if (!payslip) return null;

  const canEdit = payslip.status === "DRAFT" || payslip.status === "CALCULATED";
  const canApprove = payslip.status === "CALCULATED" && !isEditing;
  const canGeneratePDF = payslip.status === "APPROVED" || payslip.status === "PAID";
  const canApproveAndPay = payslip.status === "DRAFT" || payslip.status === "CALCULATED";

  const handleSave = async () => {
    if (!onUpdate) return;

    setIsLoading(true);
    try {
      await onUpdate(payslip.id, {
        grossPay: parseFloat(editedPayslip.grossPay),
        netPay: parseFloat(editedPayslip.netPay),
        taxDeducted: parseFloat(editedPayslip.taxDeducted),
        insuranceDeducted: parseFloat(editedPayslip.insuranceDeducted),
        otherDeductions: editedPayslip.otherDeductions ? parseFloat(editedPayslip.otherDeductions) : null,
        allowances: editedPayslip.allowances ? parseFloat(editedPayslip.allowances) : null,
      });
      setIsEditing(false);
      toast.success("תלוש השכר עודכן בהצלחה");
    } catch (error) {
      toast.error("שגיאה בעדכון תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async () => {
    if (!onApprove) return;

    setIsLoading(true);
    try {
      await onApprove(payslip.id);
      toast.success("תלוש השכר אושר בהצלחה");
    } catch (error) {
      toast.error("שגיאה באישור תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePDF = async () => {
    if (!onGeneratePDF) return;

    setIsLoading(true);
    try {
      await onGeneratePDF(payslip.id);
      toast.success("PDF נוצר בהצלחה");
    } catch (error) {
      toast.error("שגיאה ביצירת PDF");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendToEmployee = async () => {
    if (!onSendToEmployee) return;

    setIsLoading(true);
    try {
      await onSendToEmployee(payslip.id);
      toast.success("תלוש השכר נשלח לעובד בהצלחה");
    } catch (error) {
      toast.error("שגיאה בשליחת תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const handleApproveAndPay = async () => {
    if (!onApproveAndPay) return;

    setIsLoading(true);
    try {
      await onApproveAndPay(payslip.id);
      toast.success("תלוש השכר אושר, שולם ונשלח בהצלחה");
    } catch (error) {
      toast.error("שגיאה באישור ותשלום תלוש השכר");
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number | string | null | undefined | { toString(): string }) => {
    if (!amount) return "₪0";
    const num = typeof amount === 'string' ? parseFloat(amount) :
                typeof amount === 'number' ? amount :
                parseFloat(amount.toString());
    return new Intl.NumberFormat("he-IL", {
      style: "currency",
      currency: "ILS",
    }).format(num);
  };

  const earnings = payslip.items?.filter((item: any) => item.type === "EARNING") || [];
  const deductions = payslip.items?.filter((item: any) => item.type === "DEDUCTION") || [];
  const employerContribs = payslip.items?.filter((item: any) => item.type === "EMPLOYER_CONTRIB") || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>תלוש שכר - {payslip.period}</span>
            <div className="flex gap-2">
              {canEdit && !isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  disabled={isLoading}
                >
                  <Edit2 className="h-4 w-4 ml-2" />
                  עריכה
                </Button>
              )}
              {isEditing && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsEditing(false);
                      setEditedPayslip(payslip);
                    }}
                    disabled={isLoading}
                  >
                    <X className="h-4 w-4 ml-2" />
                    ביטול
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSave}
                    disabled={isLoading}
                  >
                    <Save className="h-4 w-4 ml-2" />
                    שמירה
                  </Button>
                </>
              )}
              {canApproveAndPay && !isEditing && (
                <Button
                  size="sm"
                  onClick={handleApproveAndPay}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CreditCard className="h-4 w-4 ml-2" />
                  אישור, תשלום ושליחת SMS
                </Button>
              )}
              {canApprove && (
                <Button
                  size="sm"
                  onClick={handleApprove}
                  disabled={isLoading}
                >
                  <FileText className="h-4 w-4 ml-2" />
                  אישור
                </Button>
              )}
              {canGeneratePDF && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleGeneratePDF}
                    disabled={isLoading}
                  >
                    <Download className="h-4 w-4 ml-2" />
                    PDF
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSendToEmployee}
                    disabled={isLoading}
                  >
                    <Send className="h-4 w-4 ml-2" />
                    שלח לעובד
                  </Button>
                </>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">סיכום</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between items-center">
                  <span>ברוטו:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={editedPayslip.grossPay}
                      onChange={(e) => setEditedPayslip({...editedPayslip, grossPay: e.target.value})}
                      className="w-24 h-6 text-xs"
                    />
                  ) : (
                    <span className="font-medium">{formatCurrency(payslip.grossPay)}</span>
                  )}
                </div>
                <div className="flex justify-between items-center">
                  <span>מס הכנסה:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={editedPayslip.taxDeducted}
                      onChange={(e) => setEditedPayslip({...editedPayslip, taxDeducted: e.target.value})}
                      className="w-24 h-6 text-xs"
                    />
                  ) : (
                    <span className="font-medium">{formatCurrency(payslip.taxDeducted)}</span>
                  )}
                </div>
                <div className="flex justify-between items-center">
                  <span>ביטוח לאומי:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={editedPayslip.insuranceDeducted}
                      onChange={(e) => setEditedPayslip({...editedPayslip, insuranceDeducted: e.target.value})}
                      className="w-24 h-6 text-xs"
                    />
                  ) : (
                    <span className="font-medium">{formatCurrency(payslip.insuranceDeducted)}</span>
                  )}
                </div>
                {(editedPayslip.otherDeductions || isEditing) && (
                  <div className="flex justify-between items-center">
                    <span>ניכויים אחרים:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={editedPayslip.otherDeductions || ''}
                        onChange={(e) => setEditedPayslip({...editedPayslip, otherDeductions: e.target.value})}
                        className="w-24 h-6 text-xs"
                      />
                    ) : (
                      <span className="font-medium">{formatCurrency(payslip.otherDeductions)}</span>
                    )}
                  </div>
                )}
                {(editedPayslip.allowances || isEditing) && (
                  <div className="flex justify-between items-center">
                    <span>קצבאות:</span>
                    {isEditing ? (
                      <Input
                        type="number"
                        step="0.01"
                        value={editedPayslip.allowances || ''}
                        onChange={(e) => setEditedPayslip({...editedPayslip, allowances: e.target.value})}
                        className="w-24 h-6 text-xs"
                      />
                    ) : (
                      <span className="font-medium">{formatCurrency(payslip.allowances)}</span>
                    )}
                  </div>
                )}
                <Separator className="my-2" />
                <div className="flex justify-between items-center font-semibold">
                  <span>נטו:</span>
                  {isEditing ? (
                    <Input
                      type="number"
                      step="0.01"
                      value={editedPayslip.netPay}
                      onChange={(e) => setEditedPayslip({...editedPayslip, netPay: e.target.value})}
                      className="w-24 h-6 text-xs font-semibold"
                    />
                  ) : (
                    <span>{formatCurrency(payslip.netPay)}</span>
                  )}
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">פרטים</h3>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>סטטוס:</span>
                  <Badge variant={payslip.status === "PAID" || payslip.status === "APPROVED" || payslip.status === "SENT" ? "default" : "secondary"}>
                    {payslip.status === "DRAFT" ? "טיוטה" :
                     payslip.status === "CALCULATED" ? "מחושב" :
                     payslip.status === "APPROVED" ? "מאושר" :
                     payslip.status === "SENT" ? "נשלח" :
                     payslip.status === "PAID" ? "שולם" : "מבוטל"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>תאריך הפקה:</span>
                  <span>{new Date(payslip.issuedAt).toLocaleDateString("he-IL")}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Earnings */}
          {earnings.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">תשלומים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">כמות</TableHead>
                    <TableHead className="text-right">תעריף</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {earnings.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{item.units ? item.units.toString() : "-"}</TableCell>
                      <TableCell>{item.rate ? formatCurrency(item.rate) : "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Deductions */}
          {deductions.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">ניכויים</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">קוד</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {deductions.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.kod || "-"}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Employer Contributions */}
          {employerContribs.length > 0 && (
            <div>
              <h3 className="font-semibold mb-2">הפרשות מעסיק</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">תיאור</TableHead>
                    <TableHead className="text-right">סכום</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {employerContribs.map((item: any) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{formatCurrency(item.amount)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}