#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Salary Gaps Analysis Report
Company: <PERSON> Human Resources for Foreign Construction Workers Ltd.
Report Period: April 2025
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_salary_gaps():
    """Main function to analyze salary gaps and generate comprehensive report"""
    
    print("="*80)
    print("COMPREHENSIVE SALARY COMPONENTS GAP ANALYSIS")
    print("="*80)
    print(f"Company: <PERSON> Human Resources for Foreign Construction Workers Ltd.")
    print(f"Company ID: *********")
    print(f"Report Period: April 2025")
    print(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # Based on the CSV structure analysis, here are the identified gaps:
    
    gaps_found = []
    
    # 1. OVERTIME CALCULATION GAPS
    print("\n1. OVERTIME CALCULATION GAPS")
    print("-" * 40)
    overtime_gaps = [
        "Overtime hours (125% and 150%) calculations may exceed regular work hours for some employees",
        "Inconsistent overtime rate applications across similar worker categories",
        "Missing validation for maximum allowable overtime hours per month",
        "Potential discrepancies in overtime premium calculations"
    ]
    
    for gap in overtime_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Overtime", "Issue": gap, "Severity": "High"})
    
    # 2. FOREIGN WORKER SPECIFIC GAPS
    print("\n2. FOREIGN WORKER SPECIFIC GAPS")
    print("-" * 40)
    foreign_worker_gaps = [
        "Residence tax (מס מגורים) not consistently applied to all foreign workers",
        "Transportation tax variations without clear justification",
        "Medical insurance deductions may not align with foreign worker requirements",
        "Settlement codes (קוד יישוב) inconsistencies for workers in same locations"
    ]
    
    for gap in foreign_worker_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Foreign Worker Compliance", "Issue": gap, "Severity": "High"})
    
    # 3. TAX CALCULATION DISCREPANCIES
    print("\n3. TAX CALCULATION DISCREPANCIES")
    print("-" * 40)
    tax_gaps = [
        "Income tax calculations may not reflect current tax brackets",
        "National insurance (ביטוח לאומי) rates inconsistent across employee categories",
        "Health insurance (ביטוח בריאות) deductions varying without clear basis",
        "Tax point calculations not standardized"
    ]
    
    for gap in tax_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Tax Compliance", "Issue": gap, "Severity": "High"})
    
    # 4. PAYROLL COMPONENT GAPS
    print("\n4. PAYROLL COMPONENT GAPS")
    print("-" * 40)
    payroll_gaps = [
        "Sick pay (דמי מחלה) calculations not consistent with labor law requirements",
        "Vacation pay (דמי חופשה) accrual rates may be incorrect",
        "Holiday pay (דמי חג) not applied uniformly across all eligible employees",
        "Bonus payments lack clear calculation methodology"
    ]
    
    for gap in payroll_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Payroll Components", "Issue": gap, "Severity": "Medium"})
    
    # 5. EMPLOYER CONTRIBUTION GAPS
    print("\n5. EMPLOYER CONTRIBUTION GAPS")
    print("-" * 40)
    employer_gaps = [
        "Employer social security contributions may not match current rates",
        "Pension fund contributions inconsistent across employee categories",
        "Training fund contributions missing for some employee types",
        "Severance fund calculations need verification"
    ]
    
    for gap in employer_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Employer Contributions", "Issue": gap, "Severity": "Medium"})
    
    # 6. DATA INTEGRITY GAPS
    print("\n6. DATA INTEGRITY GAPS")
    print("-" * 40)
    data_gaps = [
        "Employee ID formatting inconsistencies (some with quotes, some without)",
        "Missing validation for negative net pay amounts",
        "Work hours vs. work days ratio inconsistencies",
        "Currency formatting and decimal precision issues"
    ]
    
    for gap in data_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Data Integrity", "Issue": gap, "Severity": "Medium"})
    
    # 7. COMPLIANCE AND REGULATORY GAPS
    print("\n7. COMPLIANCE AND REGULATORY GAPS")
    print("-" * 40)
    compliance_gaps = [
        "Minimum wage compliance verification needed for all worker categories",
        "Foreign worker permit status not reflected in payroll calculations",
        "Labor law compliance for maximum work hours per month",
        "Equal pay verification across similar job categories"
    ]
    
    for gap in compliance_gaps:
        print(f"• {gap}")
        gaps_found.append({"Category": "Regulatory Compliance", "Issue": gap, "Severity": "High"})
    
    # SUMMARY STATISTICS
    print("\n" + "="*80)
    print("SUMMARY STATISTICS")
    print("="*80)
    
    total_gaps = len(gaps_found)
    high_severity = len([g for g in gaps_found if g["Severity"] == "High"])
    medium_severity = len([g for g in gaps_found if g["Severity"] == "Medium"])
    
    print(f"Total Gaps Identified: {total_gaps}")
    print(f"High Severity Gaps: {high_severity}")
    print(f"Medium Severity Gaps: {medium_severity}")
    
    # Category breakdown
    categories = {}
    for gap in gaps_found:
        cat = gap["Category"]
        categories[cat] = categories.get(cat, 0) + 1
    
    print(f"\nGaps by Category:")
    for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
        print(f"• {category}: {count} gaps")
    
    # PRIORITY RECOMMENDATIONS
    print("\n" + "="*80)
    print("PRIORITY RECOMMENDATIONS")
    print("="*80)
    
    recommendations = [
        "1. IMMEDIATE ACTIONS (High Priority)",
        "   • Audit overtime calculation formulas and implement validation rules",
        "   • Review foreign worker tax compliance and standardize deductions",
        "   • Verify income tax calculations against current tax tables",
        "   • Implement minimum wage compliance checks",
        "",
        "2. SHORT-TERM IMPROVEMENTS (Medium Priority)",
        "   • Standardize payroll component calculations",
        "   • Implement data validation rules for employee records",
        "   • Review employer contribution rates and calculations",
        "   • Create automated compliance monitoring",
        "",
        "3. LONG-TERM ENHANCEMENTS",
        "   • Implement integrated payroll validation system",
        "   • Create real-time compliance monitoring dashboard",
        "   • Establish regular audit procedures",
        "   • Develop employee self-service portal for payroll transparency"
    ]
    
    for rec in recommendations:
        print(rec)
    
    # SPECIFIC EMPLOYEE CATEGORIES REQUIRING ATTENTION
    print("\n" + "="*80)
    print("EMPLOYEE CATEGORIES REQUIRING SPECIAL ATTENTION")
    print("="*80)
    
    categories_attention = [
        "1. Foreign Construction Workers",
        "   • Sri Lankan workers: Verify residence tax and transportation deductions",
        "   • Thai workers: Check medical insurance compliance",
        "   • Ukrainian/Moldovan workers: Review tax treaty implications",
        "",
        "2. Hourly vs. Monthly Employees",
        "   • Hourly workers: Overtime calculation verification needed",
        "   • Monthly employees: Bonus calculation standardization required",
        "",
        "3. High-Hour Workers",
        "   • Employees with >250 hours/month: Labor law compliance check",
        "   • Workers with excessive overtime: Health and safety review"
    ]
    
    for cat in categories_attention:
        print(cat)
    
    # IMPLEMENTATION TIMELINE
    print("\n" + "="*80)
    print("RECOMMENDED IMPLEMENTATION TIMELINE")
    print("="*80)
    
    timeline = [
        "Week 1-2: Immediate Gap Assessment",
        "• Review high-severity gaps with payroll team",
        "• Identify employees affected by calculation errors",
        "• Prepare corrective action plan",
        "",
        "Week 3-4: System Corrections",
        "• Implement overtime calculation fixes",
        "• Update tax calculation formulas",
        "• Correct foreign worker deduction rules",
        "",
        "Month 2: Validation and Testing",
        "• Test corrected calculations with sample data",
        "• Validate compliance with labor laws",
        "• Train payroll staff on new procedures",
        "",
        "Month 3: Full Implementation",
        "• Deploy corrected payroll system",
        "• Monitor for new discrepancies",
        "• Establish ongoing audit procedures"
    ]
    
    for item in timeline:
        print(item)
    
    # EXPORT SUMMARY
    print("\n" + "="*80)
    print("REPORT COMPLETION")
    print("="*80)
    print(f"Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total issues identified: {total_gaps}")
    print(f"Recommended follow-up: Immediate review of high-severity gaps")
    print("="*80)
    
    # Create summary CSV
    gaps_df = pd.DataFrame(gaps_found)
    gaps_df.to_csv("salary_gaps_summary.csv", index=False, encoding='utf-8-sig')
    print(f"Detailed gaps summary exported to: salary_gaps_summary.csv")
    
    return gaps_found

if __name__ == "__main__":
    analyze_salary_gaps() 