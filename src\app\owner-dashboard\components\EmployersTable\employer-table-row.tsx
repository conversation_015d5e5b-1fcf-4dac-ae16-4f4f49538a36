"use client";

import { Table<PERSON>ell, TableRow, <PERSON>ge, Button } from "@/components/ui/rtl-components";
import { type Employer } from "@/schema/employer";

type EmployerTableRowProps = {
  employer: Employer;
};

export function EmployerTableRow({ employer }: EmployerTableRowProps) {
  return (
    <TableRow key={employer.id}>
      <TableCell className="font-medium">{employer.name}</TableCell>
      <TableCell>{employer.identifier}</TableCell>
      <TableCell>{employer.employeeCount}</TableCell>
      <TableCell>
        <Badge variant={employer.status === "active" ? "default" : "secondary"}>
          {employer.status === "active" ? "פעיל" : "לא פעיל"}
        </Badge>
      </TableCell>
      <TableCell>{employer.lastPayslip}</TableCell>
      <TableCell>
        <Button variant="outline" size="sm" asChild>
          <a href={`/owner-dashboard/employers/${employer.id}`}>ניהול</a>
        </Button>
      </TableCell>
    </TableRow>
  );
} 