"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";
import { EmployersTable } from "./EmployersTable";
import { UsersTable } from "./UsersTable";
import { ReportsList } from "./reports-list";
import { AuditLogTable } from "./audit-log-table";
import type { Employer, Report, AuditLog } from "@/types";
import type { CreateEmployerInput } from "@/schema/employer";
import type { User, UserFormValues } from "./UsersTable/types";

type DashboardTabsProps = {
  // Props for EmployersTable
  employers?: Employer[];
  isEmployersLoading: boolean;
  createEmployer?: (data: CreateEmployerInput) => void;
  isCreatingEmployer?: boolean;
  
  // Props for UsersTable
  users?: User[];
  isUsersLoading: boolean;
  createUser?: (data: UserFormValues) => Promise<void>;
  isCreatingUser?: boolean;
  
  // Props for ReportsList
  reports?: Report[];
  isReportsLoading: boolean;
  generateReport?: (data: { reportId: string }) => void;
  isGeneratingReport?: boolean;
  
  // Props for AuditLogTable
  logs?: AuditLog[];
  isLogsLoading: boolean;
  
  // General loading state
  isLoading?: boolean;

  // Control which tabs are displayed
  visibleTabs?: ("tenants" | "users" | "reports" | "logs")[];
};

export function DashboardTabs({
  // EmployersTable props
  employers,
  isEmployersLoading,
  createEmployer,
  isCreatingEmployer,

  // UsersTable props
  users,
  isUsersLoading,
  createUser,
  isCreatingUser,

  // ReportsList props
  reports,
  isReportsLoading,
  generateReport,
  isGeneratingReport,

  // AuditLogTable props
  logs,
  isLogsLoading,

  // General loading state
  isLoading = false,
  visibleTabs = ["tenants", "users", "reports", "logs"]
}: DashboardTabsProps) {
  if (isLoading) {
    return <DashboardTabsSkeleton visibleTabs={visibleTabs} />;
  }

  return (
    <Tabs defaultValue={visibleTabs[0]} className="w-full">
      <TabsList>
        {visibleTabs.includes("tenants") && (
          <TabsTrigger value="tenants">מעסיקים</TabsTrigger>
        )}
        {visibleTabs.includes("users") && (
          <TabsTrigger value="users">משתמשים</TabsTrigger>
        )}
        {visibleTabs.includes("reports") && (
          <TabsTrigger value="reports">דוחות מערכת</TabsTrigger>
        )}
        {visibleTabs.includes("logs") && (
          <TabsTrigger value="logs">יומני ביקורת</TabsTrigger>
        )}
      </TabsList>
      
      {/* Tenants Tab */}
      {visibleTabs.includes("tenants") && (
        <TabsContent value="tenants">
          <EmployersTable
            employers={employers}
            isLoading={isEmployersLoading}
            createEmployer={createEmployer}
            isCreating={isCreatingEmployer}
          />
        </TabsContent>
      )}
      
      {/* Users Tab */}
      {visibleTabs.includes("users") && (
        <TabsContent value="users">
          <UsersTable
            users={users}
            isLoading={isUsersLoading}
            createUser={createUser}
            isCreating={isCreatingUser}
          />
        </TabsContent>
      )}
      
      {/* System Reports Tab */}
      {visibleTabs.includes("reports") && (
        <TabsContent value="reports">
          <ReportsList
            reports={reports}
            isLoading={isReportsLoading}
            generateReport={generateReport}
            isGenerating={isGeneratingReport}
          />
        </TabsContent>
      )}
      
      {/* Audit Logs Tab */}
      {visibleTabs.includes("logs") && (
        <TabsContent value="logs">
          <AuditLogTable
            logs={logs}
            isLoading={isLogsLoading}
          />
        </TabsContent>
      )}
    </Tabs>
  );
}

function DashboardTabsSkeleton({
  visibleTabs,
}: {
  visibleTabs: string[];
}) {
  return (
    <div className="w-full">
      <div className="flex border-b border-gray-200 mb-4">
        {visibleTabs.map((tab) => (
          <Skeleton key={tab} className="h-10 w-24 mx-1" />
        ))}
      </div>
      <div className="p-4">
        <div className="flex justify-between mb-4">
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-9 w-32" />
        </div>
        <div className="space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className="h-12 w-full" />
          ))}
        </div>
      </div>
    </div>
  );
} 