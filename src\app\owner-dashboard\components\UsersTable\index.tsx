"use client";

import { Card } from "@/components/ui/card";
import { UsersHeader } from "./users-header";
import { UsersTableView } from "./users-table-view";
import { TablePagination } from "./users-table-pagination";
import { type UsersTableProps } from "./types";
import { useDashboardStore } from "../../hooks/useDashboardStore";

export function UsersTable({
  users,
  isLoading,
  createUser,
  isCreating
}: UsersTableProps) {
  const { usersPage: page, setUsersPage: setPage } = useDashboardStore();
  return (
    <div className="space-y-4">
      <UsersHeader 
        isCreating={isCreating} 
        createUser={createUser}
      />
      <Card>
        <UsersTableView 
          users={users} 
          isLoading={isLoading} 
        />
        <TablePagination 
          page={page} 
          setPage={setPage} 
        />
      </Card>
    </div>
  );
}

// Re-export for convenience
export * from "./users-header";
export * from "./add-user-dialog";
export * from "./user-form";
export * from "./users-table-view";
export * from "./user-table-row";
export * from "./users-table-pagination";
export * from "./users-table-skeleton";
export * from "./employer-autocomplete";
export * from "./types";
