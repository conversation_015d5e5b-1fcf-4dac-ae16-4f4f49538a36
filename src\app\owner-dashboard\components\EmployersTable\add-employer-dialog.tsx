"use client";

import { 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { EmployerForm } from "./employer-form";
import { type EmployerFormValues } from "./types";

type AddEmployerDialogProps = {
  isCreating?: boolean;
  createEmployer?: (data: EmployerFormValues) => void;
};

export function AddEmployerDialog({ isCreating, createEmployer }: AddEmployerDialogProps) {
  return (
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle className="text-right">הוספת מעסיק חדש</DialogTitle>
        <DialogDescription className="text-right">
          הזן את פרטי המעסיק החדש כאן. לחץ על שמור כדי להוסיף את המעסיק.
        </DialogDescription>
      </DialogHeader>
      <EmployerForm
        onSubmit={createEmployer}
        isSubmitting={isCreating}
      />
    </DialogContent>
  );
} 