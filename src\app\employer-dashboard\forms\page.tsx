"use client";

import React, { useState } from "react";
import { Form101Table } from "../components/form101-table";
import { Form101PreviewModal } from "../components/form101-preview-modal";
import { Form101EditModal } from "../components/form101-edit-modal";
import { DashboardHeader } from "../components/dashboard-header";
import { useForm101Data, useUpdateForm101 } from "../hooks";
import { api } from "@/trpc/react";
import { toast } from "sonner";
import { type Form101 } from "@prisma/client";

interface Form101WithEmployee extends Form101 {
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    nationalId: string;
    email: string | null;
    phone: string | null;
  };
}

export default function FormsManagementPage() {
  const currentYear = new Date().getFullYear();
  const [selectedYear, setSelectedYear] = useState(currentYear);
  const [selectedForm, setSelectedForm] = useState<Form101WithEmployee | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showEdit, setShowEdit] = useState(false);

  // Use the query directly instead of a custom hook
  const { data: forms, isLoading, refetch } = api.employee.getAllForm101.useQuery(
    { year: selectedYear },
    { refetchOnWindowFocus: false }
  );
  
  const { updateForm101, isUpdating } = useUpdateForm101();

  const handleSaveForm = async (data: any) => {
    if (!selectedForm) return;

    try {
      // Convert Form101EditData to the expected format
      const updateData = {
        id: selectedForm.id,
        maritalStatus: data.maritalStatus,
        spouseWorks: data.spouseWorks,
        childrenCount: data.childrenCount,
        childrenUnder5: data.childrenUnder5,
        childrenUnder18: data.childrenUnder18,
        isMainEmployer: data.isMainEmployer,
        hasAdditionalIncome: data.hasAdditionalIncome,
        additionalCreditPoints: data.additionalCreditPoints,
        taxCoordinationNumber: data.taxCoordinationPercent?.toString(),
        overrideTaxRate: data.overrideTaxRate
      };
      
      await updateForm101(updateData);
      toast.success("טופס 101 עודכן בהצלחה");
      setShowEdit(false);
      void refetch();
    } catch (error) {
      toast.error("שגיאה בעדכון טופס 101");
    }
  };

  return (
    <div className="container mx-auto py-8">
      <DashboardHeader
        title="ניהול טפסי 101"
        description="צפייה, עריכה ושליחה לחתימה של טפסי 101"
      />

      <div className="my-8">
        <Form101Table
          forms={forms || []}
          year={selectedYear}
          isLoading={isLoading}
          onViewForm={(form) => {
            setSelectedForm(form);
            setShowPreview(true);
          }}
          onEditForm={(form) => {
            setSelectedForm(form);
            setShowEdit(true);
          }}
          onPreviewForm={(form) => {
            setSelectedForm(form);
            setShowPreview(true);
          }}
        />
      </div>

      {/* Preview Modal */}
      {selectedForm && (
        <Form101PreviewModal
          isOpen={showPreview}
          onClose={() => {
            setShowPreview(false);
            setSelectedForm(null);
          }}
          form101Data={selectedForm}
          employeeName={`${selectedForm.employee.firstName} ${selectedForm.employee.lastName}`}
        />
      )}

      {/* Edit Modal */}
      {selectedForm && (
        <Form101EditModal
          isOpen={showEdit}
          onClose={() => {
            setShowEdit(false);
            setSelectedForm(null);
          }}
          form101Data={selectedForm}
          onSave={handleSaveForm}
        />
      )}
    </div>
  );
} 