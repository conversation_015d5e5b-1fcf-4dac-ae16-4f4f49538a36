import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
vi.mock('@/server/auth', () => ({ auth: vi.fn(() => Promise.resolve(null)) }));
const { createCallerFactory } = await import('../../trpc');
const { employerRouter } = await import('../employer');
const createCaller = createCallerFactory(employerRouter);

const baseCtx = {
  db: {},
  logger: undefined,
  headers: new Headers(),
} as any;

describe('employerRouter.getById', () => {
  it('rejects mismatched employerId for regular user', async () => {
    const dbMock = { user: { findUnique: vi.fn() } } as any;
    const caller = createCaller({
      ...baseCtx,
      db: dbMock,
      session: { user: { id: 'u1', role: 'EMPLOYEE', employerId: 'emp1' } },
    });
    await expect(caller.getById({ id: 'emp2' })).rejects.toBeInstanceOf(Error);
    expect(dbMock.user.findUnique).not.toHaveBeenCalled();
  });
});
