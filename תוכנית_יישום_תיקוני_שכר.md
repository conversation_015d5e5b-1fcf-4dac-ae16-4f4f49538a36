# תוכנית יישום תיקוני מערכת השכר
## אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע"מ

### פרטי התוכנית
- **תאריך התחלה:** 25/05/2025
- **משך זמן משוער:** 90 יום
- **אחראי ראשי:** מנהל משאבי אנוש + מנהל שכר
- **סטטוס:** בתחילת יישום

---

## שלב 1: תיקונים מיידיים (ימים 1-14)

### 1.1 תיקון חישובי שעות נוספות
**בעיות מזוהות:**
- שעות נוספות חורגות משעות עבודה רגילות
- אי עקביות בתעריפי שעות נוספות
- חסרה ולידציה למקסימום שעות נוספות

**פעולות תיקון:**
1. **יום 1-2:** ביקורת נוסחאות חישוב קיימות
2. **יום 3-4:** יצירת כללי ולידציה חדשים
3. **יום 5-7:** בדיקת התאמה לחוק עבודה
4. **יום 8-10:** יישום תיקונים במערכת
5. **יום 11-14:** בדיקות ואימות

**כללי ולידציה חדשים:**
- שעות נוספות 125% + 150% ≤ שעות עבודה רגילות
- מקסימום 67 שעות נוספות לחודש (לפי חוק)
- תעריף אחיד לפי קטגוריית עובד

### 1.2 סטנדרטיזציה ניכויים עובדים זרים
**בעיות מזוהות:**
- מס מגורים לא עקבי
- שינויים במס תחבורה ללא הצדקה
- אי עקביות בקודי יישוב

**פעולות תיקון:**
1. **יום 1-3:** מיפוי כל העובדים הזרים
2. **יום 4-6:** הגדרת תעריפים סטנדרטיים
3. **יום 7-9:** עדכון קודי יישוב
4. **יום 10-12:** יישום במערכת
5. **יום 13-14:** בדיקת תקינות

**תעריפים סטנדרטיים:**
- מס מגורים: 236 ₪ לחודש (כל העובדים הזרים)
- מס תחבורה: לפי מרחק מקום עבודה
- ביטוח רפואי: לפי תקנות משרד הבריאות

### 1.3 עדכון חישובי מיסים
**בעיות מזוהות:**
- חישובי מס הכנסה לא מעודכנים
- שיעורי ביטוח לאומי לא עקביים
- נקודות זיכוי לא מתוקננות

**פעולות תיקון:**
1. **יום 1-2:** עדכון טבלאות מס 2025
2. **יום 3-4:** בדיקת שיעורי ביטוח לאומי ובריאות
3. **יום 5-7:** תיקון נוסחאות חישוב
4. **יום 8-10:** יישום במערכת
5. **יום 11-14:** בדיקות ואימות

---

## שלב 2: תיקונים מערכתיים (ימים 15-30)

### 2.1 תיקון רכיבי שכר
**פעולות:**
- סטנדרטיזציה חישובי דמי מחלה
- תיקון שיעורי צבירת דמי חופשה
- אחידות דמי חג
- הגדרת מתודולוגיה לבונוסים

### 2.2 תיקון הפרשות מעביד
**פעולות:**
- עדכון שיעורי ביטוח לאומי מעביד
- סטנדרטיזציה הפרשות קרן פנסיה
- הוספת הפרשות קרן הכשרה
- אימות חישובי קרן פיצויים

### 2.3 תיקון תקינות נתונים
**פעולות:**
- אחידות פורמט מספרי עובד
- הוספת ולידציה לשכר נטו שלילי
- תיקון יחס שעות עבודה לימי עבודה
- תיקון פורמט מטבע ודיוק עשרוני

---

## שלב 3: ולידציה ובדיקות (ימים 31-60)

### 3.1 בדיקות מערכת
- בדיקת חישובים מתוקנים עם נתוני דוגמה
- אימות ציות לחוקי עבודה
- בדיקת עקביות בין קטגוריות עובדים

### 3.2 הדרכת צוות
- הדרכת צוות שכר על נהלים חדשים
- יצירת מדריכי הפעלה
- הקמת נהלי בקרת איכות

---

## שלב 4: יישום מלא (ימים 61-90)

### 4.1 הפעלת מערכת מתוקנת
- הפעלה הדרגתית של התיקונים
- ניטור אי התאמות חדשות
- תיקונים נוספים לפי הצורך

### 4.2 הקמת נהלי ביקורת
- יצירת נהלי ביקורת שוטפים
- הקמת מערכת התרעות אוטומטית
- הגדרת דוחות בקרה חודשיים

---

## מדדי הצלחה

### מדדים כמותיים
- **0 פערים בחומרה גבוהה** עד יום 30
- **95% דיוק בחישובי שכר** עד יום 60
- **100% ציות רגולטורי** עד יום 90

### מדדים איכותיים
- שביעות רצון צוות שכר
- הפחתת זמן עיבוד שכר
- שיפור שקיפות לעובדים

---

## תקציב משוער

### עלויות פיתוח
- שעות עבודה צוות IT: 200 שעות × 150 ₪ = 30,000 ₪
- ייעוץ חיצוני: 50,000 ₪
- הדרכות: 15,000 ₪

### **סה"כ תקציב:** 95,000 ₪

---

## ניהול סיכונים

### סיכונים מזוהים
1. **עיכוב בלוח זמנים** - סיכוי בינוני
2. **התנגדות לשינוי** - סיכוי נמוך
3. **שגיאות במהלך המעבר** - סיכוי נמוך

### תוכניות מיתון
- גיבויים מלאים לפני כל שינוי
- בדיקות מקבילות במהלך המעבר
- תמיכה טכנית מוגברת

---

## דיווח והתקדמות

### דיווחים שבועיים
- סטטוס התקדמות לפי שלבים
- בעיות שזוהו ופתרונות
- עדכון לוח זמנים

### דיווחים חודשיים
- סיכום הישגים
- מדדי ביצועים
- המלצות להמשך

---

*תוכנית זו תעודכן באופן שוטף בהתאם להתקדמות בפועל* 