import { Pays<PERSON><PERSON><PERSON><PERSON><PERSON>, MaritalStatus, Sector } from "@prisma/client";

// 2025 Tax brackets for Israel
export const TAX_BRACKETS_2025 = [
  { min: 0, max: 8880, rate: 0.10 },
  { min: 8881, max: 12720, rate: 0.14 },
  { min: 12721, max: 20440, rate: 0.20 },
  { min: 20441, max: 42030, rate: 0.31 },
  { min: 42031, max: 54130, rate: 0.35 },
  { min: 54131, max: Infinity, rate: 0.47 }
];

// 2025 National Insurance rates
export const NI_RATES_2025 = {
  employee: {
    resident: 0.07,
    nonResident: 0.0066
  },
  employer: {
    resident: 0.076,
    nonResident: 0.0066
  },
  health: 0.031
};

// Minimum wage 2025
export const MINIMUM_WAGE_2025 = {
  monthly: 5880,
  hourly: 30
};

// Maximum working hours per month
export const MAX_WORKING_HOURS = {
  regular: 186, // Standard monthly hours
  total: 400,   // Maximum total hours including overtime
  overtime: {
    max125: 80,
    max150: 60,
    max175: 40,
    max200: 20
  }
};

// Tax credit points values for 2025
export const TAX_CREDIT_VALUES_2025 = {
  personal: 2.25,
  spouse: 2.25,
  child: 1.5,
  childUnder5: 2.5,
  singleParent: 1.5
};

/**
 * Calculate overtime pay based on hours and rates
 */
export function calculateOvertimePay(
  regularHours: number,
  overtime125: number,
  overtime150: number,
  overtime175: number,
  overtime200: number,
  hourlyRate: number
): {
  regularPay: number;
  overtime125Pay: number;
  overtime150Pay: number;
  overtime175Pay: number;
  overtime200Pay: number;
  totalPay: number;
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Validate maximum hours
  if (regularHours > MAX_WORKING_HOURS.regular) {
    errors.push(`שעות רגילות (${regularHours}) חורגות מהמקסימום המותר (${MAX_WORKING_HOURS.regular})`);
  }
  
  if (overtime125 > MAX_WORKING_HOURS.overtime.max125) {
    errors.push(`שעות נוספות 125% (${overtime125}) חורגות מהמקסימום המותר (${MAX_WORKING_HOURS.overtime.max125})`);
  }
  
  if (overtime150 > MAX_WORKING_HOURS.overtime.max150) {
    errors.push(`שעות נוספות 150% (${overtime150}) חורגות מהמקסימום המותר (${MAX_WORKING_HOURS.overtime.max150})`);
  }
  
  if (overtime175 > MAX_WORKING_HOURS.overtime.max175) {
    errors.push(`שעות נוספות 175% (${overtime175}) חורגות מהמקסימום המותר (${MAX_WORKING_HOURS.overtime.max175})`);
  }
  
  if (overtime200 > MAX_WORKING_HOURS.overtime.max200) {
    errors.push(`שעות נוספות 200% (${overtime200}) חורגות מהמקסימום המותר (${MAX_WORKING_HOURS.overtime.max200})`);
  }
  
  const totalHours = regularHours + overtime125 + overtime150 + overtime175 + overtime200;
  if (totalHours > MAX_WORKING_HOURS.total) {
    errors.push(`סך השעות (${totalHours}) חורג מהמקסימום המותר (${MAX_WORKING_HOURS.total})`);
  }
  
  // Validate minimum wage
  if (hourlyRate < MINIMUM_WAGE_2025.hourly) {
    errors.push(`שכר שעה (${hourlyRate}) נמוך משכר המינימום (${MINIMUM_WAGE_2025.hourly})`);
  }
  
  // Calculate pay
  const regularPay = regularHours * hourlyRate;
  const overtime125Pay = overtime125 * hourlyRate * 1.25;
  const overtime150Pay = overtime150 * hourlyRate * 1.50;
  const overtime175Pay = overtime175 * hourlyRate * 1.75;
  const overtime200Pay = overtime200 * hourlyRate * 2.00;
  const totalPay = regularPay + overtime125Pay + overtime150Pay + overtime175Pay + overtime200Pay;
  
  return {
    regularPay,
    overtime125Pay,
    overtime150Pay,
    overtime175Pay,
    overtime200Pay,
    totalPay,
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Calculate income tax based on 2025 brackets
 */
export function calculateIncomeTax(
  grossSalary: number,
  taxCredits: number,
  isMainEmployer: boolean = true,
  exemptionPercentage: number = 0
): {
  taxableIncome: number;
  taxBeforeCredits: number;
  taxCreditsValue: number;
  finalTax: number;
  effectiveRate: number;
} {
  // Calculate tax credits value
  const taxCreditsValue = taxCredits * TAX_CREDIT_VALUES_2025.personal * 223; // Monthly credit value
  
  // Calculate taxable income after credits
  const taxableIncome = Math.max(0, grossSalary - taxCreditsValue);
  
  // Calculate tax based on brackets
  let taxBeforeCredits = 0;
  let remainingIncome = taxableIncome;
  
  for (const bracket of TAX_BRACKETS_2025) {
    if (remainingIncome <= 0) break;
    
    const taxableInBracket = Math.min(remainingIncome, bracket.max - bracket.min + 1);
    taxBeforeCredits += taxableInBracket * bracket.rate;
    remainingIncome -= taxableInBracket;
  }
  
  // Apply exemption percentage if applicable
  let finalTax = taxBeforeCredits;
  if (exemptionPercentage > 0) {
    finalTax = finalTax * (1 - exemptionPercentage / 100);
  }
  
  // If not main employer, apply different calculation
  if (!isMainEmployer) {
    finalTax = grossSalary * 0.47; // Maximum rate for secondary employment
  }
  
  const effectiveRate = grossSalary > 0 ? (finalTax / grossSalary) * 100 : 0;
  
  return {
    taxableIncome,
    taxBeforeCredits,
    taxCreditsValue,
    finalTax: Math.max(0, finalTax),
    effectiveRate
  };
}

/**
 * Calculate National Insurance contributions
 */
export function calculateNationalInsurance(
  grossSalary: number,
  isResident: boolean = true,
  age: number = 30
): {
  employeeNI: number;
  employerNI: number;
  healthInsurance: number;
  totalNI: number;
  rates: {
    employeeRate: number;
    employerRate: number;
    healthRate: number;
  };
} {
  const rates = {
    employeeRate: isResident ? NI_RATES_2025.employee.resident : NI_RATES_2025.employee.nonResident,
    employerRate: isResident ? NI_RATES_2025.employer.resident : NI_RATES_2025.employer.nonResident,
    healthRate: NI_RATES_2025.health
  };
  
  // Apply age-based adjustments
  if (age >= 67) {
    rates.employeeRate = 0; // No NI for pension age
  }
  
  const employeeNI = grossSalary * rates.employeeRate;
  const employerNI = grossSalary * rates.employerRate;
  const healthInsurance = grossSalary * rates.healthRate;
  const totalNI = employeeNI + employerNI + healthInsurance;
  
  return {
    employeeNI,
    employerNI,
    healthInsurance,
    totalNI,
    rates
  };
}

/**
 * Calculate standardized deductions for foreign workers
 */
export function calculateForeignWorkerDeductions(
  country: string,
  sector: Sector,
  grossSalary: number,
  settlementCode?: string
): {
  housingDeduction: number;
  transportDeduction: number;
  healthInsuranceDeduction: number;
  totalDeductions: number;
  isStandardized: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  
  // Standardized housing deductions by sector
  const standardHousing: Record<Sector, number> = {
    [Sector.CONSTRUCTION]: 1200,
    [Sector.AGRICULTURE]: 1000,
    [Sector.CAREGIVING]: 800,
    [Sector.INDUSTRY]: 1100,
    [Sector.HOSPITALITY]: 900,
    [Sector.TECHNOLOGY]: 1000,
    [Sector.OTHER]: 1000
  };
  
  // Standardized transport deductions by settlement area
  let transportDeduction = 400; // Default
  if (settlementCode) {
    // Jerusalem, Tel Aviv, Haifa - higher transport costs
    if (['1000', '5000', '4000'].includes(settlementCode)) {
      transportDeduction = 600;
    }
    // Peripheral areas - lower transport costs
    else if (['8000', '9000'].includes(settlementCode)) {
      transportDeduction = 300;
    }
  }
  
  const housingDeduction = standardHousing[sector] || standardHousing[Sector.OTHER];
  
  // Health insurance for foreign workers (mandatory)
  const healthInsuranceDeduction = Math.min(grossSalary * 0.05, 500); // Max 500 NIS
  
  const totalDeductions = housingDeduction + transportDeduction + healthInsuranceDeduction;
  
  // Validate deductions don't exceed reasonable percentage of salary
  if (totalDeductions > grossSalary * 0.4) {
    warnings.push(`סך הניכויים (${totalDeductions}) חורג מ-40% מהשכר הברוטו`);
  }
  
  return {
    housingDeduction,
    transportDeduction,
    healthInsuranceDeduction,
    totalDeductions,
    isStandardized: true,
    warnings
  };
}

/**
 * Validate minimum wage compliance
 */
export function validateMinimumWageCompliance(
  grossSalary: number,
  workedHours: number,
  basis: 'MONTHLY' | 'HOURLY'
): {
  isCompliant: boolean;
  minimumRequired: number;
  shortfall: number;
  hourlyRate: number;
} {
  let minimumRequired: number;
  let hourlyRate: number;
  
  if (basis === 'HOURLY') {
    hourlyRate = grossSalary / workedHours;
    minimumRequired = workedHours * MINIMUM_WAGE_2025.hourly;
  } else {
    // Monthly basis
    hourlyRate = grossSalary / (workedHours || MAX_WORKING_HOURS.regular);
    minimumRequired = MINIMUM_WAGE_2025.monthly;
  }
  
  const isCompliant = grossSalary >= minimumRequired && hourlyRate >= MINIMUM_WAGE_2025.hourly;
  const shortfall = Math.max(0, minimumRequired - grossSalary);
  
  return {
    isCompliant,
    minimumRequired,
    shortfall,
    hourlyRate
  };
}

/**
 * Validate working hours compliance
 */
export function validateWorkingHoursCompliance(
  regularHours: number,
  overtimeHours: number
): {
  isCompliant: boolean;
  violations: string[];
  totalHours: number;
} {
  const violations: string[] = [];
  const totalHours = regularHours + overtimeHours;
  
  // Check maximum total hours
  if (totalHours > MAX_WORKING_HOURS.total) {
    violations.push(`סך השעות (${totalHours}) חורג מהמקסימום החוקי (${MAX_WORKING_HOURS.total})`);
  }
  
  // Check regular hours limit
  if (regularHours > MAX_WORKING_HOURS.regular) {
    violations.push(`שעות רגילות (${regularHours}) חורגות מהמקסימום (${MAX_WORKING_HOURS.regular})`);
  }
  
  // Check overtime limits
  if (overtimeHours > 120) { // Maximum 120 overtime hours per month
    violations.push(`שעות נוספות (${overtimeHours}) חורגות מהמקסימום החוקי (120)`);
  }
  
  return {
    isCompliant: violations.length === 0,
    violations,
    totalHours
  };
}

/**
 * Check visa expiry alerts for foreign workers
 */
export function checkVisaExpiryAlerts(
  visaExpiry: Date,
  employeeId: string,
  employeeName: string
): {
  needsAlert: boolean;
  urgency: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  daysUntilExpiry: number;
  message: string;
} {
  const now = new Date();
  const daysUntilExpiry = Math.ceil((visaExpiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  let urgency: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
  let needsAlert = false;
  let message = '';
  
  if (daysUntilExpiry < 0) {
    urgency = 'CRITICAL';
    needsAlert = true;
    message = `אשרת העבודה של ${employeeName} פגה לפני ${Math.abs(daysUntilExpiry)} ימים`;
  } else if (daysUntilExpiry <= 30) {
    urgency = 'CRITICAL';
    needsAlert = true;
    message = `אשרת העבודה של ${employeeName} תפוג בעוד ${daysUntilExpiry} ימים`;
  } else if (daysUntilExpiry <= 60) {
    urgency = 'HIGH';
    needsAlert = true;
    message = `אשרת העבודה של ${employeeName} תפוג בעוד ${daysUntilExpiry} ימים`;
  } else if (daysUntilExpiry <= 90) {
    urgency = 'MEDIUM';
    needsAlert = true;
    message = `אשרת העבודה של ${employeeName} תפוג בעוד ${daysUntilExpiry} ימים`;
  }
  
  return {
    needsAlert,
    urgency,
    daysUntilExpiry,
    message
  };
}

/**
 * Validate payslip calculation consistency
 */
export function validatePayslipConsistency(
  grossPay: number,
  netPay: number,
  taxDeducted: number,
  insuranceDeducted: number,
  otherDeductions: number = 0,
  allowances: number = 0
): {
  isConsistent: boolean;
  calculatedNet: number;
  variance: number;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check for negative values
  if (netPay < 0) {
    errors.push('שכר נטו לא יכול להיות שלילי');
  }
  
  if (grossPay < 0) {
    errors.push('שכר ברוטו לא יכול להיות שלילי');
  }
  
  // Calculate expected net pay
  const totalDeductions = taxDeducted + insuranceDeducted + otherDeductions;
  const calculatedNet = grossPay - totalDeductions + allowances;
  const variance = Math.abs(calculatedNet - netPay);
  
  // Check if deductions exceed gross pay
  if (totalDeductions > grossPay + allowances) {
    errors.push('סך הניכויים חורג מהשכר הברוטו בתוספת הקצבאות');
  }
  
  // Allow small variance for rounding (up to 1 NIS)
  const isConsistent = variance <= 1 && errors.length === 0;
  
  if (variance > 1) {
    errors.push(`אי התאמה בחישוב השכר הנטו: הפרש של ${variance.toFixed(2)} ש"ח`);
  }
  
  return {
    isConsistent,
    calculatedNet,
    variance,
    errors
  };
}

/**
 * Generate compliance report for an employee
 */
export function generateComplianceReport(
  employeeData: {
    id: string;
    name: string;
    isForeign: boolean;
    visaExpiry?: Date;
    grossSalary: number;
    workedHours: number;
    basis: 'MONTHLY' | 'HOURLY';
    country?: string;
    sector?: Sector;
  }
): {
  overallCompliance: boolean;
  issues: Array<{
    category: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
  }>;
  recommendations: string[];
} {
  const issues: Array<{
    category: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
  }> = [];
  const recommendations: string[] = [];
  
  // Check minimum wage compliance
  const minWageCheck = validateMinimumWageCompliance(
    employeeData.grossSalary,
    employeeData.workedHours,
    employeeData.basis
  );
  
  if (!minWageCheck.isCompliant) {
    issues.push({
      category: 'שכר מינימום',
      severity: 'CRITICAL',
      description: `השכר נמוך משכר המינימום בסכום של ${minWageCheck.shortfall} ש"ח`
    });
    recommendations.push('יש להעלות את השכר לרמת שכר המינימום החוקי');
  }
  
  // Check visa expiry for foreign workers
  if (employeeData.isForeign && employeeData.visaExpiry) {
    const visaCheck = checkVisaExpiryAlerts(
      employeeData.visaExpiry,
      employeeData.id,
      employeeData.name
    );
    
    if (visaCheck.needsAlert) {
      issues.push({
        category: 'אשרת עבודה',
        severity: visaCheck.urgency,
        description: visaCheck.message
      });
      
      if (visaCheck.daysUntilExpiry <= 60) {
        recommendations.push('יש להתחיל בהליכי חידוש אשרת העבודה');
      }
    }
  }
  
  // Check working hours compliance
  const hoursCheck = validateWorkingHoursCompliance(
    employeeData.workedHours,
    0 // Simplified - in real implementation, separate overtime hours
  );
  
  if (!hoursCheck.isCompliant) {
    issues.push({
      category: 'שעות עבודה',
      severity: 'HIGH',
      description: hoursCheck.violations.join(', ')
    });
    recommendations.push('יש לוודא ציות למגבלות שעות העבודה החוקיות');
  }
  
  const overallCompliance = issues.filter(issue => 
    issue.severity === 'CRITICAL' || issue.severity === 'HIGH'
  ).length === 0;
  
  return {
    overallCompliance,
    issues,
    recommendations
  };
} 