import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { FileText, Copy, FileCheck } from "lucide-react";

export default function FormsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="container py-8">
      <Tabs defaultValue="form101" className="w-full">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="form101" className="flex items-center space-x-2 rtl:space-x-reverse">
            <FileText className="h-4 w-4" />
            <span>טפסי 101</span>
          </TabsTrigger>
          <TabsTrigger value="form106" className="flex items-center space-x-2 rtl:space-x-reverse">
            <FileCheck className="h-4 w-4" />
            <span>טפסי 106</span>
          </TabsTrigger>
          <TabsTrigger value="otherdocs" className="flex items-center space-x-2 rtl:space-x-reverse">
            <Copy className="h-4 w-4" />
            <span>מסמכים נוספים</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="form101">
          {children}
        </TabsContent>
        
        <TabsContent value="form106">
          <div className="flex items-center justify-center h-64 border rounded-lg">
            <p className="text-muted-foreground">ניהול טפסי 106 יהיה זמין בקרוב</p>
          </div>
        </TabsContent>
        
        <TabsContent value="otherdocs">
          <div className="flex items-center justify-center h-64 border rounded-lg">
            <p className="text-muted-foreground">ניהול מסמכים נוספים יהיה זמין בקרוב</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
} 