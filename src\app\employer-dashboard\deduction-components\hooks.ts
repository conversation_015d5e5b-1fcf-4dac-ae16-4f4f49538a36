"use client";

import { api } from "@/trpc/react";
import { toast } from "sonner";
import type {
  DeductionComponent,
  DeductionComponentFilters,
  DeductionComponentFormData,
} from "./types";

// ============================================
// Deduction Components Hooks
// ============================================

export function useDeductionComponents(filters?: DeductionComponentFilters) {
  return api.deductionComponent.getAll.useQuery(filters);
}

export function useDeductionComponent(id: string) {
  return api.deductionComponent.getById.useQuery(
    { id },
    { enabled: !!id }
  );
}

export function useCreateDeductionComponent() {
  const utils = api.useContext();
  
  return api.deductionComponent.create.useMutation({
    onSuccess: () => {
      utils.deductionComponent.getAll.invalidate();
      toast.success("רכיב ניכוי נוצר בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה ביצירת רכיב ניכוי: ${error.message}`);
    },
  });
}

export function useUpdateDeductionComponent() {
  const utils = api.useContext();
  
  return api.deductionComponent.update.useMutation({
    onSuccess: (data) => {
      utils.deductionComponent.getAll.invalidate();
      utils.deductionComponent.getById.invalidate({ id: data.id });
      toast.success("רכיב ניכוי עודכן בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בעדכון רכיב ניכוי: ${error.message}`);
    },
  });
}

export function useDeleteDeductionComponent() {
  const utils = api.useContext();
  
  return api.deductionComponent.delete.useMutation({
    onSuccess: () => {
      utils.deductionComponent.getAll.invalidate();
      toast.success("רכיב ניכוי נמחק בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה במחיקת רכיב ניכוי: ${error.message}`);
    },
  });
}

// ============================================
// Reports and Analytics Hooks
// ============================================

export function useDeductionChangeReport(fromDate: Date, toDate: Date) {
  return api.deductionComponent.getChangeReport.useQuery(
    { fromDate, toDate },
    { enabled: !!fromDate && !!toDate }
  );
}

export function useDeductionUsageStats() {
  return api.deductionComponent.getUsageStats.useQuery();
}

// ============================================
// Validation Hooks
// ============================================

export function useValidateDeductionComponent(data: DeductionComponentFormData) {
  return api.deductionComponent.validate.useQuery(
    data,
    { enabled: !!data.name && !!data.code }
  );
}

// ============================================
// Bulk Operations Hooks
// ============================================

export function useBulkUpdateDeductionComponents() {
  const utils = api.useContext();
  
  return api.deductionComponent.bulkUpdate.useMutation({
    onSuccess: (data) => {
      utils.deductionComponent.getAll.invalidate();
      toast.success(`${data.updated} רכיבים עודכנו בהצלחה`);
    },
    onError: (error) => {
      toast.error(`שגיאה בעדכון רכיבים: ${error.message}`);
    },
  });
}

export function useDuplicateDeductionComponent() {
  const utils = api.useContext();
  
  return api.deductionComponent.duplicate.useMutation({
    onSuccess: () => {
      utils.deductionComponent.getAll.invalidate();
      toast.success("רכיב ניכוי שוכפל בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בשכפול רכיב ניכוי: ${error.message}`);
    },
  });
}

// ============================================
// Export/Import Hooks
// ============================================

export function useExportDeductionComponents() {
  return api.deductionComponent.export.useMutation({
    onSuccess: (data) => {
      // Trigger download
      const blob = new Blob([JSON.stringify(data, null, 2)], {
        type: "application/json",
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `deduction-components-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success("קובץ רכיבי הניכוי הורד בהצלחה");
    },
    onError: (error) => {
      toast.error(`שגיאה בייצוא: ${error.message}`);
    },
  });
}

export function useImportDeductionComponents() {
  const utils = api.useContext();
  
  return api.deductionComponent.import.useMutation({
    onSuccess: (data) => {
      utils.deductionComponent.getAll.invalidate();
      toast.success(`${data.imported} רכיבים יובאו בהצלחה`);
    },
    onError: (error) => {
      toast.error(`שגיאה בייבוא: ${error.message}`);
    },
  });
} 