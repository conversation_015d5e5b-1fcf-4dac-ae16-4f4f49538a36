"use client";

import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/rtl-components";
import { EmployerOverviewTab } from "./tabs/employer-overview-tab";
import { EmployerEmployeesTab } from "./tabs/employer-employees-tab";
import { EmployerPayrollTab } from "./tabs/employer-payroll-tab";
import { EmployerReportsTab } from "./tabs/employer-reports-tab";
import { EmployerSettingsTab } from "./tabs/employer-settings-tab";
import { type Employer } from "@/schema/employer";

export type EmployerTabsProps = {
  employer: Employer;
};

export function EmployerTabs({ employer }: EmployerTabsProps) {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <Tabs defaultValue="overview" onValueChange={setActiveTab} className="w-full">
      <TabsList className="mb-6">
        <TabsTrigger value="overview">סקירה כללית</TabsTrigger>
        <TabsTrigger value="employees">עובדים</TabsTrigger>
        <TabsTrigger value="payroll">תלושי שכר</TabsTrigger>
        <TabsTrigger value="reports">דוחות</TabsTrigger>
        <TabsTrigger value="settings">הגדרות</TabsTrigger>
      </TabsList>
      
      {/* סקירה כללית */}
      <TabsContent value="overview" className="w-full">
        <EmployerOverviewTab employer={employer} />
      </TabsContent>
      
      {/* עובדים */}
      <TabsContent value="employees">
        <EmployerEmployeesTab employer={employer} />
      </TabsContent>
      
      {/* תלושי שכר */}
      <TabsContent value="payroll">
        <EmployerPayrollTab employer={employer} />
      </TabsContent>
      
      {/* דוחות */}
      <TabsContent value="reports">
        <EmployerReportsTab employer={employer} />
      </TabsContent>
      
      {/* הגדרות */}
      <TabsContent value="settings">
        <EmployerSettingsTab employer={employer} />
      </TabsContent>
    </Tabs>
  );
} 