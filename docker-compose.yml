# docker-compose.yml
version: '3.9'

services:
  db:
    image: postgres:16-alpine          # small, current
    container_name: payroll-postgres
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    ports:
      - "5432:5432"                    # → localhost:5432
    environment:
      POSTGRES_USER: payroll_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}  # defined in .env file
      POSTGRES_DB: payroll_local
    volumes:
      - payroll_pgdata:/var/lib/postgresql/data

volumes:
  payroll_pgdata:                      # named volume keeps data between restarts
