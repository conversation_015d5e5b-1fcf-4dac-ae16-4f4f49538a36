"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/rtl-components";
import { Button } from "@/components/ui/rtl-components";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { PlusCircle } from "lucide-react";
import { EmployersTableSkeleton } from "./employers-table-skeleton";
import { EmployerTableRow } from "./employer-table-row";
import { type Employer } from "@/schema/employer";
import { AddEmployerDialog } from "./add-employer-dialog";
import { type EmployerFormValues } from "./types";

type EmployersTableViewProps = {
  employers?: Employer[];
  isLoading: boolean;
  isCreating?: boolean;
  createEmployer?: (data: EmployerFormValues) => void;
};

export function EmployersTableView({ employers, isLoading, isCreating, createEmployer }: EmployersTableViewProps) {
  return (
    <CardContent className="p-0">
      {isLoading ? (
        <EmployersTableSkeleton />
      ) : employers && employers.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>שם</TableHead>
              <TableHead>מזהה</TableHead>
              <TableHead>עובדים</TableHead>
              <TableHead>סטטוס</TableHead>
              <TableHead>תלוש אחרון</TableHead>
              <TableHead>פעולות</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {employers.map((employer) => (
              <EmployerTableRow key={employer.id} employer={employer} />
            ))}
          </TableBody>
        </Table>
      ) : (
        <div className="p-8 text-center">
          <p className="text-muted-foreground mb-4">אין מעסיקים זמינים במערכת</p>
          <Dialog>
            <DialogTrigger asChild>
              <Button disabled={isCreating}>
                <PlusCircle className="ml-2 h-4 w-4" />
                הוסף מעסיק חדש
              </Button>
            </DialogTrigger>
            <AddEmployerDialog isCreating={isCreating} createEmployer={createEmployer} />
          </Dialog>
        </div>
      )}
    </CardContent>
  );
} 