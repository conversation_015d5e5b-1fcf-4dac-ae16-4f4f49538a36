import { exec } from 'child_process';

console.log("Restarting the development server with NextAuth debug logging enabled...");

// Set environment variables for debugging
process.env.NEXTAUTH_DEBUG = "true";
process.env.DEBUG = "next-auth:*";

// Run the development server
const server = exec('npm run dev', { 
  env: { 
    ...process.env,
    NEXTAUTH_DEBUG: "true",
    DEBUG: "next-auth:*"
  } 
});

server.stdout?.on('data', (data) => {
  console.log(data);
});

server.stderr?.on('data', (data) => {
  console.error(data);
});

server.on('close', (code) => {
  console.log(`Child process exited with code ${code}`);
});

// Make sure we clean up properly
process.on('SIGINT', () => {
  server.kill();
  process.exit();
}); 