import { NextResponse } from "next/server";
import { appRouter } from "@/server/api/root";

/**
 * Creates a safe proxy that handles empty arrays and null/undefined values
 * to prevent "Reduce of empty array with no initial value" errors
 */
function createSafeProxy<T extends object>(obj: T): T {
  return new Proxy(obj, {
    get(target, prop) {
      const value = Reflect.get(target, prop);

      // If value is undefined or null, return an empty object
      if (value === undefined || value === null) {
        return {};
      }

      // Handle empty arrays - the main cause of reduce errors in the panel
      if (Array.isArray(value) && value.length === 0) {
        return [{}]; // Return array with dummy object
      }

      // Recursively wrap objects in the proxy
      if (typeof value === 'object' && value !== null) {
        return createSafeProxy(value);
      }

      return value;
    }
  });
}

export async function GET() {
  if (process.env.NODE_ENV !== "development") {
    return new NextResponse("Not Found", { status: 404 });
  }

  try {
    const { renderTrpcPanel } = await import("trpc-ui");

    // Create a safe version of the router to prevent empty array reduce errors
    const safeAppRouter = createSafeProxy(appRouter);

    const html = renderTrpcPanel(safeAppRouter, {
      url: "/api/trpc", // Default trpc route in nextjs
      transformer: "superjson", // Enabled by default with create-t3-app
    });

    return new NextResponse(html, {
      status: 200,
      headers: [["Content-Type", "text/html"] as [string, string]],
    });
  } catch (error) {
    console.error("Error rendering tRPC panel:", error);
    
    return new NextResponse(
      `<html>
        <head><title>tRPC Panel Error</title></head>
        <body>
          <h1>Error rendering tRPC panel</h1>
          <p>Check the server logs for more details.</p>
          <pre>${error instanceof Error ? error.message : String(error)}</pre>
        </body>
      </html>`,
      {
        status: 500,
        headers: [["Content-Type", "text/html"] as [string, string]],
      }
    );
  }
}