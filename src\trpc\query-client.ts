import {
	QueryClient,
	defaultShouldDehydrateQuery,
} from "@tanstack/react-query";
import SuperJ<PERSON><PERSON> from "superjson";

// Cache configuration constants
export const CACHE_TIMES = {
	// Static/rarely changing data
	STATIC: 10 * 60 * 1000, // 10 minutes
	// User data that changes occasionally
	USER_DATA: 5 * 60 * 1000, // 5 minutes
	// Dashboard metrics and frequently updated data
	DASHBOARD: 2 * 60 * 1000, // 2 minutes
	// Real-time data like audit logs
	REALTIME: 30 * 1000, // 30 seconds
	// Search results and filtered data
	SEARCH: 1 * 60 * 1000, // 1 minute
} as const;

export const createQueryClient = () =>
	new QueryClient({
		defaultOptions: {
			queries: {
				// Enhanced default stale time
				staleTime: CACHE_TIMES.USER_DATA,
				// Keep data in cache longer to improve UX
				gcTime: 15 * 60 * 1000, // 15 minutes (was cacheTime)
				// Retry failed requests with exponential backoff
				retry: (failureCount, error: any) => {
					// Don't retry on 4xx errors (client errors)
					if (error?.status >= 400 && error?.status < 500) {
						return false;
					}
					// Retry up to 3 times for other errors
					return failureCount < 3;
				},
				retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
				// Refetch on window focus for important data
				refetchOnWindowFocus: true,
				// Don't refetch on reconnect by default (we'll handle this per query)
				refetchOnReconnect: false,
				// Background refetching for better UX
				refetchInterval: false, // We'll set this per query type
			},
			mutations: {
				// Retry mutations once on network errors
				retry: (failureCount, error: any) => {
					if (error?.status >= 400 && error?.status < 500) {
						return false;
					}
					return failureCount < 1;
				},
				// Keep mutation results in cache briefly for optimistic updates
				gcTime: 5 * 60 * 1000, // 5 minutes
			},
			dehydrate: {
				serializeData: SuperJSON.serialize,
				shouldDehydrateQuery: (query) =>
					defaultShouldDehydrateQuery(query) ||
					query.state.status === "pending",
			},
			hydrate: {
				deserializeData: SuperJSON.deserialize,
			},
		},
	});

// Query key factories for consistent cache management
export const queryKeys = {
	// Employer-related queries
	employer: {
		all: ['employer'] as const,
		employees: (employerId: string) => ['employer', 'employees', employerId] as const,
		employeesWithSearch: (employerId: string, search?: string, page?: number) => 
			['employer', 'employees', employerId, { search, page }] as const,
		metrics: (employerId: string, period: string) => 
			['employer', 'metrics', employerId, period] as const,
	},
	// User-related queries
	user: {
		all: ['user'] as const,
		list: (page: number, employerId: string) => ['user', 'list', { page, employerId }] as const,
		detail: (id: string) => ['user', 'detail', id] as const,
	},
	// Employee-related queries
	employee: {
		all: ['employee'] as const,
		detail: (id: string) => ['employee', 'detail', id] as const,
		payslips: (id: string, page: number) => ['employee', 'payslips', id, page] as const,
		transactions: (id: string, filters?: any) => ['employee', 'transactions', id, filters] as const,
	},
	// Audit logs
	auditLog: {
		all: ['auditLog'] as const,
		list: (page: number, actionType: string, employerId: string) => 
			['auditLog', 'list', { page, actionType, employerId }] as const,
	},
} as const;

// Cache invalidation helpers
export const cacheUtils = {
	// Invalidate all employer-related data
	invalidateEmployerData: (queryClient: QueryClient, employerId: string) => {
		queryClient.invalidateQueries({ queryKey: queryKeys.employer.employees(employerId) });
		queryClient.invalidateQueries({ queryKey: queryKeys.employer.metrics(employerId, 'current') });
	},
	
	// Invalidate specific employee data
	invalidateEmployeeData: (queryClient: QueryClient, employeeId: string) => {
		queryClient.invalidateQueries({ queryKey: queryKeys.employee.detail(employeeId) });
		queryClient.invalidateQueries({ queryKey: queryKeys.employee.payslips(employeeId, 1) });
	},
	
	// Smart invalidation for user operations
	invalidateUserData: (queryClient: QueryClient, employerId: string, userId?: string) => {
		queryClient.invalidateQueries({ queryKey: queryKeys.user.list(1, employerId) });
		if (userId) {
			queryClient.invalidateQueries({ queryKey: queryKeys.user.detail(userId) });
		}
	},
};
