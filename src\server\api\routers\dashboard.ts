import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";

export const dashboardRouter = createTRPCRouter({
  getMetrics: protectedProcedure
    .input(
      z.object({ period: z.enum(["current", "previous", "quarter", "year"]) })
    )
    .query(async ({ ctx, input }) => {
      try {
        const { db, session } = ctx;
        const userId = session.user.id;

        // Get the tenant ID for the current user
        const user = await db.user.findUnique({
          where: { id: userId },
          select: { tenantId: true },
        });

        if (!user) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Get the start date based on the period
        const now = new Date();
        let startDate = new Date();

        switch (input.period) {
          case "current":
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            break;
          case "previous":
            startDate.setMonth(startDate.getMonth() - 1);
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            break;
          case "quarter":
            startDate.setMonth(Math.floor(startDate.getMonth() / 3) * 3);
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            break;
          case "year":
            startDate.setMonth(0);
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            break;
        }

        // Count of active employers
        const activeEmployers = await db.employer.count({
          where: {
            tenantId: user.tenantId,
          },
        });

        // Count of employees
        const totalEmployees = await db.employee.count({
          where: {
            tenantId: user.tenantId,
            status: "ACTIVE",
          },
        });

        // Count payslips generated in this period
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;

        const payslipsGenerated = await db.payslip.count({
          where: {
            tenantId: user.tenantId,
            issuedAt: {
              gte: startDate,
            },
          },
        });

        // Get total salary value
        const salarySum = await db.payslip.aggregate({
          where: {
            tenantId: user.tenantId,
            issuedAt: {
              gte: startDate,
            },
          },
          _sum: {
            grossPay: true,
          },
        });

        // Calculate change compared to previous period
        // For each metric, get the previous period data
        let previousStartDate = new Date(startDate);
        let previousEndDate = new Date(startDate);

        switch (input.period) {
          case "current":
          case "previous":
            previousStartDate.setMonth(previousStartDate.getMonth() - 1);
            break;
          case "quarter":
            previousStartDate.setMonth(previousStartDate.getMonth() - 3);
            break;
          case "year":
            previousStartDate.setFullYear(previousStartDate.getFullYear() - 1);
            break;
        }

        // Get previous period employee count
        const previousEmployees = await db.employee.count({
          where: {
            tenantId: user.tenantId,
            status: "ACTIVE",
            createdAt: {
              lt: startDate,
            },
          },
        });

        // Get previous period payslips
        const previousPayslips = await db.payslip.count({
          where: {
            tenantId: user.tenantId,
            issuedAt: {
              gte: previousStartDate,
              lt: startDate,
            },
          },
        });

        // Get previous period salary sum
        const previousSalarySum = await db.payslip.aggregate({
          where: {
            tenantId: user.tenantId,
            issuedAt: {
              gte: previousStartDate,
              lt: startDate,
            },
          },
          _sum: {
            grossPay: true,
          },
        });

        // Format the salary value
        const totalSalaryValue = new Intl.NumberFormat("he-IL", {
          style: "currency",
          currency: "ILS",
          maximumFractionDigits: 0,
        }).format(salarySum._sum.grossPay?.toNumber() || 0);

        // Calculate changes
        const employersChange = 0; // For now, we don't track historical employer counts
        const employeesChange = totalEmployees - previousEmployees;
        const payslipsChange =
          payslipsGenerated > 0 && previousPayslips > 0
            ? Math.round(
                ((payslipsGenerated - previousPayslips) / previousPayslips) *
                  100
              )
            : 0;

        const salaryValueChange =
          previousSalarySum._sum.grossPay &&
          previousSalarySum._sum.grossPay.toNumber() > 0
            ? Math.round(
                (((salarySum._sum.grossPay?.toNumber() || 0) -
                  previousSalarySum._sum.grossPay.toNumber()) /
                  previousSalarySum._sum.grossPay.toNumber()) *
                  100
              )
            : 0;

        return {
          activeEmployers,
          totalEmployees,
          payslipsGenerated,
          totalSalaryValue,
          metrics: {
            employers: {
              value: activeEmployers,
              change: employersChange,
              changeType: employersChange >= 0 ? "increase" : "decrease",
            },
            employees: {
              value: totalEmployees,
              change: employeesChange,
              changeType: employeesChange >= 0 ? "increase" : "decrease",
            },
            payslips: {
              value: payslipsGenerated,
              change: payslipsChange,
              changeType: payslipsChange >= 0 ? "increase" : "decrease",
            },
            salaryValue: {
              value: totalSalaryValue,
              change: salaryValueChange,
              changeType: salaryValueChange >= 0 ? "increase" : "decrease",
            },
          },
        };
      } catch (error) {
        ctx.logger?.error(
          { err: error, userId: ctx.session.user.id },
          "Error fetching dashboard metrics"
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch dashboard metrics",
          cause: error,
        });
      }
    }),

  getSystemAlerts: protectedProcedure.query(async ({ ctx }) => {
    try {
      const { db, session } = ctx;
      const userId = session.user.id;

      // Get the tenant ID for the current user
      const user = await db.user.findUnique({
        where: { id: userId },
        select: { tenantId: true },
      });

      if (!user) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Get unread system alerts
      const alerts = await db.alert.findMany({
        where: {
          tenantId: user.tenantId,
          isRead: false,
        },
        select: {
          id: true,
          type: true,
          message: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
      });

      return {
        alerts: alerts.map((alert) => ({
          id: alert.id,
          type: alert.type.toLowerCase(),
          message: alert.message,
        })),
      };
    } catch (error) {
      ctx.logger?.error(
        { err: error, userId: ctx.session.user.id },
        "Error fetching system alerts"
      );
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch system alerts",
        cause: error,
      });
    }
  }),
});
