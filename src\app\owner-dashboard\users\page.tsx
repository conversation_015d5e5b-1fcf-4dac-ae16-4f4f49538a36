"use client";

import { useState, useEffect } from "react";
import { UsersTable } from "../components/UsersTable";
import { api } from "@/trpc/react";
import { type UserFormValues, Role, type User } from "../components/UsersTable/types";
import { toast } from "sonner";
import { TRPCClientError } from "@trpc/client";

// UsersTable component props type
type UsersTableComponentProps = {
  users?: User[];
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  createUser?: (data: UserFormValues) => Promise<void>;
  isCreating?: boolean;
};

export default function UsersPage() {
  const [page, setPage] = useState(1);
  const [usersError, setUsersError] = useState<string | null>(null);

  // Fetch users
  const { 
    data: usersData, 
    isLoading: isLoadingUsers,
    error: usersQueryError
  } = api.user.getAll.useQuery({ page, limit: 10 });
  
  // Handle user query errors
  useEffect(() => {
    if (usersQueryError) {
      const errorMessage = usersQueryError instanceof TRPCClientError 
        ? usersQueryError.message
        : "Unknown error loading users";
      setUsersError(errorMessage);
      toast.error(`Error loading users: ${errorMessage}`);
    } else {
      setUsersError(null);
    }
  }, [usersQueryError]);
  
  // Mutation for creating a new user
  const { mutate: createUser, isPending: isCreating } = api.user.create.useMutation({
    onSuccess: () => {
      toast.success("User created successfully");
    },
    onError: (error) => {
      toast.error("Error creating user: " + error.message);
    }
  });

  // Function to create a new user
  const handleCreateUser = async (data: UserFormValues): Promise<void> => {
    return new Promise<void>((resolve, reject) => {
      createUser(
        { ...data },
        {
          onSuccess: () => resolve(),
          onError: (error) => reject(error)
        }
      );
    });
  };

  // Ensure we have all required parameters
  const tableProps: UsersTableComponentProps = {
    users: usersData?.users as User[] | undefined,
    isLoading: isLoadingUsers,
    page,
    setPage,
    createUser: handleCreateUser,
    isCreating
  };

  return (
    <div className="container py-6">
      {usersError && (
        <div className="mb-4 p-4 bg-red-100 text-red-800 rounded-md">
          Error loading users: {usersError}
        </div>
      )}
      
      <UsersTable {...tableProps} />
    </div>
  );
} 