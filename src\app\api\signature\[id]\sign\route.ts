import { NextRequest, NextResponse } from "next/server";
import { db } from "@/server/db";

// Export route configuration
export const dynamic = 'force-dynamic';

// Define a simplified handler for signing forms
export async function POST(req: NextRequest, context: any) {
  const id = context.params?.id;
  
  if (!id) {
    return new NextResponse(
      JSON.stringify({ success: false, error: "Signature ID is required" }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }

  try {
    const body = await req.json();
    const { signatureData, timestamp } = body;

    if (!signatureData) {
      return new NextResponse(
        JSON.stringify({ success: false, error: "Signature data is required" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Get signature request from database
    const signatureRequest = await db.signatureRequest.findUnique({
      where: { id },
      include: {
        form101: {
          include: {
            employee: true
          }
        }
      }
    });

    if (!signatureRequest) {
      return new NextResponse(
        JSON.stringify({ success: false, error: "Signature request not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }

    // Check if signature request is expired
    const now = new Date();
    const isExpired = now > signatureRequest.expiresAt;

    if (isExpired) {
      return new NextResponse(
        JSON.stringify({ success: false, error: "Signature request has expired" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Check if already signed
    if (signatureRequest.status === 'SIGNED') {
      return new NextResponse(
        JSON.stringify({ success: false, error: "Form has already been signed" }),
        { status: 400, headers: { "Content-Type": "application/json" } }
      );
    }

    // Update signature request status and save signature data
    const updatedSignatureRequest = await db.signatureRequest.update({
      where: { id },
      data: {
        status: 'SIGNED',
        signedAt: new Date(timestamp || Date.now()),
        signatureData: signatureData,
      }
    });

    // Extract data safely
    const form101 = signatureRequest.form101 as any;
    const employee = form101.employee as any;

    // Update Form 101 status
    await db.form101.update({
      where: { id: signatureRequest.form101Id },
      data: {
        status: 'SIGNED',
        signedAt: new Date(timestamp || Date.now()),
      }
    });

    // Log the signature event
    console.log(`Form 101 signed: ${signatureRequest.form101Id} by employee: ${employee.firstName || 'Unknown'} ${employee.lastName || 'Employee'}`);

    return new NextResponse(
      JSON.stringify({
        success: true,
        data: {
          id: updatedSignatureRequest.id,
          status: updatedSignatureRequest.status,
          signedAt: updatedSignatureRequest.signedAt,
        }
      }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );

  } catch (error) {
    console.error("Error signing form:", error);
    return new NextResponse(
      JSON.stringify({ success: false, error: "Internal server error" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
