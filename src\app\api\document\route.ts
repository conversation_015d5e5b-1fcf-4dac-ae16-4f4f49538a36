import { auth } from "@/server/auth";
import { db } from "@/server/db";
import { getSignedDownloadUrl } from "@/server/s3";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import type { Session } from "next-auth";

// Add this function to check document access (implement according to your access control logic)
async function checkDocumentAccess(
	userId: string,
	key: string,
): Promise<boolean> {
	const user = await db.user.findUnique({
		where: { id: userId },
		select: { tenantId: true },
	});

	if (!user) {
		return false;
	}

	const document = await db.document.findFirst({
		where: {
			s3Key: key,
			tenantId: user.tenantId,
		},
		select: { id: true },
	});

	return Boolean(document);
}

export async function GET(request: NextRequest) {
	// Define these outside the try block so they're accessible in the catch block
	let session: Session | null = null;
	const url = new URL(request.url);
	const key = url.searchParams.get("key");

	try {
		// Get the current session
		session = await auth();
		if (!session || !session.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		if (!key) {
			return NextResponse.json(
				{ error: "Missing key parameter" },
				{ status: 400 },
			);
		}

		// Validate key format (e.g., no path traversal, valid characters)
		const keyRegex = /^[a-zA-Z0-9-_/]+\.[a-zA-Z0-9]+$/;
		if (!keyRegex.test(key)) {
			return NextResponse.json(
				{ error: "Invalid key format" },
				{ status: 400 },
			);
		}

		// Check if user has permission to access this document
		const canAccess = await checkDocumentAccess(session.user.id, key);
		if (!canAccess) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Generate a signed URL (1 hour expiry)
		const signedUrl = await getSignedDownloadUrl(key, 3600);

		// Return the signed URL
		return NextResponse.json({ signedUrl });
	} catch (error) {
		// Enhanced error logging with more context
		console.error("Error generating signed URL:", {
			userId: session?.user?.id,
			documentKey: key,
			errorMessage: error instanceof Error ? error.message : String(error),
		});

		return NextResponse.json(
			{ error: "Failed to generate signed URL" },
			{ status: 500 },
		);
	}
}
