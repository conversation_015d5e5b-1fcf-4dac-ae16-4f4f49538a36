import * as z from "zod";

export type User = {
  id: string;
  name: string;
  email: string;
  role: Role;
  employerId: string;
  employerName: string;
  status: "active" | "inactive";
};

// תפקידים לפי schema.prisma - חשוב שהערכים יהיו זהים למה שקיים בפריזמה
export enum Role {
  OWNER = "OWNER",
  ADMIN = "ADMIN",
  ACCOUNTANT = "ACCOUNTANT",
  HR = "HR",
  EMPLOYEE = "EMPLOYEE"
}

export type UsersTableProps = {
  users?: User[];
  isLoading: boolean;
  createUser?: (data: z.infer<typeof userFormSchema>) => Promise<void>;
  isCreating?: boolean;
};

// בקשת יצירת משתמש עם ערכי Enum תקינים
export const userFormSchema = z.object({
  name: z.string().min(1, { message: "שם המשתמש הוא שדה חובה" }),
  email: z.string().email({ message: "אימייל לא תקין" }),
  role: z.nativeEnum(Role, { message: "יש לבחור תפקיד מהרשימה" }),
  employerId: z.string().uuid({ message: "מזהה המעסיק אינו תקין" }),
  status: z.enum(["active", "inactive"], { message: "יש לבחור סטטוס תקין" }),
});

export type UserFormValues = z.infer<typeof userFormSchema>; 