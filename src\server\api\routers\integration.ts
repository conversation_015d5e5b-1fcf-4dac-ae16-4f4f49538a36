import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "@/server/api/trpc";
import { TRPCError } from "@trpc/server";
import { PrismaClient, Prisma, AlertType, PayslipItemType, EmployeeStatus } from "@prisma/client";
import { getOrCreate, findFirstOrCreate } from "@/server/utils/db";

/**
 * PayslipItemKod enum - מיפוי קודי תשלום ותיאורם
 * ערכים אלו מצויים בסכמה בפריזמה כenum מיוחד
 */
const PayslipItemKodValues = [
  "1000", // שעות רגילות 100 %
  "1001", // שעות נוספות 125 %
  "1002", // שעות נוספות 150 %
  "1003", // שעות נוספות 175 %
  "1004", // שעות נוספות 200 %
  "1005", // פרמיה
  "1008", // WEATHER
  "1010", // שווי דיור
  "1011", // שווי ביטוח רפואי
  "1020", // ניכוי מגורים
  "1021", // ניכוי ביטוח רפואי
  "1022", // מפרעה #1
  "1063", // HOLIDAY
  "1077", // השלמה
  "1089", // נעדר
  "1090", // בונוס נוכחות חודשי
  "1091", // בונוס התמדה רבעוני
] as const;

// הגדרת טיפוס PayslipItemKod המשמש בקוד
type PayslipItemKod = keyof typeof PayslipItemKodEnum;
// ממפה בין מספרים לערכי enum עם התחילית K_
const PayslipItemKodEnum = PayslipItemKodValues.reduce((acc, kod) => {
  acc[`K_${kod}` as const] = kod;
  return acc;
}, {} as Record<string, string>);

// Define the payslip schema based on the provided specification
const payslipItemSchema = z.object({
  kod: z.union([
    z.number().transform(val => String(val)),
    z.string()
  ]).refine(val => PayslipItemKodValues.includes(val as any), {
    message: `kod must be one of: ${PayslipItemKodValues.join(', ')}`
  }),
  type: z.enum(["EARNING", "DEDUCTION", "EMPLOYER_CONTRIB", "REIMBURSEMENT"]),
  description: z.string(),
  amount: z.number().nonnegative(),
  rate: z.number().optional(),
  units: z.number().optional(),
});

const payslipDocumentSchema = z.object({
  title: z.string().optional(),
  fileName: z.string(),
  url: z.string().url(),
  fileType: z.string(),
  fileSize: z.number().int().optional(),
  metadata: z.preprocess(
    (val) => (typeof val === 'string' ? JSON.parse(val) : val),
    z.record(z.string())
  ).optional(),
});

const pushPayslipSchema = z.object({
  tenantSlug: z.string().min(1),
  payslip: z.object({
    employeeNationalId: z.string().min(1)
      .describe("מזהה ייחודי של עובד"),
    employerIdentifier: z.string().min(1)
      .describe("מזהה מעסיק"),
    year: z.number().int().min(2000).max(2100),
    month: z.number().int().min(1).max(12),
    periodStart: z.string().datetime().optional(),
    periodEnd: z.string().datetime().optional(),
    grossPay: z.number().nonnegative(),
    netPay: z.number().nonnegative(),
    taxDeducted: z.number().nonnegative().default(0),
    insuranceDeducted: z.number().nonnegative().default(0),
    currency: z.enum(["ILS", "USD", "EUR", "OTHER"]).default("ILS"),
    items: z.array(payslipItemSchema).default([]),
    documents: z.array(payslipDocumentSchema).optional(),
  }),
});

// Define the baseMASKORET schema according to the mapping document
const avoda = z.object({
  shiyuch: z.string().optional(),
  shiyuch2: z.string().optional(),
  pizul_shiyuch: z.array(z.object({
    shem_shiyuch: z.string(),
    shaot: z.number().optional(),
    yamim: z.number().optional(),
    achuz: z.number().optional(),
  })).optional(),
  pizul_shiyuch2: z.array(z.object({
    shem_shiyuch: z.string(),
    shaot: z.number().optional(),
    yamim: z.number().optional(),
    achuz: z.number().optional(),
  })).optional(),
  baal_shlita: z.boolean().optional(),
  taarich_vetek: z.string().optional(),
  lelo_pikuach: z.boolean().optional(),
  sium_avoda: z.boolean().optional(),
  yom_sium_avoda: z.number().optional(),
  sibat_hafsakat_avoda: z.string().optional(),
});

const misra = z.object({
  sug: z.number(),
  taarif: z.number().optional(),
  taarif_zurat_chishuv: z.number().optional(),
  taarif_yom: z.number().optional(),
  shaot_avoda: z.number().optional(),
  yamim_avoda: z.number().optional(),
  shaot_teken_chodesh: z.number().optional(),
  shaot_teken_yom: z.number().optional(),
  yamim_teken: z.number().optional(),
  yamim_shavua: z.number().optional(),
  hekef_misra: z.number().optional(),
});

const tashlumItem = z.object({
  shem: z.string(),
  kod: z.string(),
  taarif: z.number().optional(),
  kamut: z.number().optional(),
  gilum: z.boolean().optional(),
  kovea_kizva: z.number().optional(),
  teur: z.string().optional(),
  teur_same_line: z.boolean().optional(),
});

const nikuyimItem = z.object({
  shem: z.string(),
  kod: z.string(),
  schum: z.number(),
  gilum: z.boolean().optional(),
});

const shovaItem = z.object({
  shem: z.string(),
  kod: z.string(),
  schum: z.number(),
});

const leaveItem = z.object({
  taarich_hatchala: z.string(),
  taarich_sium: z.string(),
  zakaut: z.number().optional(),
  nizul: z.number().optional(),
  yitra_kodemet: z.number().optional(),
  zakaut_auto: z.boolean().optional(),
  nizul_auto: z.boolean().optional(),
  yitra_kodemet_auto: z.boolean().optional(),
  teur: z.string().optional(),
  zarim_sug: z.number().optional(),
});

const bankItem = z.object({
  bank: z.number(),
  snif: z.number(),
  cheshbon: z.string(),
  achuz: z.number().optional(),
});

const baseMaskoretSchema = z.object({
  tenantSlug: z.string().min(1),
  maskoret: z.object({
    sug: z.literal("MASKORET"),
    mispar_tz: z.string().min(1),
    shem_mishpacha: z.string(),
    shem_praty: z.string(),
    chodesh: z.number().int().min(1).max(12),
    shana: z.number().int().min(2000).max(2100),
    misra,
    avoda,
    chishuv_gilum: z.record(z.any()).optional(),
    tashlumim: z.array(tashlumItem).default([]),
    shovayim: z.array(shovaItem).default([]),
    nikuyim: z.array(nikuyimItem).default([]),
    kupot: z.array(z.any()).optional(),
    chufsha: z.array(leaveItem).default([]),
    machala: z.array(leaveItem).default([]),
    zurat_tashlum: z.number().optional(),
    bank: z.array(bankItem).default([]),
  }),
});

type Tx = Omit<PrismaClient, "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends">;

export const integrationRouter = createTRPCRouter({
  /** Push a fully-prepared Payslip coming from an external client */
  pushPayslip: protectedProcedure
    .input(pushPayslipSchema)
    .mutation(async ({ ctx, input }) => {
      const { tenantSlug, payslip } = input;
      const {
        employeeNationalId,
        employerIdentifier,
        year,
        month,
      } = payslip;

      // Create a log of the integration attempt
      const integrationLog = {
        success: false,
        errors: [] as string[],
        warnings: [] as string[],
        created: {} as Record<string, boolean>,
        steps: {} as Record<string, boolean>,
      };

      /* ---------- Start Transaction ---------- */
      try {
        return await ctx.db.$transaction(async (tx: Tx) => {
          try {
            /* --- TENANT ---------------------------------------------------- */
            integrationLog.steps.tenant = false;
            
            const tenant = await getOrCreate(tx, 'Tenant' as keyof Prisma.TypeMap["model"],
              { name: tenantSlug },
              { 
                name: tenantSlug, 
                plan: 'FREE',
                // Add other required fields here
              }
            );
            integrationLog.steps.tenant = true;

            // Log if we auto-created the tenant
            if (('_created' in tenant)) {
              integrationLog.created.tenant = true;
              await tx.alert.create({
                data: {
                  tenantId: tenant.id,
                  type: "CRITICAL" as AlertType,
                  message: `Auto-created tenant "${tenantSlug}" during integration sync`,
                }
              });
            }

            /* --- EMPLOYER --------------------------------------------------- */
            integrationLog.steps.employer = false;
            
            let employer;
            try {
              employer = await findFirstOrCreate(tx, "Employer" as keyof Prisma.TypeMap["model"],
                { 
                  tenantId: tenant.id, 
                  identifier: employerIdentifier 
                },
                {
                  tenantId: tenant.id,
                  identifier: employerIdentifier,
                  name: `Unknown Employer ${employerIdentifier}`,
                  // Other required fields
                }
              );
              integrationLog.steps.employer = true;
              
              // Log if employer was just created
              if ('_created' in employer) {
                integrationLog.created.employer = true;
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "CRITICAL" as AlertType,
                    message: `Auto-created employer "${employerIdentifier}" while pushing payslip`,
                  }
                });
              }
            } catch (err) {
              integrationLog.errors.push(`Employer error: ${String(err)}`);
              throw new Error(`Failed to process employer: ${String(err)}`);
            }

            /* --- EMPLOYEE --------------------------------------------------- */
            integrationLog.steps.employee = false;
            
            let employee;
            try {
              employee = await findFirstOrCreate(tx, "Employee" as keyof Prisma.TypeMap["model"],
                { 
                  tenantId: tenant.id, 
                  nationalId: employeeNationalId 
                },
                {
                  tenantId: tenant.id,
                  employerId: employer.id,
                  nationalId: employeeNationalId,
                  firstName: "UNKNOWN",
                  lastName: "UNKNOWN",
                  status: "ACTIVE",
                  startDate: new Date(`${year}-${String(month).padStart(2, "0")}-01`),
                  // Other required fields
                }
              );
              integrationLog.steps.employee = true;
              
              // Log if employee was just created
              if ('_created' in employee) {
                integrationLog.created.employee = true;
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "WARNING" as AlertType,
                    message: `Auto-created employee "${employeeNationalId}" while pushing payslip`,
                  }
                });
              }
            } catch (err) {
              integrationLog.errors.push(`Employee error: ${String(err)}`);
              throw new Error(`Failed to process employee: ${String(err)}`);
            }

            /* --- UPSERT OR CREATE PAYSLIP ---------------------------------- */
            integrationLog.steps.payslip = false;
            
            let payslipRecord;
            try {
              payslipRecord = await tx.payslip.upsert({
                where: {
                  employeeId_year_month: {
                    employeeId: employee.id,
                    year,
                    month,
                  },
                },
                update: {
                  grossPay: payslip.grossPay,
                  netPay: payslip.netPay,
                  taxDeducted: payslip.taxDeducted,
                  insuranceDeducted: payslip.insuranceDeducted,
                  periodStart: payslip.periodStart,
                  periodEnd: payslip.periodEnd,
                  // Update any other fields
                },
                create: {
                  tenantId: tenant.id,
                  employeeId: employee.id,
                  year,
                  month,
                  periodStart: payslip.periodStart,
                  periodEnd: payslip.periodEnd,
                  grossPay: payslip.grossPay,
                  netPay: payslip.netPay,
                  taxDeducted: payslip.taxDeducted,
                  insuranceDeducted: payslip.insuranceDeducted,
                  // Any other required fields
                },
              });
              integrationLog.steps.payslip = true;
            } catch (err) {
              integrationLog.errors.push(`Payslip creation error: ${String(err)}`);
              throw new Error(`Failed to create payslip: ${String(err)}`);
            }

            /* --- PROCESS PayslipItems -------------------------------------- */
            integrationLog.steps.payslipItems = false;
            
            try {
              // First delete existing items
              await tx.payslipItem.deleteMany({
                where: { payslipId: payslipRecord.id },
              });

              // Then create new items if they exist
              if (payslip.items && payslip.items.length > 0) {
                await tx.payslipItem.createMany({
                  data: payslip.items.map((it) => {
                    // Convert string kod to enum format (K_1000, etc.)
                    // The actual string is saved as "1000", "1001", etc.
                    // But we need to format it as K_1000, K_1001 for the enum
                    const kodStr = it.kod ? String(it.kod) : null;
                    
                    // Only use valid codes that exist in our PayslipItemKodValues array
                    const isValidKod = kodStr && 
                      (PayslipItemKodValues as readonly string[]).includes(kodStr);
                    
                    return {
                      payslipId: payslipRecord.id,
                      description: it.description,
                      amount: it.amount,
                      type: it.type,
                      // If valid kod, format as K_XXX enum, otherwise null
                      kod: isValidKod ? `K_${kodStr}` as any : null,
                      rate: it.rate,
                      units: it.units,
                    };
                  }),
                });
              }
              integrationLog.steps.payslipItems = true;
            } catch (err) {
              integrationLog.errors.push(`PayslipItems error: ${String(err)}`);
              integrationLog.warnings.push("Failed to process payslip items, but continuing with integration");
              // Don't throw - continue with the process
              
              // Create an alert about this failure
              await tx.alert.create({
                data: {
                  tenantId: tenant.id,
                  type: "WARNING" as AlertType,
                  message: `Failed to process payslip items for ${year}-${month}`,
                  context: { 
                    error: String(err), 
                    payslipId: payslipRecord.id,
                    employeeId: employee.id
                  }
                }
              });
            }

            /* --- Attach Documents ------------------------------------------ */
            integrationLog.steps.documents = false;
            
            if (payslip.documents?.length) {
              try {
                // Create the documents first
                const createdDocuments = await Promise.all(
                  payslip.documents.map(async (doc) => {
                    try {
                      // First create the document
                      const document = await tx.document.create({
                        data: {
                          tenantId: tenant.id,
                          title: doc.title || `Payslip ${year}-${month}`,
                          fileName: doc.fileName,
                          fileType: doc.fileType,
                          fileSize: doc.fileSize ?? 0,
                          url: doc.url,
                          metadata: doc.metadata || {},
                          mimeType: doc.fileType, // Required field
                          uploadedBy: ctx.session.user.id, // Required field
                          // Don't include payslips relation here
                        }
                      });
                      
                      // Connect document to payslip using Prisma's relation API
                      await tx.payslip.update({
                        where: { id: payslipRecord.id },
                        data : { documentRelations: { create: { documentId: document.id } } }
                      });
                      
                      return document;
                    } catch (err) {
                      ctx.logger?.error(
                        { err, userId: ctx.session.user.id, fileName: doc.fileName },
                        `Failed to create document ${doc.fileName}`
                      );
                      integrationLog.warnings.push(`Failed to attach document ${doc.fileName}: ${String(err)}`);
                      
                      // Log error but don't fail the whole process
                      await tx.alert.create({
                        data: {
                          tenantId: tenant.id,
                          type: "WARNING" as AlertType,
                          message: `Failed to attach document ${doc.fileName} to payslip`,
                          context: { error: String(err), payslipId: payslipRecord.id }
                        }
                      });
                      return null;
                    }
                  })
                );

                // Log successful document attachments
                const successCount = createdDocuments.filter(Boolean).length;
                if (successCount > 0) {
                  await tx.auditLog.create({
                    data: {
                      tenantId: tenant.id,
                      modelName: "Document",
                      action: "CREATE",
                      newValues: { count: successCount, payslipId: payslipRecord.id },
                      userId: ctx.session.user.id,
                      userEmail: ctx.session.user.email || "unknown",
                      ipAddress: "api-integration",
                    }
                  });
                }
                integrationLog.steps.documents = true;
              } catch (err) {
                ctx.logger?.error(
                  { err, userId: ctx.session.user.id, tenant: tenant.id },
                  "Failed to process documents"
                );
                integrationLog.warnings.push("Failed to process documents, but continuing with integration");
                
                // Create alert about document processing failure
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "WARNING" as AlertType,
                    message: `Failed to process documents for payslip`,
                    context: { error: String(err), payslipId: payslipRecord.id }
                  }
                });
                // Continue processing - don't fail the whole integration
              }
            } else {
              // If no documents provided, generate a default PDF
              try {
                // Create a placeholder entry for a PDF that will be generated
                const placeholderDoc = await tx.document.create({
                  data: {
                    tenantId: tenant.id,
                    title: `Payslip ${year}-${month}`,
                    fileName: `payslip_${year}_${month}.pdf`,
                    fileType: "application/pdf",
                    fileSize: 0, // Will be updated when actually generated
                    url: "", // Will be updated when actually generated
                    metadata: { status: "pending_generation", generatedAt: new Date().toISOString() },
                    mimeType: "application/pdf", // Required field
                    uploadedBy: ctx.session.user.id, // Required field
                    // Don't include payslips relation here
                  }
                });
                
                // Connect document to payslip using Prisma's relation API
                await tx.payslip.update({
                  where: { id: payslipRecord.id },
                  data : { documentRelations: { create: { documentId: placeholderDoc.id } } }
                });

                // Create a task or alert for PDF generation
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "INFO" as AlertType,
                    message: `PDF generation needed for payslip ${year}-${month}`,
                    context: { payslipId: payslipRecord.id, employeeId: employee.id }
                  }
                });
                integrationLog.steps.documents = true;
              } catch (err) {
                ctx.logger?.error(
                  { err, userId: ctx.session.user.id, tenant: tenant.id },
                  "Failed to create placeholder for PDF generation"
                );
                integrationLog.warnings.push("Failed to setup PDF generation, but continuing with integration");
                
                // Log but don't fail the process
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "WARNING" as AlertType,
                    message: `Failed to setup PDF generation for payslip`,
                    context: { error: String(err), payslipId: payslipRecord.id }
                  }
                });
              }
            }

            /* --- REPORT & AUDIT -------------------------------------------- */
            try {
              await tx.report.create({
                data: {
                  tenantId: tenant.id,
                  employerId: employer.id,
                  type: "PAYROLL_SUMMARY",
                  year,
                  month,
                  parameters: { payslipId: payslipRecord.id },
                }
              });
            } catch (err) {
              integrationLog.warnings.push(`Failed to create report: ${String(err)}`);
              // Don't stop the process for this
            }

            try {
              // Stringify the complex object to avoid UUID parsing issues
              const payslipJson = JSON.stringify({
                employeeNationalId: payslip.employeeNationalId,
                employerIdentifier: payslip.employerIdentifier,
                year: payslip.year,
                month: payslip.month,
                // Only include primitive values
                grossPay: payslip.grossPay,
                netPay: payslip.netPay,
                // etc.
              });
              
              await tx.auditLog.create({
                data: {
                  tenantId: tenant.id,
                  modelName: "Payslip",
                  recordId: payslipRecord.id,
                  action: "CREATE",
                  // Avoid complex objects in newValues to prevent UUID parsing issues
                  newValues: { 
                    timestamp: new Date().toISOString(),
                    success: integrationLog.success,
                    warnings: integrationLog.warnings,
                    errors: integrationLog.errors
                  },
                  userId: ctx.session.user.id,
                  userEmail: ctx.session.user.email || "unknown",
                  ipAddress: "api-integration",
                }
              });
            } catch (err) {
              integrationLog.warnings.push(`Failed to create audit log: ${String(err)}`);
              // Don't stop the process for this
            }

            integrationLog.success = true;
            
            return { 
              success: true, 
              payslipId: payslipRecord.id,
              created: {
                employer: !!integrationLog.created.employer,
                employee: !!integrationLog.created.employee,
              },
              integrationLog
            };
          } catch (err) {
            // Handle any errors within the transaction
            ctx.logger?.error(
              { err, userId: ctx.session.user.id, tenantSlug },
              "Transaction error"
            );
            
            // Try to log the error in the database, if possible
            try {
              // Find tenant ID if possible
              const errorTenant = await tx.tenant.findFirst({ 
                where: { name: tenantSlug } 
              });
              
              // Only create audit log if we have a tenant
              if (errorTenant) {
                await tx.auditLog.create({
                  data: {
                    tenantId: errorTenant.id,
                    modelName: "IntegrationError",
                    action: "CREATE",
                    newValues: {
                      error: String(err),
                      payslip: payslip,
                      integrationLog
                    },
                    userId: ctx.session.user.id,
                    userEmail: ctx.session.user.email || "unknown",
                    ipAddress: "api-integration",
                  }
                });
              } else {
                // If no tenant, log to console
                ctx.logger?.error(
                  {
                    err,
                    tenantSlug,
                    integrationLog,
                    userId: ctx.session.user.id,
                  },
                  "Cannot log integration error to database - tenant not found"
                );
              }
            } catch (logErr) {
              ctx.logger?.error({ err: logErr, tenantSlug }, "Failed to log error");
            }
            
            throw err;
          }
        });
      } catch (err) {
        // Handle any errors outside the transaction
        ctx.logger?.error(
          { err, userId: ctx.session.user.id, tenantSlug },
          "Integration error"
        );
        
        // Throw a proper TRPC error
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Integration failed: ${String(err)}`,
          cause: {
            integrationLog,
            error: err
          }
        });
      }
    }),

  /** Push baseMASKORET data for payroll integration */
  pushBaseMaskoret: protectedProcedure
    .input(baseMaskoretSchema)
    .mutation(async ({ ctx, input }) => {
      const { tenantSlug, maskoret } = input;
      const {
        mispar_tz: nationalId,
        chodesh: month,
        shana: year,
        misra,
        avoda,
      } = maskoret;

      // Create a log of the integration attempt
      const integrationLog = {
        success: false,
        errors: [] as string[],
        warnings: [] as string[],
        created: {} as Record<string, boolean>,
        steps: {} as Record<string, boolean>,
      };

      /* ---------- Start Transaction ---------- */
      try {
        return await ctx.db.$transaction(async (tx: Tx) => {
          try {
            /* --- TENANT ---------------------------------------------------- */
            integrationLog.steps.tenant = false;
            
            const tenant = await getOrCreate(tx, 'Tenant' as keyof Prisma.TypeMap["model"],
              { name: tenantSlug },
              { 
                name: tenantSlug, 
                plan: 'FREE',
              }
            );
            integrationLog.steps.tenant = true;

            // Log if we auto-created the tenant
            if (('_created' in tenant)) {
              integrationLog.created.tenant = true;
              await tx.alert.create({
                data: {
                  tenantId: tenant.id,
                  type: "CRITICAL" as AlertType,
                  message: `Auto-created tenant "${tenantSlug}" during baseMASKORET integration`,
                }
              });
            }

            /* --- EMPLOYER --------------------------------------------------- */
            integrationLog.steps.employer = false;
            
            // We'll use a default employer identifier for baseMASKORET 
            // (this could be modified based on specific business requirements)
            const employerIdentifier = "BMSKT-DEFAULT";
            let employer;
            try {
              employer = await findFirstOrCreate(tx, "Employer" as keyof Prisma.TypeMap["model"],
                { 
                  tenantId: tenant.id, 
                  identifier: employerIdentifier 
                },
                {
                  tenantId: tenant.id,
                  identifier: employerIdentifier,
                  name: `Default Employer for baseMASKORET`,
                }
              );
              integrationLog.steps.employer = true;
              
              // Log if employer was just created
              if ('_created' in employer) {
                integrationLog.created.employer = true;
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "CRITICAL" as AlertType,
                    message: `Auto-created employer "${employerIdentifier}" for baseMASKORET integration`,
                  }
                });
              }
            } catch (err) {
              integrationLog.errors.push(`Employer error: ${String(err)}`);
              throw new Error(`Failed to process employer: ${String(err)}`);
            }

            /* --- EMPLOYEE --------------------------------------------------- */
            integrationLog.steps.employee = false;
            
            let employee;
            try {
              employee = await findFirstOrCreate(tx, "Employee" as keyof Prisma.TypeMap["model"],
                { 
                  tenantId: tenant.id, 
                  nationalId
                },
                {
                  tenantId: tenant.id,
                  employerId: employer.id,
                  nationalId,
                  firstName: maskoret.shem_praty,
                  lastName: maskoret.shem_mishpacha,
                  status: avoda.sium_avoda ? "TERMINATED" : "ACTIVE" as EmployeeStatus,
                  startDate: avoda.taarich_vetek 
                    ? new Date(avoda.taarich_vetek) 
                    : new Date(`${year}-${String(month).padStart(2, "0")}-01`),
                  // End date if terminated
                  ...(avoda.sium_avoda && avoda.yom_sium_avoda 
                    ? { endDate: new Date(`${year}-${String(month).padStart(2, "0")}-${String(avoda.yom_sium_avoda).padStart(2, "0")}`) } 
                    : {}),
                }
              );
              integrationLog.steps.employee = true;
              
              // Log if employee was just created
              if ('_created' in employee) {
                integrationLog.created.employee = true;
                await tx.alert.create({
                  data: {
                    tenantId: tenant.id,
                    type: "WARNING" as AlertType,
                    message: `Auto-created employee "${nationalId}" for baseMASKORET integration`,
                  }
                });
              }
            } catch (err) {
              integrationLog.errors.push(`Employee error: ${String(err)}`);
              throw new Error(`Failed to process employee: ${String(err)}`);
            }

            /* --- CALCULATE TOTALS ------------------------------------------ */
            // Calculate gross and net pay from tashlumim and nikuyim
            const grossPay = maskoret.tashlumim.reduce((sum, item) => sum + (Number(item.taarif || 0) * Number(item.kamut || 1)), 0);
            const totalDeductions = maskoret.nikuyim.reduce((sum, item) => sum + Number(item.schum || 0), 0);
            const netPay = grossPay - totalDeductions;

            // Split tax and insurance deductions (assuming standard codes, adjust as needed)
            const taxDeducted = maskoret.nikuyim
              .filter(item => item.kod.startsWith("40") && !item.kod.includes("401")) // Example for tax codes
              .reduce((sum, item) => sum + Number(item.schum || 0), 0);

            const insuranceDeducted = maskoret.nikuyim
              .filter(item => item.kod.includes("401")) // Example for insurance codes
              .reduce((sum, item) => sum + Number(item.schum || 0), 0);

            /* --- UPSERT OR CREATE PAYSLIP ---------------------------------- */
            integrationLog.steps.payslip = false;
            
            let payslipRecord;
            try {
              const periodStart = new Date(`${year}-${String(month).padStart(2, "0")}-01`);
              const periodEnd = new Date(new Date(periodStart).setMonth(periodStart.getMonth() + 1) - 1);

              payslipRecord = await tx.payslip.upsert({
                where: {
                  employeeId_year_month: {
                    employeeId: employee.id,
                    year,
                    month,
                  },
                },
                update: {
                  grossPay,
                  netPay,
                  taxDeducted,
                  insuranceDeducted,
                  periodStart,
                  periodEnd,
                },
                create: {
                  tenantId: tenant.id,
                  employeeId: employee.id,
                  year,
                  month,
                  periodStart,
                  periodEnd,
                  grossPay,
                  netPay,
                  taxDeducted,
                  insuranceDeducted,
                },
              });
              integrationLog.steps.payslip = true;
            } catch (err) {
              integrationLog.errors.push(`Payslip creation error: ${String(err)}`);
              throw new Error(`Failed to create payslip: ${String(err)}`);
            }

            /* --- PROCESS PayslipItems -------------------------------------- */
            integrationLog.steps.payslipItems = false;
            
            try {
              // First delete existing items
              await tx.payslipItem.deleteMany({
                where: { payslipId: payslipRecord.id },
              });

              // Map earnings (tashlumim) to PayslipItems
              if (maskoret.tashlumim && maskoret.tashlumim.length > 0) {
                await tx.payslipItem.createMany({
                  data: maskoret.tashlumim.map((item) => {
                    const amount = Number(item.taarif || 0) * Number(item.kamut || 1);
                    const kodStr = item.kod ? String(item.kod) : null;
                    
                    // Only use valid codes that exist in our PayslipItemKodValues array
                    const isValidKod = kodStr && 
                      (PayslipItemKodValues as readonly string[]).includes(kodStr);
                    
                    return {
                      payslipId: payslipRecord.id,
                      description: item.shem,
                      amount,
                      type: "EARNING" as PayslipItemType,
                      kod: isValidKod ? `K_${kodStr}` as any : null,
                      rate: item.taarif,
                      units: item.kamut,
                    };
                  }),
                });
              }
              
              // Map deductions (nikuyim) to PayslipItems
              if (maskoret.nikuyim && maskoret.nikuyim.length > 0) {
                await tx.payslipItem.createMany({
                  data: maskoret.nikuyim.map((item) => {
                    return {
                      payslipId: payslipRecord.id,
                      description: item.shem,
                      amount: item.schum,
                      type: "DEDUCTION" as PayslipItemType,
                      // Deduction codes aren't in the PayslipItemKod enum, so we don't set it
                    };
                  }),
                });
              }
              
              // Map benefits (shovayim) to PayslipItems if any exist
              if (maskoret.shovayim && maskoret.shovayim.length > 0) {
                await tx.payslipItem.createMany({
                  data: maskoret.shovayim.map((item) => {
                    return {
                      payslipId: payslipRecord.id,
                      description: item.shem,
                      amount: item.schum,
                      type: "EMPLOYER_CONTRIB" as PayslipItemType,
                      // Benefit codes may not be in the PayslipItemKod enum
                    };
                  }),
                });
              }
              
              integrationLog.steps.payslipItems = true;
            } catch (err) {
              integrationLog.errors.push(`PayslipItems error: ${String(err)}`);
              integrationLog.warnings.push("Failed to process payslip items, but continuing with integration");
              
              // Create an alert about this failure
              await tx.alert.create({
                data: {
                  tenantId: tenant.id,
                  type: "WARNING" as AlertType,
                  message: `Failed to process payslip items for ${year}-${month}`,
                  context: { 
                    error: String(err), 
                    payslipId: payslipRecord.id,
                    employeeId: employee.id
                  }
                }
              });
            }

            /* --- Process Leave Records if needed --------------------------- */
            // This would be implemented based on business requirements for chufsha/machala data

            /* --- REPORT & AUDIT -------------------------------------------- */
            try {
              await tx.report.create({
                data: {
                  tenantId: tenant.id,
                  employerId: employer.id,
                  type: "PAYROLL_SUMMARY",
                  year,
                  month,
                  parameters: { payslipId: payslipRecord.id },
                }
              });
            } catch (err) {
              integrationLog.warnings.push(`Failed to create report: ${String(err)}`);
              // Don't stop the process for this
            }

            try {
              await tx.auditLog.create({
                data: {
                  tenantId: tenant.id,
                  modelName: "Payslip",
                  recordId: payslipRecord.id,
                  action: "CREATE",
                  newValues: { 
                    timestamp: new Date().toISOString(),
                    source: "baseMASKORET",
                    success: true,
                    warnings: integrationLog.warnings,
                    errors: integrationLog.errors
                  },
                  userId: ctx.session.user.id,
                  userEmail: ctx.session.user.email || "unknown",
                  ipAddress: "api-baseMASKORET-integration",
                }
              });
            } catch (err) {
              integrationLog.warnings.push(`Failed to create audit log: ${String(err)}`);
              // Don't stop the process for this
            }

            integrationLog.success = true;
            
            return { 
              success: true, 
              payslipId: payslipRecord.id,
              created: {
                employer: !!integrationLog.created.employer,
                employee: !!integrationLog.created.employee,
              },
              integrationLog
            };
          } catch (err) {
            // Handle any errors within the transaction
            ctx.logger?.error(
              { err, userId: ctx.session.user.id, tenantSlug },
              "Transaction error"
            );
            
            // Try to log the error in the database, if possible
            try {
              // Find tenant ID if possible
              const errorTenant = await tx.tenant.findFirst({ 
                where: { name: tenantSlug } 
              });
              
              // Only create audit log if we have a tenant
              if (errorTenant) {
                await tx.auditLog.create({
                  data: {
                    tenantId: errorTenant.id,
                    modelName: "IntegrationError",
                    action: "CREATE",
                    newValues: {
                      error: String(err),
                      source: "baseMASKORET",
                      maskoret: JSON.stringify({
                        mispar_tz: maskoret.mispar_tz,
                        year: maskoret.shana,
                        month: maskoret.chodesh
                      }),
                      integrationLog
                    },
                    userId: ctx.session.user.id,
                    userEmail: ctx.session.user.email || "unknown",
                    ipAddress: "api-baseMASKORET-integration",
                  }
                });
              } else {
                // If no tenant, log to console
                ctx.logger?.error(
                  {
                    err,
                    tenantSlug,
                    integrationLog,
                    userId: ctx.session.user.id,
                  },
                  "Cannot log integration error to database - tenant not found"
                );
              }
            } catch (logErr) {
              ctx.logger?.error({ err: logErr, tenantSlug }, "Failed to log error");
            }
            
            throw err;
          }
        });
      } catch (err) {
        // Handle any errors outside the transaction
        ctx.logger?.error(
          { err, userId: ctx.session.user.id, tenantSlug },
          "Integration error"
        );
        
        // Throw a proper TRPC error
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `baseMASKORET integration failed: ${String(err)}`,
          cause: {
            integrationLog,
            error: err
          }
        });
      }
    }),

  /** Fetch delta since last sync – useful for legacy system */
  delta: protectedProcedure
    .input(
      z.object({
        since: z.coerce.date(),
        tenantSlug: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Check if user has an associated tenant or is an admin
      // Retrieve the user's tenant information from the database
      const userWithTenant = ctx.session.user.id ? await ctx.db.user.findUnique({
        where: { id: ctx.session.user.id },
        select: { tenantId: true }
      }) : null;
      
      const userTenantId = userWithTenant?.tenantId;
      const isAdmin = ctx.session.user.role === "ADMIN" || ctx.session.user.role === "OWNER";
      
      // If tenantSlug is provided, limit results to that tenant
      let targetTenantId = null;
      if (input.tenantSlug) {
        const tenant = await ctx.db.tenant.findUnique({
          where: { name: input.tenantSlug },
        });
        
        if (!tenant) {
          throw new TRPCError({
            code: "NOT_FOUND", 
            message: `Tenant "${input.tenantSlug}" not found`
          });
        }
        
        // Only allow access if user belongs to this tenant or is admin
        if (!isAdmin && tenant.id !== userTenantId) {
          throw new TRPCError({
            code: "FORBIDDEN",
            message: "You don't have permission to access audit logs for this tenant"
          });
        }
        
        targetTenantId = tenant.id;
      } else {
        // No tenantSlug provided
        if (!isAdmin) {
          // Non-admins can only see their own tenant's logs
          if (!userTenantId) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "You must specify a tenant or be an admin to access audit logs"
            });
          }
          targetTenantId = userTenantId;
        }
        // Admins with no tenantSlug can see all logs (targetTenantId remains null)
      }
      
      return ctx.db.auditLog.findMany({
        where: { 
          timestamp: { gt: input.since },
          // Only filter by tenantId if it's set (for admins who can see all)
          ...(targetTenantId ? { tenantId: targetTenantId } : {})
        },
        orderBy: { timestamp: "asc" },
        include: {
          // Include related models if needed
          // tenant: true,
        }
      });
    }),
});