"use client";

import { But<PERSON> } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";
import { FileSignature, FileText, UserPlus, Users } from "lucide-react";

export type EmployerQuickAccessProps = {
	isLoading?: boolean;
};

export function EmployerQuickAccess({
	isLoading = false,
}: EmployerQuickAccessProps) {
	if (isLoading) {
		return <EmployerQuickAccessSkeleton />;
	}

	return (
		<div className="mt-6">
			<h2 className="mb-4 font-semibold text-xl">פעולות מהירות</h2>
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				<Button className="flex h-24 flex-col items-center justify-center gap-2">
					<UserPlus className="h-6 w-6" />
					<span>הוספת עובד</span>
				</Button>
				<Button
					className="flex h-24 flex-col items-center justify-center gap-2"
					variant="outline"
				>
					<Users className="h-6 w-6" />
					<span>צפייה/עריכת עובדים</span>
				</Button>
				<Button
					className="flex h-24 flex-col items-center justify-center gap-2"
					variant="outline"
				>
					<FileText className="h-6 w-6" />
					<span>הפקת תלושי שכר</span>
				</Button>
				<Button
					className="flex h-24 flex-col items-center justify-center gap-2"
					variant="outline"
				>
					<FileSignature className="h-6 w-6" />
					<span>ניהול טופס 101</span>
				</Button>
			</div>
		</div>
	);
}

function EmployerQuickAccessSkeleton() {
	return (
		<div className="mt-6">
			<Skeleton className="mb-4 h-7 w-40" />
			<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
				{Array.from({ length: 4 }).map((_, i) => (
					<Skeleton key={i} className="h-24 rounded-md" />
				))}
			</div>
		</div>
	);
}
