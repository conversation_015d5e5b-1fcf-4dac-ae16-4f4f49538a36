"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/rtl-components";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { UserPlus, Loader2 } from "lucide-react";
import { toast } from "sonner";

// Schema for employee form validation
const employeeFormSchema = z.object({
  fullName: z.string().min(2, "שם מלא חייב להכיל לפחות 2 תווים"),
  nationalId: z.string().min(9, "תעודת זהות חייבת להכיל 9 ספרות").max(9, "תעודת זהות חייבת להכיל 9 ספרות"),
  email: z.string().email("כתובת אימייל לא תקינה").optional().or(z.literal("")),
  phone: z.string().min(10, "מספר טלפון חייב להכיל לפחות 10 ספרות").optional().or(z.literal("")),
  address: z.string().optional(),
  startDate: z.string().min(1, "תאריך התחלה נדרש"),
  baseSalary: z.string().min(1, "שכר בסיס נדרש"),
  position: z.string().min(2, "תפקיד נדרש"),
  department: z.string().optional(),
  bankAccount: z.string().optional(),
  bankBranch: z.string().optional(),
  bankName: z.string().optional(),
  notes: z.string().optional(),
});

type EmployeeFormValues = z.infer<typeof employeeFormSchema>;

interface AddEmployeeModalProps {
  onAddEmployee: (data: EmployeeFormValues) => Promise<void>;
  isLoading?: boolean;
  trigger?: React.ReactNode;
}

export function AddEmployeeModal({ 
  onAddEmployee, 
  isLoading = false,
  trigger 
}: AddEmployeeModalProps) {
  const [open, setOpen] = useState(false);
  
  const form = useForm<EmployeeFormValues>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: {
      fullName: "",
      nationalId: "",
      email: "",
      phone: "",
      address: "",
      startDate: "",
      baseSalary: "",
      position: "",
      department: "",
      bankAccount: "",
      bankBranch: "",
      bankName: "",
      notes: "",
    },
  });

  const onSubmit = async (data: EmployeeFormValues) => {
    try {
      await onAddEmployee(data);
      toast.success(`עובד נוסף בהצלחה - ${data.fullName} נוסף למערכת`);
      form.reset();
      setOpen(false);
    } catch (error) {
      toast.error("אירעה שגיאה בעת הוספת העובד. נסה שוב.");
    }
  };

  const defaultTrigger = (
    <Button>
      <UserPlus className="h-4 w-4 ml-2" />
      הוסף עובד
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>הוספת עובד חדש</DialogTitle>
          <DialogDescription>
            מלא את פרטי העובד החדש. שדות המסומנים בכוכבית הם חובה.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">פרטים אישיים</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>שם מלא *</FormLabel>
                      <FormControl>
                        <Input placeholder="שם פרטי ומשפחה" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="nationalId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>תעודת זהות *</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="123456789" 
                          {...field}
                          maxLength={9}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>אימייל</FormLabel>
                      <FormControl>
                        <Input 
                          type="email" 
                          placeholder="<EMAIL>" 
                          {...field} 
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>טלפון</FormLabel>
                      <FormControl>
                        <Input placeholder="050-1234567" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>כתובת</FormLabel>
                    <FormControl>
                      <Input placeholder="רחוב, מספר בית, עיר" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Employment Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">פרטי העסקה</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="startDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>תאריך התחלה *</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="baseSalary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>שכר בסיס *</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          placeholder="10000" 
                          {...field}
                          min="0"
                          step="100"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="position"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>תפקיד *</FormLabel>
                      <FormControl>
                        <Input placeholder="מפתח תוכנה" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="department"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>מחלקה</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="בחר מחלקה" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="development">פיתוח</SelectItem>
                          <SelectItem value="marketing">שיווק</SelectItem>
                          <SelectItem value="sales">מכירות</SelectItem>
                          <SelectItem value="hr">משאבי אנוש</SelectItem>
                          <SelectItem value="finance">כספים</SelectItem>
                          <SelectItem value="operations">תפעול</SelectItem>
                          <SelectItem value="support">תמיכה</SelectItem>
                          <SelectItem value="management">הנהלה</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Bank Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">פרטי בנק</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="bankName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>שם הבנק</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="בחר בנק" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="hapoalim">בנק הפועלים</SelectItem>
                          <SelectItem value="leumi">בנק לאומי</SelectItem>
                          <SelectItem value="discount">בנק דיסקונט</SelectItem>
                          <SelectItem value="mizrahi">בנק מזרחי טפחות</SelectItem>
                          <SelectItem value="igud">בנק איגוד</SelectItem>
                          <SelectItem value="yahav">בנק יהב</SelectItem>
                          <SelectItem value="otsar">בנק אוצר החייל</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="bankBranch"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>מספר סניף</FormLabel>
                      <FormControl>
                        <Input placeholder="123" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="bankAccount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>מספר חשבון</FormLabel>
                      <FormControl>
                        <Input placeholder="1234567" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Notes Section */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>הערות</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="הערות נוספות על העובד..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex justify-end gap-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isLoading}
              >
                ביטול
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 ml-2 animate-spin" />
                    מוסיף עובד...
                  </>
                ) : (
                  <>
                    <UserPlus className="h-4 w-4 ml-2" />
                    הוסף עובד
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 