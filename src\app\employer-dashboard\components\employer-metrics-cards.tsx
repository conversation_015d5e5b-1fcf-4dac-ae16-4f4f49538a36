"use client";

import React, { useState, useEffect } from "react";
import { Users, Receipt, BarChart3, ArrowUp, ArrowDown } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

export type EmployerMetrics = {
  totalEmployees: number;
  payslipsGenerated: number;
  totalSalaryValue: string;
  metrics: {
    employees: { value: number; change: number; changeType: string };
    payslips: { value: number; change: number; changeType: string };
    salaryValue: { value: string; change: number; changeType: string };
  };
};

export type EmployerMetricsCardsProps = {
  metrics?: EmployerMetrics;
  isLoading: boolean;
};

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 80 } },
};

export function EmployerMetricsCards({ metrics, isLoading }: EmployerMetricsCardsProps) {
  const [counters, setCounters] = useState({
    employees: 0,
    payslips: 0,
    salary: "",
  });

  useEffect(() => {
    if (!metrics) return;

    const duration = 1500;
    let animationFrameId: number;
    const salaryNumericValue = Number(metrics.totalSalaryValue.replace(/[^0-9.]/g, ""));
    const start = performance.now();
    
    const tick = (now: number) => {
      const progress = Math.min((now - start) / duration, 1);

      setCounters({
        employees: Math.floor(progress * metrics.totalEmployees),
        payslips: Math.floor(progress * metrics.payslipsGenerated),
        salary: new Intl.NumberFormat("he-IL", { style: "currency", currency: "ILS" }).format(
          progress * salaryNumericValue,
        ),
      });

      if (progress < 1) {
        animationFrameId = requestAnimationFrame(tick);
      } else {
        setCounters({
          employees: metrics.totalEmployees,
          payslips: metrics.payslipsGenerated,
          salary: metrics.totalSalaryValue,
        });
      }
    };

    animationFrameId = requestAnimationFrame(tick);

    return () => cancelAnimationFrame(animationFrameId);
  }, [metrics]);

  if (isLoading) {
    return <EmployerMetricsCardsSkeleton />;
  }

  if (!metrics) {
    return <div>אין נתונים זמינים</div>;
  }

  return (
    <motion.div
      className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div
        variants={item}
        whileHover={{ y: -5 }}
      >
        <Card className="group transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30 hover:shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">סך כל העובדים</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground transition-all duration-300 group-hover:scale-125 group-hover:text-primary" />
          </CardHeader>
          <CardContent>
            <motion.div
              className="text-2xl font-bold transition-colors duration-300 group-hover:text-primary"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 100 }}
            >
              {counters.employees}
            </motion.div>
            <p className="text-xs text-muted-foreground">
              <span
                className={`${metrics.metrics.employees.changeType === "increase" ? "text-green-500" : "text-red-500"} inline-flex items-center font-bold transition-all duration-300 group-hover:font-bold`}
              >
                {metrics.metrics.employees.changeType === "increase" ? (
                  <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
                ) : (
                  <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
                )}
                {metrics.metrics.employees.change > 0 ? "+" : ""}
                {metrics.metrics.employees.change}
              </span>{" "}
              <span className="transition-opacity duration-300 group-hover:opacity-75">מאז החודש שעבר</span>
            </p>
            <div className="mt-3 h-0.5 w-0 rounded-full bg-primary opacity-0 transition-all duration-500 group-hover:w-full group-hover:opacity-100" />
          </CardContent>
        </Card>
      </motion.div>

      <motion.div
        variants={item}
        whileHover={{ y: -5 }}
      >
        <Card className="group transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30 hover:shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">תלושי שכר שהופקו</CardTitle>
            <Receipt className="h-4 w-4 text-muted-foreground transition-all duration-300 group-hover:scale-125 group-hover:text-primary" />
          </CardHeader>
          <CardContent>
            <motion.div
              className="text-2xl font-bold transition-colors duration-300 group-hover:text-primary"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 100 }}
            >
              {counters.payslips}
            </motion.div>
            <p className="text-xs text-muted-foreground">
              <span
                className={`${metrics.metrics.payslips.changeType === "increase" ? "text-green-500" : "text-muted-foreground"} inline-flex items-center font-bold transition-all duration-300 group-hover:font-bold`}
              >
                {metrics.metrics.payslips.changeType === "increase" ? (
                  <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
                ) : (
                  <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
                )}
                {metrics.metrics.payslips.change > 0 ? "+" : ""}
                {metrics.metrics.payslips.change}%
              </span>{" "}
              <span className="transition-opacity duration-300 group-hover:opacity-75">מהתקופה הקודמת</span>
            </p>
            <div className="mt-3 h-0.5 w-0 rounded-full bg-primary opacity-0 transition-all duration-500 group-hover:w-full group-hover:opacity-100" />
          </CardContent>
        </Card>
      </motion.div>

      <motion.div
        variants={item}
        whileHover={{ y: -5 }}
      >
        <Card className="group transition-all duration-300 hover:scale-[1.02] hover:bg-gradient-to-b hover:from-background hover:to-muted/30 hover:shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ערך שכר כולל</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground transition-all duration-300 group-hover:scale-125 group-hover:text-primary" />
          </CardHeader>
          <CardContent>
            <motion.div
              className="text-2xl font-bold transition-colors duration-300 group-hover:text-primary"
              initial={{ scale: 0.8 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 100 }}
            >
              {counters.salary}
            </motion.div>
            <p className="text-xs text-muted-foreground">
              <span
                className={`${metrics.metrics.salaryValue.changeType === "increase" ? "text-green-500" : "text-red-500"} inline-flex items-center font-bold transition-all duration-300 group-hover:font-bold`}
              >
                {metrics.metrics.salaryValue.changeType === "increase" ? (
                  <ArrowUp className="ml-1 h-3 w-3 group-hover:animate-bounce" />
                ) : (
                  <ArrowDown className="ml-1 h-3 w-3 group-hover:animate-bounce" />
                )}
                {metrics.metrics.salaryValue.change > 0 ? "+" : ""}
                {metrics.metrics.salaryValue.change}%
              </span>{" "}
              <span className="transition-opacity duration-300 group-hover:opacity-75">מהתקופה הקודמת</span>
            </p>
            <div className="mt-3 h-0.5 w-0 rounded-full bg-primary opacity-0 transition-all duration-500 group-hover:w-full group-hover:opacity-100" />
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}

function EmployerMetricsCardsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 3 }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
        >
          <Card className="group transition-all duration-300 hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24 transition-all duration-500 group-hover:w-28" />
              <Skeleton className="h-4 w-4 rounded-full transition-all duration-300 group-hover:scale-110" />
            </CardHeader>
            <CardContent>
              <Skeleton className="mb-2 h-8 w-16 transition-all duration-500 group-hover:w-20" />
              <Skeleton className="h-3 w-32 transition-all duration-500 group-hover:w-36" />
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}
