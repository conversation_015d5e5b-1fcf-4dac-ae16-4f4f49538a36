# Salary Management System

A comprehensive salary management system built with the T3 Stack, designed for Israeli businesses to manage employee salaries, attendance, and payroll calculations.

## 🚀 Tech Stack

- **Framework**: [Next.js 15.3.2](https://nextjs.org/) with App Router
- **Language**: [TypeScript 5.8.3](https://www.typescriptlang.org/)
- **Database**: [PostgreSQL](https://www.postgresql.org/) with [Prisma ORM 6.8.2](https://www.prisma.io/)
- **API**: [tRPC 11.0.0-rc.712](https://trpc.io/)
- **UI Components**: [shadcn/ui](https://ui.shadcn.com/) with [Radix UI](https://www.radix-ui.com/)
- **Styling**: [Tailwind CSS 3.4.1](https://tailwindcss.com/)
- **Authentication**: [NextAuth.js v5](https://authjs.dev/)
- **State Management**: [TanStack Query](https://tanstack.com/query) (via tRPC)
- **Forms**: [React Hook Form](https://react-hook-form.com/) with [Zod](https://zod.dev/)

## 📋 Features

### Completed ✅
- **Employee Associations Management**
  - Associate employees with companies, departments, roles, and sites
  - Advanced autocomplete search by name/ID/passport
  - Validation for overlapping associations
  - Full CRUD operations with soft delete

### In Development 🚧
- **Attendance Agreements**
  - Agreement definitions
  - Shift management
  - Overtime rules
  - Break rules
  - Locations and movement types

### Planned 📅
- **Attendance Reporting**
- **Salary Calculations**
- **Reports and Analytics**
- **External System Integration**

## 🛠️ Installation

### Prerequisites
- Node.js 18+ 
- PostgreSQL database
- pnpm (recommended) or npm/yarn

### Setup

1. **Clone the repository**
   ```bash
   git clone [repository-url]
   cd salary-t3
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Update `.env` with your database credentials:
   ```env
   DATABASE_URL="postgresql://user:password@localhost:5432/salary_db"
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"
   ```

4. **Set up the database**
   ```bash
   pnpm db:push
   ```

5. **Run the development server**
   ```bash
   pnpm dev
   ```

   Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
salary-t3/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── (main)/            # Main layout with navigation
│   │   │   ├── associations/  # Employee associations screen
│   │   │   └── attendance-agreements/ # Attendance agreements (WIP)
│   │   ├── components/            # Shared UI components
│   │   ├── server/               # Backend logic
│   │   │   ├── api/             # tRPC routers
│   │   │   └── db.ts            # Database client
│   │   └── lib/                 # Utilities and helpers
│   ├── prisma/
│   │   └── schema.prisma        # Database schema
│   └── public/                  # Static assets
└── .cursorrules            # Development guidelines
```

## 🔧 Development

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm db:push` - Push schema changes to database
- `pnpm db:studio` - Open Prisma Studio

### Code Conventions

- **Components**: PascalCase (e.g., `EmployeeTable.tsx`)
- **Functions/Hooks**: camelCase (e.g., `useEmployees`)
- **Types**: PascalCase (e.g., `Employee`)
- **Files**: kebab-case (e.g., `employee-form.tsx`)

### Best Practices

1. **Type Safety**: Always use TypeScript types, avoid `any`
2. **Error Handling**: Implement proper error boundaries and user-friendly messages
3. **Accessibility**: Include ARIA labels and keyboard navigation
4. **Performance**: Use React Query for caching and optimistic updates
5. **Hebrew UI**: All user-facing text should be in Hebrew

## 🌐 API Documentation

The API is built with tRPC and follows RESTful conventions:

### Example Endpoints

```typescript
// Employee associations
api.association.getAll()
api.association.create()
api.association.update()
api.association.delete()
api.association.validateAssociations()
```

## 🧪 Testing

```bash
# Run unit tests
pnpm test

# Run integration tests
pnpm test:integration

# Run e2e tests
pnpm test:e2e
```

## 🚀 Deployment

### Production Build

```bash
pnpm build
pnpm start
```

### Environment Variables

Ensure all required environment variables are set in production:
- `DATABASE_URL`
- `NEXTAUTH_SECRET`
- `NEXTAUTH_URL`

## 📝 Documentation

- [Project Progress](./PROJECT_PROGRESS.md) - Detailed development progress
- [Cursor Rules](./.cursorrules) - Development guidelines
- Feature-specific documentation in each module's `.cursorrules` file

## 🤝 Contributing

1. Create a feature branch (`feature/amazing-feature`)
2. Commit your changes with Hebrew commit messages
3. Push to the branch
4. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 👥 Team

- Development team contact: [email]

---

Built with ❤️ using the T3 Stack
