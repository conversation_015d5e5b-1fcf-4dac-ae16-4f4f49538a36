import { CACHE_TIMES } from "@/trpc/query-client";

/**
 * Centralized cache configuration for employer dashboard
 * This file defines caching strategies for different types of data
 */

export const EMPLOYER_CACHE_CONFIG = {
  // User management data
  users: {
    staleTime: CACHE_TIMES.USER_DATA,
    refetchInterval: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
    gcTime: 15 * 60 * 1000, // 15 minutes
  },
  
  // Employee data
  employees: {
    staleTime: CACHE_TIMES.USER_DATA,
    refetchInterval: 3 * 60 * 1000, // 3 minutes
    refetchOnWindowFocus: true,
    gcTime: 15 * 60 * 1000,
  },
  
  // Employee search results
  employeeSearch: {
    staleTime: CACHE_TIMES.SEARCH,
    refetchInterval: false, // No background refetch for search
    refetchOnWindowFocus: false,
    gcTime: 5 * 60 * 1000, // Shorter cache for search results
  },
  
  // Employee details
  employeeDetails: {
    staleTime: CACHE_TIMES.USER_DATA,
    refetchInterval: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
    gcTime: 20 * 60 * 1000, // Longer cache for details
  },
  
  // Employee documents (general)
  employeeDocuments: {
    staleTime: CACHE_TIMES.USER_DATA,
    refetchInterval: 10 * 60 * 1000, // 10 minutes - documents don't change often
    refetchOnWindowFocus: true,
    gcTime: 30 * 60 * 1000, // 30 minutes cache
  },
  
  // Employee ID card documents (very static)
  employeeIdCards: {
    staleTime: CACHE_TIMES.STATIC,
    refetchInterval: false, // No background refetch - ID cards rarely change
    refetchOnWindowFocus: false,
    gcTime: 60 * 60 * 1000, // 1 hour cache
  },
  
  // Form 101 documents (static yearly documents)
  employeeForm101: {
    staleTime: CACHE_TIMES.STATIC,
    refetchInterval: false, // No background refetch - yearly forms are static
    refetchOnWindowFocus: false,
    gcTime: 2 * 60 * 60 * 1000, // 2 hours cache - very static data
  },
  
  // Audit logs (real-time data)
  auditLogs: {
    staleTime: CACHE_TIMES.REALTIME,
    refetchInterval: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
    gcTime: 10 * 60 * 1000,
  },
  
  // Payslips (static data)
  payslips: {
    staleTime: CACHE_TIMES.STATIC,
    refetchInterval: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    gcTime: 30 * 60 * 1000, // Long cache for static data
  },
  
  // Payslip details (very static)
  payslipDetails: {
    staleTime: CACHE_TIMES.STATIC,
    refetchInterval: false, // No background refetch
    refetchOnWindowFocus: false,
    gcTime: 60 * 60 * 1000, // 1 hour cache
  },
  
  // Salary transactions
  salaryTransactions: {
    staleTime: CACHE_TIMES.USER_DATA,
    refetchInterval: 5 * 60 * 1000,
    refetchOnWindowFocus: true,
    gcTime: 15 * 60 * 1000,
  },
  
  // Dashboard metrics
  dashboardMetrics: {
    staleTime: CACHE_TIMES.DASHBOARD,
    refetchInterval: 2 * 60 * 1000,
    refetchOnWindowFocus: true,
    gcTime: 10 * 60 * 1000,
  },
} as const;

/**
 * Cache invalidation strategies for different operations
 */
export const CACHE_INVALIDATION_STRATEGIES = {
  // When a user is created/updated/deleted
  userOperation: (utils: any, employerId: string, userId?: string) => {
    void utils.user.getAll.invalidate({ employerId });
    if (userId) {
      void utils.user.getById.invalidate({ id: userId });
    }
  },
  
  // When an employee is created/updated/deleted
  employeeOperation: (utils: any, employerId: string, employeeId?: string) => {
    void utils.employer.getEmployees.invalidate({ employerId });
    if (employeeId) {
      void utils.employee.getById.invalidate({ id: employeeId });
      void utils.employee.getPayslips.invalidate({ employeeId });
      void utils.employee.getSalaryTransactions.invalidate({ employeeId });
      // Also invalidate employee documents
      void utils.employee.getDocuments.invalidate({ employeeId });
      void utils.employee.getForm101Documents.invalidate({ employeeId });
      void utils.employee.getIdCardDocuments.invalidate({ employeeId });
    }
  },
  
  // When employee documents are modified
  employeeDocumentOperation: (utils: any, employeeId: string, category?: string) => {
    void utils.employee.getDocuments.invalidate({ employeeId });
    
    // Invalidate specific document type caches based on category
    if (category === 'form-101') {
      void utils.employee.getForm101Documents.invalidate({ employeeId });
    } else if (category === 'id-card') {
      void utils.employee.getIdCardDocuments.invalidate({ employeeId });
    }
    
    // Also invalidate employee details as it may show document counts
    void utils.employee.getById.invalidate({ id: employeeId });
  },
  
  // When payslip data changes
  payslipOperation: (utils: any, employeeId: string, payslipId?: string) => {
    void utils.employee.getPayslips.invalidate({ employeeId });
    if (payslipId) {
      void utils.employee.getPayslipDetails.invalidate({ payslipId });
    }
    // Also invalidate employee details as it may include latest payslip
    void utils.employee.getById.invalidate({ id: employeeId });
  },
  
  // When salary transaction data changes
  salaryTransactionOperation: (utils: any, employeeId: string) => {
    void utils.employee.getSalaryTransactions.invalidate({ employeeId });
    // Also invalidate employee details
    void utils.employee.getById.invalidate({ id: employeeId });
  },
} as const;

/**
 * Prefetching strategies for better UX
 */
export const PREFETCH_STRATEGIES = {
  // Prefetch next page if available and not already cached
  prefetchNextPage: (
    utils: any,
    queryFn: any,
    currentData: any,
    currentPage: number,
    queryParams: any,
    cacheConfig: any
  ) => {
    if (currentData && currentData.pageCount > currentPage) {
      const nextPageParams = { ...queryParams, page: currentPage + 1 };
      const nextPageData = queryFn.getData(nextPageParams);
      
      if (!nextPageData) {
        void queryFn.prefetch(nextPageParams, {
          staleTime: cacheConfig.staleTime,
        });
      }
    }
  },
  
  // Prefetch related employee data when viewing employee details
  prefetchEmployeeRelatedData: (utils: any, employeeId: string) => {
    // Prefetch payslips
    void utils.employee.getPayslips.prefetch(
      { employeeId, page: 1, limit: 12 },
      { staleTime: EMPLOYER_CACHE_CONFIG.payslips.staleTime }
    );
    
    // Prefetch recent salary transactions
    void utils.employee.getSalaryTransactions.prefetch(
      { employeeId, page: 1, limit: 20 },
      { staleTime: EMPLOYER_CACHE_CONFIG.salaryTransactions.staleTime }
    );
    
    // Prefetch employee documents
    void utils.employee.getDocuments.prefetch(
      { employeeId, page: 1, limit: 20 },
      { staleTime: EMPLOYER_CACHE_CONFIG.employeeDocuments.staleTime }
    );
    
    // Prefetch ID card documents
    void utils.employee.getIdCardDocuments.prefetch(
      { employeeId },
      { staleTime: EMPLOYER_CACHE_CONFIG.employeeIdCards.staleTime }
    );
    
    // Prefetch Form 101 documents
    void utils.employee.getForm101Documents.prefetch(
      { employeeId },
      { staleTime: EMPLOYER_CACHE_CONFIG.employeeForm101.staleTime }
    );
  },
  
  // Prefetch employee documents by category
  prefetchEmployeeDocumentsByCategory: (utils: any, employeeId: string, categories: string[]) => {
    categories.forEach(category => {
      void utils.employee.getDocuments.prefetch(
        { employeeId, category, page: 1, limit: 20 },
        { staleTime: EMPLOYER_CACHE_CONFIG.employeeDocuments.staleTime }
      );
    });
  },
} as const;

/**
 * Background sync configuration for real-time data
 */
export const BACKGROUND_SYNC_CONFIG = {
  // Enable background sync for critical real-time data
  auditLogs: {
    enabled: true,
    interval: 30 * 1000, // 30 seconds
  },
  
  // Moderate background sync for frequently changing data
  employees: {
    enabled: true,
    interval: 2 * 60 * 1000, // 2 minutes
  },
  
  // Light background sync for occasionally changing data
  users: {
    enabled: true,
    interval: 5 * 60 * 1000, // 5 minutes
  },
  
  // No background sync for static document data
  employeeDocuments: {
    enabled: false,
    interval: false,
  },
} as const;

/**
 * Document category configurations with requirements and UX settings
 */
export const DOCUMENT_CATEGORY_CONFIG = {
  // מסמכים חובה - תז או דרכון + טופס 101 לשנה הנוכחית
  'id-card': {
    displayName: 'תעודת זהות',
    icon: 'IdCard',
    required: true,
    yearly: false,
    maxFiles: 2, // Front and back
    allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeIdCards,
    description: 'תמונת תעודת זהות (קדמי ואחורי)',
    priority: 1,
    color: 'red', // אדום למסמכים חובה
    allowCustomDescription: false,
    alternativeFor: 'passport' // תז או דרכון
  },
  'passport': {
    displayName: 'דרכון',
    icon: 'Passport',
    required: true,
    yearly: false,
    maxFiles: 2, // Main page and photo page
    allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeIdCards,
    description: 'דרכון ישראלי או זר',
    priority: 2,
    color: 'red',
    allowCustomDescription: false,
    alternativeFor: 'id-card' // תז או דרכון
  },
  'form-101': {
    displayName: 'טופס 101',
    icon: 'FileText',
    required: true,
    yearly: true, // מסמך שנתי
    currentYearOnly: true, // רק לשנה הנוכחית
    maxFiles: 1, // One per year
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeForm101,
    description: 'טופס 101 לשנה הנוכחית',
    priority: 3,
    color: 'red',
    allowCustomDescription: false
  },
  
  // כל השאר אופציונלי
  'contract': {
    displayName: 'חוזה עבודה',
    icon: 'FileContract',
    required: false,
    yearly: false,
    maxFiles: 5,
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeDocuments,
    description: 'חוזה עבודה חתום (אופציונלי)',
    priority: 4,
    color: 'blue',
    allowCustomDescription: false
  },
  'bank-details': {
    displayName: 'פרטי בנק',
    icon: 'CreditCard',
    required: false,
    yearly: false,
    maxFiles: 3,
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeDocuments,
    description: 'פרטי חשבון בנק להעברת שכר (אופציונלי)',
    priority: 5,
    color: 'blue',
    allowCustomDescription: false
  },
  'medical': {
    displayName: 'מסמכים רפואיים',
    icon: 'Heart',
    required: false,
    yearly: false,
    maxFiles: 10,
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeDocuments,
    description: 'תעודות רפואיות ואישורי כושר (אופציונלי)',
    priority: 6,
    color: 'blue',
    allowCustomDescription: false
  },
  'education': {
    displayName: 'תעודות השכלה',
    icon: 'GraduationCap',
    required: false,
    yearly: false,
    maxFiles: 10,
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeDocuments,
    description: 'תעודות לימודים והכשרות (אופציונלי)',
    priority: 7,
    color: 'blue',
    allowCustomDescription: false
  },
  'visa': {
    displayName: 'ויזה',
    icon: 'Plane',
    required: false,
    yearly: false,
    maxFiles: 5,
    allowedTypes: ['image/jpeg', 'image/png', 'application/pdf'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeDocuments,
    description: 'ויזה לעובדים זרים (אופציונלי)',
    priority: 8,
    color: 'blue',
    allowCustomDescription: false
  },
  'other': {
    displayName: 'מסמכים אחרים',
    icon: 'File',
    required: false,
    yearly: false,
    maxFiles: 20,
    allowedTypes: ['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    cacheConfig: EMPLOYER_CACHE_CONFIG.employeeDocuments,
    description: 'מסמכים נוספים עם תיאור חופשי',
    priority: 9,
    color: 'gray',
    allowCustomDescription: true // מאפשר תיאור חופשי
  }
} as const;

/**
 * Document requirement levels for UX
 */
export const DOCUMENT_REQUIREMENT_LEVELS = {
  CRITICAL: {
    color: 'red',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-700',
    badgeVariant: 'destructive' as const,
    label: 'חובה'
  },
  REQUIRED: {
    color: 'orange',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    textColor: 'text-orange-700',
    badgeVariant: 'secondary' as const,
    label: 'נדרש'
  },
  OPTIONAL: {
    color: 'blue',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-700',
    badgeVariant: 'outline' as const,
    label: 'אופציונלי'
  },
  OTHER: {
    color: 'gray',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    textColor: 'text-gray-700',
    badgeVariant: 'outline' as const,
    label: 'אחר'
  }
} as const;

/**
 * Utility function to get cache config for a specific data type
 */
export const getCacheConfig = (dataType: keyof typeof EMPLOYER_CACHE_CONFIG) => {
  return EMPLOYER_CACHE_CONFIG[dataType];
};

/**
 * Utility function to get document category config
 */
export const getDocumentCategoryConfig = (category: keyof typeof DOCUMENT_CATEGORY_CONFIG) => {
  return DOCUMENT_CATEGORY_CONFIG[category];
};

/**
 * Utility function to apply cache invalidation strategy
 */
export const applyCacheInvalidation = (
  strategy: keyof typeof CACHE_INVALIDATION_STRATEGIES,
  utils: any,
  employerId: string,
  resourceId?: string
) => {
  return CACHE_INVALIDATION_STRATEGIES[strategy](utils, employerId, resourceId);
};

/**
 * Utility functions for document requirements and UX
 */

// קבלת רשימת מסמכים חובה
export const getRequiredDocumentCategories = () => {
  return Object.entries(DOCUMENT_CATEGORY_CONFIG)
    .filter(([_, config]) => config.required)
    .sort((a, b) => a[1].priority - b[1].priority)
    .map(([category, config]) => ({ category, ...config }));
};

// קבלת רשימת מסמכים אופציונליים
export const getOptionalDocumentCategories = () => {
  return Object.entries(DOCUMENT_CATEGORY_CONFIG)
    .filter(([_, config]) => !config.required)
    .sort((a, b) => a[1].priority - b[1].priority)
    .map(([category, config]) => ({ category, ...config }));
};

// קבלת מסמכים שנתיים (טופס 101)
export const getYearlyDocumentCategories = () => {
  return Object.entries(DOCUMENT_CATEGORY_CONFIG)
    .filter(([_, config]) => config.yearly)
    .map(([category, config]) => ({ category, ...config }));
};

// קבלת רמת דרישה של מסמך
export const getDocumentRequirementLevel = (category: string) => {
  const config = DOCUMENT_CATEGORY_CONFIG[category as keyof typeof DOCUMENT_CATEGORY_CONFIG];
  if (!config) return DOCUMENT_REQUIREMENT_LEVELS.OTHER;
  
  if (config.required && config.color === 'red') {
    return DOCUMENT_REQUIREMENT_LEVELS.CRITICAL;
  } else if (!config.required && config.color === 'blue') {
    return DOCUMENT_REQUIREMENT_LEVELS.OPTIONAL;
  } else {
    return DOCUMENT_REQUIREMENT_LEVELS.OTHER;
  }
};

// בדיקת מסמכים חסרים לעובד
export const getMissingRequiredDocuments = (employeeDocuments: any[], employeeStartYear?: number) => {
  const requiredCategories = getRequiredDocumentCategories();
  const yearlyCategories = getYearlyDocumentCategories();
  const currentYear = new Date().getFullYear();
  
  const missing: Array<{
    category: string;
    displayName: string;
    year?: number;
    priority: number;
    color: string;
  }> = [];
  
  // בדיקת מסמכים חובה רגילים
  requiredCategories.forEach((categoryData) => {
    const { category, displayName, priority, color } = categoryData;
    const yearly = (categoryData as any).yearly;
    const alternativeFor = (categoryData as any).alternativeFor;
    
    if (yearly === true) return; // נטפל בזה בנפרד
    
    // אם זה תז או דרכון - בודקים שיש לפחות אחד מהם
    if (alternativeFor) {
      const hasThisDocument = employeeDocuments.some(doc => doc.category === category);
      const hasAlternativeDocument = employeeDocuments.some(doc => doc.category === alternativeFor);
      
      // אם אין אף אחד מהם, נוסיף רק את התז (priority נמוך יותר)
      if (!hasThisDocument && !hasAlternativeDocument && category === 'id-card') {
        missing.push({ 
          category: 'id-card-or-passport', 
          displayName: 'תעודת זהות או דרכון', 
          priority, 
          color 
        });
      }
    } else {
      // מסמכים רגילים אחרים
      const hasDocument = employeeDocuments.some(doc => doc.category === category);
      if (!hasDocument) {
        missing.push({ category, displayName, priority, color });
      }
    }
  });
  
  // בדיקת מסמכים שנתיים (טופס 101) - רק לשנה הנוכחית
  yearlyCategories.forEach(({ category, displayName, priority, color }) => {
    const config = DOCUMENT_CATEGORY_CONFIG[category as keyof typeof DOCUMENT_CATEGORY_CONFIG];
    const currentYearOnly = (config as any)?.currentYearOnly;
    
    if (currentYearOnly) {
      // רק לשנה הנוכחית
      const hasDocumentForCurrentYear = employeeDocuments.some(
        doc => doc.category === category && 
        ((doc.metadata as any)?.year === currentYear || new Date(doc.uploadedAt).getFullYear() === currentYear)
      );
      
      if (!hasDocumentForCurrentYear) {
        missing.push({ 
          category, 
          displayName: `${displayName} ${currentYear}`, 
          year: currentYear, 
          priority, 
          color 
        });
      }
    } else {
      // לוגיקה ישנה לכל השנים (אם יש מסמכים שנתיים אחרים)
      const startYear = employeeStartYear || currentYear;
      for (let year = startYear; year <= currentYear; year++) {
        const hasDocumentForYear = employeeDocuments.some(
          doc => doc.category === category && 
          ((doc.metadata as any)?.year === year || new Date(doc.uploadedAt).getFullYear() === year)
        );
        
        if (!hasDocumentForYear) {
          missing.push({ 
            category, 
            displayName: `${displayName} ${year}`, 
            year, 
            priority, 
            color 
          });
        }
      }
    }
  });
  
  return missing.sort((a, b) => a.priority - b.priority);
};

// חישוב אחוז השלמת מסמכים
export const calculateDocumentCompletionPercentage = (employeeDocuments: any[], employeeStartYear?: number) => {
  const requiredCategories = getRequiredDocumentCategories();
  const yearlyCategories = getYearlyDocumentCategories();
  const currentYear = new Date().getFullYear();
  
  let totalRequired = 0;
  let completed = 0;
  
  // מסמכים חובה רגילים
  requiredCategories.forEach((categoryData) => {
    const { category, yearly } = categoryData;
    const alternativeFor = (categoryData as any).alternativeFor;
    
    if (yearly) return;
    
    // אם זה תז או דרכון - סופרים רק פעם אחת
    if (alternativeFor) {
      if (category === 'id-card') { // סופרים רק פעם אחת עבור תז או דרכון
        totalRequired++;
        const hasIdCard = employeeDocuments.some(doc => doc.category === 'id-card');
        const hasPassport = employeeDocuments.some(doc => doc.category === 'passport');
        if (hasIdCard || hasPassport) completed++;
      }
      // לא סופרים את הדרכון בנפרד כי כבר ספרנו את התז
    } else {
      totalRequired++;
      const hasDocument = employeeDocuments.some(doc => doc.category === category);
      if (hasDocument) completed++;
    }
  });
  
  // מסמכים שנתיים - רק לשנה הנוכחית
  yearlyCategories.forEach((categoryData) => {
    const { category } = categoryData;
    const config = DOCUMENT_CATEGORY_CONFIG[category as keyof typeof DOCUMENT_CATEGORY_CONFIG];
    const currentYearOnly = (config as any)?.currentYearOnly;
    
    if (currentYearOnly) {
      // רק לשנה הנוכחית
      totalRequired++;
      const hasDocumentForCurrentYear = employeeDocuments.some(
        doc => doc.category === category && 
        ((doc.metadata as any)?.year === currentYear || new Date(doc.uploadedAt).getFullYear() === currentYear)
      );
      if (hasDocumentForCurrentYear) completed++;
    } else {
      // לוגיקה ישנה לכל השנים (אם יש מסמכים שנתיים אחרים)
      const startYear = employeeStartYear || currentYear;
      for (let year = startYear; year <= currentYear; year++) {
        totalRequired++;
        const hasDocumentForYear = employeeDocuments.some(
          doc => doc.category === category && 
          ((doc.metadata as any)?.year === year || new Date(doc.uploadedAt).getFullYear() === year)
        );
        if (hasDocumentForYear) completed++;
      }
    }
  });
  
  return totalRequired > 0 ? Math.round((completed / totalRequired) * 100) : 100;
}; 