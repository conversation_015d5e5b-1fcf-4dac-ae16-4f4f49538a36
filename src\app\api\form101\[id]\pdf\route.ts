import { NextRequest, NextResponse } from "next/server";
import { db } from "@/server/db";
import { generateForm101PDF } from "@/lib/form101-pdf";

// Export route configuration
export const dynamic = 'force-dynamic';

// Define a simplified handler for form101 PDF generation
export async function GET(req: NextRequest, context: any) {
  const id = context.params?.id;
  
  if (!id) {
    return new NextResponse(
      JSON.stringify({ error: "Form 101 ID is required" }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }

  try {
    // Get Form 101 data with employee and tenant information
    const form101 = await db.form101.findUnique({
      where: { id },
      include: {
        employee: {
          include: {
            tenant: true
          }
        }
      }
    });

    if (!form101) {
      return new NextResponse(
        JSON.stringify({ error: "Form 101 not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }

    // Extract data safely with type assertions
    const employee = form101.employee as any;
    const tenant = employee.tenant as any;

    // Prepare employee data
    const employeeData = {
      firstName: employee.firstName || "",
      lastName: employee.lastName || "",
      nationalId: employee.nationalId || "",
      email: employee.contact?.email || "",
      phone: employee.contact?.phone || "",
    };

    // Prepare employer data
    const employerData = {
      name: tenant.name || "",
      businessNumber: tenant.identifier || "",
      address: tenant.address ? JSON.stringify(tenant.address) : "",
      phone: tenant.contact?.phone || "",
    };

    // Generate PDF
    const pdfBuffer = await generateForm101PDF(
      form101 as any,
      employeeData,
      employerData
    );

    // Return PDF with appropriate headers
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="form101_${employee.firstName || 'user'}_${employee.lastName || 'form'}_${form101.taxYear || 'document'}.pdf"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  } catch (error) {
    console.error("Error generating Form 101 PDF:", error);
    return new NextResponse(
      JSON.stringify({ error: "Failed to generate PDF" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
