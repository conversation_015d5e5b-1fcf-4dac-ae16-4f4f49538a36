-- CreateEnum
CREATE TYPE "Role" AS ENUM ('OWNER', 'ADMIN', 'ACCOUNTANT', 'HR', 'EMPLOYEE');

-- CreateEnum
CREATE TYPE "Currency" AS ENUM ('ILS', 'USD', 'EUR', 'OTHER');

-- Create<PERSON>num
CREATE TYPE "PayFrequency" AS ENUM ('MONTHLY', 'BI_WEEKLY', 'WEEKLY');

-- CreateEnum
CREATE TYPE "Basis" AS ENUM ('MONTHLY', 'HOURLY');

-- CreateEnum
CREATE TYPE "AuditAction" AS ENUM ('CREATE', 'UPDATE', 'DELETE');

-- CreateEnum
CREATE TYPE "AlertType" AS ENUM ('INFO', 'WARNING', 'CRITICAL');

-- CreateEnum
CREATE TYPE "AlertCategory" AS ENUM ('VISA_EXPIRATION', 'MISSING_FORM101', 'OVERTIME_LIMIT', 'DEPOSIT_COMPLIANCE', 'UNUSUAL_DEDUCTION', 'GENERAL');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "EmployeeStatus" AS ENUM ('ACTIVE', 'TERMINATED', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "FundType" AS ENUM ('PENSION', 'STUDY', 'MANAGERS_INS', 'COMPENSATION', 'DEPOSIT', 'HISHTALMUT', 'UNION');

-- CreateEnum
CREATE TYPE "PayslipItemType" AS ENUM ('EARNING', 'DEDUCTION', 'EMPLOYER_CONTRIB', 'REIMBURSEMENT');

-- CreateEnum
CREATE TYPE "PayslipItemKod" AS ENUM ('1000', '1001', '1002', '1003', '1004', '1005', '1008', '1010', '1011', '1020', '1021', '1022', '1063', '1077', '1089', '1090', '1091', '0100', '0102', '0113', '0114', '0120', '0131', '0170', '0191', '0192', '0193', '0250', '0312', '0313', '0400', '0500', '0900', '0121', 'GILUM', 'TAX', 'NI_EMP', 'NI_HEALTH', 'PENSION_EMP', 'UNION');

-- CreateEnum
CREATE TYPE "TransferAction" AS ENUM ('IMPORT', 'EXPORT');

-- CreateEnum
CREATE TYPE "AccountType" AS ENUM ('CHECKING', 'SAVINGS', 'CREDIT', 'LOAN');

-- CreateEnum
CREATE TYPE "ReportType" AS ENUM ('FORM_102', 'PAYROLL_SUMMARY', 'TAX_REPORT', 'EMPLOYEE_OVERVIEW');

-- CreateEnum
CREATE TYPE "PositionType" AS ENUM ('MONTHLY', 'HOURLY', 'DAILY', 'FINAL_PAY');

-- CreateEnum
CREATE TYPE "LeaveType" AS ENUM ('VACATION', 'SICK', 'UNPAID', 'MATERNITY', 'MILITARY', 'INTER_VISA', 'OTHER');

-- CreateEnum
CREATE TYPE "AgreementType" AS ENUM ('PERSONAL', 'COLLECTIVE', 'TEMPLATE');

-- CreateEnum
CREATE TYPE "Sector" AS ENUM ('AGRICULTURE', 'CONSTRUCTION', 'CAREGIVING', 'INDUSTRY', 'HOSPITALITY', 'TECHNOLOGY', 'OTHER');

-- CreateEnum
CREATE TYPE "MaritalStatus" AS ENUM ('SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED');

-- CreateEnum
CREATE TYPE "PayslipStatus" AS ENUM ('DRAFT', 'CALCULATED', 'APPROVED', 'PAID', 'CANCELLED');

-- CreateTable
CREATE TABLE "Tenant" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "plan" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Tenant_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "role" "Role" NOT NULL,
    "password" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "preferences" JSONB,
    "mustResetPassword" BOOLEAN NOT NULL DEFAULT false,
    "employerId" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Alert" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "userId" UUID,
    "employeeId" UUID,
    "type" "AlertType" NOT NULL DEFAULT 'INFO',
    "category" "AlertCategory",
    "message" TEXT NOT NULL,
    "context" JSONB,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    "dueDate" TIMESTAMP(3),
    "severity" TEXT,
    "isResolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Alert_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AuditLog" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "modelName" TEXT NOT NULL,
    "recordId" UUID,
    "action" "AuditAction" NOT NULL,
    "oldValues" JSONB,
    "newValues" JSONB,
    "userId" UUID,
    "userEmail" TEXT,
    "ipAddress" TEXT,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Employer" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "identifier" TEXT,
    "taxId" TEXT,
    "companyId" TEXT,
    "industry" "Sector",
    "address" JSONB,
    "contact" JSONB,
    "profilePictureUrl" TEXT,
    "profilePictureKey" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Employer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Department" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Department_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BankAccount" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "bankName" TEXT NOT NULL,
    "branchCode" TEXT,
    "accountNumber" TEXT NOT NULL,
    "bankCode" INTEGER,
    "branchNumber" INTEGER,
    "currency" "Currency" NOT NULL DEFAULT 'ILS',
    "accountType" "AccountType",
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "percentage" DECIMAL(5,2),
    "employeeId" UUID,
    "employerId" UUID,
    "effectiveFrom" TIMESTAMP(3),
    "effectiveTo" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BankAccount_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Employee" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "departmentId" UUID,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "nationalId" TEXT NOT NULL,
    "status" "EmployeeStatus" NOT NULL DEFAULT 'ACTIVE',
    "birthDate" TIMESTAMP(3),
    "address" JSONB,
    "contact" JSONB,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "profilePictureUrl" TEXT,
    "profilePictureKey" TEXT,
    "isForeign" BOOLEAN NOT NULL DEFAULT false,
    "country" TEXT,
    "sector" "Sector",
    "agreementType" "AgreementType",
    "isResidentForNI" BOOLEAN NOT NULL DEFAULT true,
    "visaNumber" TEXT,
    "visaExpiry" TIMESTAMP(3),
    "visaType" TEXT,
    "baseSalary" DECIMAL(12,2),
    "travelAllowance" DECIMAL(12,2),
    "primaryAssignment" TEXT,
    "secondaryAssignment" TEXT,
    "isControllingOwner" BOOLEAN,
    "isUnsupervised" BOOLEAN,
    "terminationReason" TEXT,
    "assignments" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Employee_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalaryRecord" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "basis" "Basis" NOT NULL DEFAULT 'MONTHLY',
    "currency" "Currency" NOT NULL DEFAULT 'ILS',
    "payFrequency" "PayFrequency" NOT NULL DEFAULT 'MONTHLY',
    "amount" DECIMAL(12,2) NOT NULL,
    "effectiveFrom" TIMESTAMP(3) NOT NULL,
    "effectiveTo" TIMESTAMP(3),
    "positionType" "PositionType",
    "hourlyRate" DECIMAL(12,2),
    "dailyRate" DECIMAL(12,2),
    "workedHours" DECIMAL(8,2),
    "workedDays" DECIMAL(5,2),
    "standardMonthlyHours" DECIMAL(8,2),
    "standardDailyHours" DECIMAL(5,2),
    "standardMonthDays" INTEGER,
    "workDaysPerWeek" INTEGER,
    "positionPercentage" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SalaryRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Payslip" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "periodStart" TIMESTAMP(3),
    "periodEnd" TIMESTAMP(3),
    "status" "PayslipStatus" NOT NULL DEFAULT 'DRAFT',
    "grossPay" DECIMAL(12,2) NOT NULL,
    "netPay" DECIMAL(12,2) NOT NULL,
    "taxDeducted" DECIMAL(12,2) NOT NULL,
    "insuranceDeducted" DECIMAL(12,2) NOT NULL,
    "otherDeductions" DECIMAL(12,2),
    "allowances" DECIMAL(12,2),
    "currency" "Currency" NOT NULL DEFAULT 'ILS',
    "healthInsurance" DECIMAL(12,2),
    "pensionEmployee" DECIMAL(12,2),
    "pensionEmployer" DECIMAL(12,2),
    "severancePay" DECIMAL(12,2),
    "netDeposit" DECIMAL(12,2),
    "breakdown" JSONB,
    "issuedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "Payslip_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PayslipItem" (
    "id" UUID NOT NULL,
    "payslipId" UUID NOT NULL,
    "description" TEXT NOT NULL,
    "amount" DECIMAL(12,2) NOT NULL,
    "type" "PayslipItemType" NOT NULL,
    "kod" "PayslipItemKod",
    "rate" DECIMAL(12,2),
    "units" DECIMAL(12,2),
    "percentage" DECIMAL(5,2),
    "isDebit" BOOLEAN NOT NULL DEFAULT false,
    "isInfo" BOOLEAN NOT NULL DEFAULT false,
    "departmentId" UUID,
    "note" TEXT,

    CONSTRAINT "PayslipItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SalaryTransaction" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "periodMonth" INTEGER NOT NULL,
    "periodYear" INTEGER NOT NULL,
    "componentCode" "PayslipItemKod",
    "description" TEXT,
    "quantity" DECIMAL(12,2),
    "rate" DECIMAL(12,2),
    "percentage" DECIMAL(5,2),
    "amount" DECIMAL(12,2),
    "fromDate" TIMESTAMP(3),
    "toDate" TIMESTAMP(3),
    "departmentId" UUID,
    "source" TEXT,
    "isGrossedUp" BOOLEAN NOT NULL DEFAULT false,
    "isProcessed" BOOLEAN NOT NULL DEFAULT false,
    "userId" UUID,
    "note" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "SalaryTransaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProvidentFund" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "fundNumber" TEXT NOT NULL,
    "fundType" "FundType" NOT NULL DEFAULT 'PENSION',

    CONSTRAINT "ProvidentFund_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProvidentFundContribution" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "fundId" UUID NOT NULL,
    "payslipId" UUID,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "employeeAmount" DECIMAL(12,2) NOT NULL,
    "employerAmount" DECIMAL(12,2) NOT NULL,
    "severancePay" DECIMAL(12,2) NOT NULL,
    "totalAmount" DECIMAL(12,2) NOT NULL,
    "capApplied" BOOLEAN NOT NULL DEFAULT false,
    "fundName" TEXT,

    CONSTRAINT "ProvidentFundContribution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "NationalInsuranceRecord" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "employeeNI" DECIMAL(12,2) NOT NULL,
    "employerNI" DECIMAL(12,2) NOT NULL,
    "healthNI" DECIMAL(12,2) NOT NULL,
    "totalNI" DECIMAL(12,2) NOT NULL,
    "earnings" DECIMAL(12,2) NOT NULL,

    CONSTRAINT "NationalInsuranceRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "LeaveRecord" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "leaveType" "LeaveType" NOT NULL,
    "leaveTypeCode" INTEGER,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "entitlement" DECIMAL(8,2),
    "taken" DECIMAL(8,2),
    "previousBalance" DECIMAL(8,2),
    "notes" TEXT,
    "autoCalculated" BOOLEAN,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LeaveRecord_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Form101" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "taxYear" INTEGER,
    "maritalStatus" "MaritalStatus",
    "spouseWorks" BOOLEAN NOT NULL DEFAULT false,
    "childrenCount" INTEGER NOT NULL DEFAULT 0,
    "childrenUnder5" INTEGER NOT NULL DEFAULT 0,
    "childrenUnder18" INTEGER NOT NULL DEFAULT 0,
    "additionalCreditPoints" DECIMAL(5,2),
    "isMainEmployer" BOOLEAN NOT NULL DEFAULT true,
    "hasAdditionalIncome" BOOLEAN NOT NULL DEFAULT false,
    "exemptionPercentage" DECIMAL(5,2),
    "taxCoordinationNumber" TEXT,
    "overrideTaxRate" DECIMAL(5,2),
    "data" JSONB,
    "signedAt" TIMESTAMP(3),
    "validUntil" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),
    "documentId" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Form101_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Form102" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "month" INTEGER NOT NULL,
    "data" JSONB NOT NULL,
    "submittedAt" TIMESTAMP(3),
    "documentId" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Form102_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Form106" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employeeId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "data" JSONB NOT NULL,
    "generatedAt" TIMESTAMP(3),
    "documentId" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Form106_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Form126" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID NOT NULL,
    "year" INTEGER NOT NULL,
    "data" JSONB NOT NULL,
    "submittedAt" TIMESTAMP(3),
    "documentId" UUID,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Form126_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Report" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "employerId" UUID,
    "type" "ReportType" NOT NULL,
    "year" INTEGER,
    "month" INTEGER,
    "parameters" JSONB,
    "filePath" TEXT,
    "generatedBy" UUID,
    "generatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "version" INTEGER NOT NULL DEFAULT 1,

    CONSTRAINT "Report_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Document" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "title" TEXT,
    "fileName" TEXT NOT NULL,
    "fileType" TEXT NOT NULL,
    "fileSize" INTEGER NOT NULL,
    "url" TEXT NOT NULL,
    "category" TEXT,
    "referenceModel" TEXT,
    "referenceId" UUID,
    "metadata" JSONB,
    "s3Key" TEXT,
    "employerId" UUID,
    "employeeId" UUID,
    "uploadedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "DocumentToPayslip" (
    "documentId" UUID NOT NULL,
    "payslipId" UUID NOT NULL,

    CONSTRAINT "DocumentToPayslip_pkey" PRIMARY KEY ("documentId","payslipId")
);

-- CreateTable
CREATE TABLE "ImportExportHistory" (
    "id" UUID NOT NULL,
    "tenantId" UUID NOT NULL,
    "userId" UUID,
    "action" "TransferAction" NOT NULL,
    "entity" TEXT NOT NULL,
    "fileName" TEXT,
    "recordCount" INTEGER,
    "status" TEXT NOT NULL,
    "details" JSONB,
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "finishedAt" TIMESTAMP(3),

    CONSTRAINT "ImportExportHistory_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Tenant_name_key" ON "Tenant"("name");

-- CreateIndex
CREATE INDEX "User_tenantId_idx" ON "User"("tenantId");

-- CreateIndex
CREATE INDEX "User_employerId_idx" ON "User"("employerId");

-- CreateIndex
CREATE UNIQUE INDEX "User_tenantId_email_key" ON "User"("tenantId", "email");

-- CreateIndex
CREATE INDEX "Alert_tenantId_idx" ON "Alert"("tenantId");

-- CreateIndex
CREATE INDEX "Alert_userId_idx" ON "Alert"("userId");

-- CreateIndex
CREATE INDEX "Alert_employeeId_idx" ON "Alert"("employeeId");

-- CreateIndex
CREATE INDEX "Alert_category_idx" ON "Alert"("category");

-- CreateIndex
CREATE INDEX "Alert_isResolved_idx" ON "Alert"("isResolved");

-- CreateIndex
CREATE INDEX "AuditLog_tenantId_idx" ON "AuditLog"("tenantId");

-- CreateIndex
CREATE INDEX "AuditLog_modelName_recordId_idx" ON "AuditLog"("modelName", "recordId");

-- CreateIndex
CREATE INDEX "AuditLog_userId_idx" ON "AuditLog"("userId");

-- CreateIndex
CREATE INDEX "Employer_tenantId_idx" ON "Employer"("tenantId");

-- CreateIndex
CREATE INDEX "Employer_profilePictureUrl_idx" ON "Employer"("profilePictureUrl");

-- CreateIndex
CREATE UNIQUE INDEX "Employer_tenantId_name_key" ON "Employer"("tenantId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Employer_tenantId_companyId_key" ON "Employer"("tenantId", "companyId");

-- CreateIndex
CREATE INDEX "Department_tenantId_idx" ON "Department"("tenantId");

-- CreateIndex
CREATE INDEX "Department_employerId_idx" ON "Department"("employerId");

-- CreateIndex
CREATE UNIQUE INDEX "Department_employerId_name_key" ON "Department"("employerId", "name");

-- CreateIndex
CREATE UNIQUE INDEX "Department_employerId_code_key" ON "Department"("employerId", "code");

-- CreateIndex
CREATE INDEX "BankAccount_tenantId_idx" ON "BankAccount"("tenantId");

-- CreateIndex
CREATE INDEX "BankAccount_employeeId_idx" ON "BankAccount"("employeeId");

-- CreateIndex
CREATE INDEX "BankAccount_employerId_idx" ON "BankAccount"("employerId");

-- CreateIndex
CREATE INDEX "Employee_tenantId_idx" ON "Employee"("tenantId");

-- CreateIndex
CREATE INDEX "Employee_employerId_idx" ON "Employee"("employerId");

-- CreateIndex
CREATE INDEX "Employee_nationalId_idx" ON "Employee"("nationalId");

-- CreateIndex
CREATE INDEX "Employee_tenantId_employerId_idx" ON "Employee"("tenantId", "employerId");

-- CreateIndex
CREATE INDEX "Employee_profilePictureUrl_idx" ON "Employee"("profilePictureUrl");

-- CreateIndex
CREATE INDEX "Employee_isForeign_idx" ON "Employee"("isForeign");

-- CreateIndex
CREATE INDEX "Employee_sector_idx" ON "Employee"("sector");

-- CreateIndex
CREATE INDEX "Employee_visaExpiry_idx" ON "Employee"("visaExpiry");

-- CreateIndex
CREATE INDEX "Employee_departmentId_idx" ON "Employee"("departmentId");

-- CreateIndex
CREATE UNIQUE INDEX "Employee_tenantId_nationalId_key" ON "Employee"("tenantId", "nationalId");

-- CreateIndex
CREATE INDEX "SalaryRecord_tenantId_idx" ON "SalaryRecord"("tenantId");

-- CreateIndex
CREATE INDEX "SalaryRecord_employeeId_idx" ON "SalaryRecord"("employeeId");

-- CreateIndex
CREATE INDEX "Payslip_tenantId_idx" ON "Payslip"("tenantId");

-- CreateIndex
CREATE INDEX "Payslip_employeeId_idx" ON "Payslip"("employeeId");

-- CreateIndex
CREATE INDEX "Payslip_year_month_idx" ON "Payslip"("year", "month");

-- CreateIndex
CREATE INDEX "Payslip_status_idx" ON "Payslip"("status");

-- CreateIndex
CREATE UNIQUE INDEX "Payslip_employeeId_year_month_key" ON "Payslip"("employeeId", "year", "month");

-- CreateIndex
CREATE INDEX "PayslipItem_payslipId_idx" ON "PayslipItem"("payslipId");

-- CreateIndex
CREATE INDEX "PayslipItem_kod_idx" ON "PayslipItem"("kod");

-- CreateIndex
CREATE INDEX "PayslipItem_type_idx" ON "PayslipItem"("type");

-- CreateIndex
CREATE INDEX "PayslipItem_departmentId_idx" ON "PayslipItem"("departmentId");

-- CreateIndex
CREATE INDEX "SalaryTransaction_tenantId_idx" ON "SalaryTransaction"("tenantId");

-- CreateIndex
CREATE INDEX "SalaryTransaction_employeeId_idx" ON "SalaryTransaction"("employeeId");

-- CreateIndex
CREATE INDEX "SalaryTransaction_periodYear_periodMonth_idx" ON "SalaryTransaction"("periodYear", "periodMonth");

-- CreateIndex
CREATE INDEX "SalaryTransaction_isProcessed_idx" ON "SalaryTransaction"("isProcessed");

-- CreateIndex
CREATE INDEX "SalaryTransaction_departmentId_idx" ON "SalaryTransaction"("departmentId");

-- CreateIndex
CREATE INDEX "SalaryTransaction_userId_idx" ON "SalaryTransaction"("userId");

-- CreateIndex
CREATE INDEX "ProvidentFund_tenantId_idx" ON "ProvidentFund"("tenantId");

-- CreateIndex
CREATE UNIQUE INDEX "ProvidentFund_tenantId_fundNumber_key" ON "ProvidentFund"("tenantId", "fundNumber");

-- CreateIndex
CREATE INDEX "ProvidentFundContribution_tenantId_idx" ON "ProvidentFundContribution"("tenantId");

-- CreateIndex
CREATE INDEX "ProvidentFundContribution_employeeId_idx" ON "ProvidentFundContribution"("employeeId");

-- CreateIndex
CREATE INDEX "ProvidentFundContribution_fundId_idx" ON "ProvidentFundContribution"("fundId");

-- CreateIndex
CREATE INDEX "ProvidentFundContribution_year_month_idx" ON "ProvidentFundContribution"("year", "month");

-- CreateIndex
CREATE INDEX "ProvidentFundContribution_tenantId_year_month_idx" ON "ProvidentFundContribution"("tenantId", "year", "month");

-- CreateIndex
CREATE INDEX "ProvidentFundContribution_payslipId_idx" ON "ProvidentFundContribution"("payslipId");

-- CreateIndex
CREATE UNIQUE INDEX "ProvidentFundContribution_employeeId_fundId_year_month_key" ON "ProvidentFundContribution"("employeeId", "fundId", "year", "month");

-- CreateIndex
CREATE INDEX "NationalInsuranceRecord_tenantId_idx" ON "NationalInsuranceRecord"("tenantId");

-- CreateIndex
CREATE INDEX "NationalInsuranceRecord_employeeId_idx" ON "NationalInsuranceRecord"("employeeId");

-- CreateIndex
CREATE INDEX "NationalInsuranceRecord_year_month_idx" ON "NationalInsuranceRecord"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "NationalInsuranceRecord_employeeId_year_month_key" ON "NationalInsuranceRecord"("employeeId", "year", "month");

-- CreateIndex
CREATE INDEX "LeaveRecord_tenantId_idx" ON "LeaveRecord"("tenantId");

-- CreateIndex
CREATE INDEX "LeaveRecord_employeeId_idx" ON "LeaveRecord"("employeeId");

-- CreateIndex
CREATE INDEX "LeaveRecord_leaveType_idx" ON "LeaveRecord"("leaveType");

-- CreateIndex
CREATE INDEX "LeaveRecord_startDate_endDate_idx" ON "LeaveRecord"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "LeaveRecord_year_month_idx" ON "LeaveRecord"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "Form101_employeeId_key" ON "Form101"("employeeId");

-- CreateIndex
CREATE INDEX "Form101_tenantId_idx" ON "Form101"("tenantId");

-- CreateIndex
CREATE INDEX "Form101_employeeId_idx" ON "Form101"("employeeId");

-- CreateIndex
CREATE INDEX "Form101_taxYear_idx" ON "Form101"("taxYear");

-- CreateIndex
CREATE INDEX "Form102_tenantId_idx" ON "Form102"("tenantId");

-- CreateIndex
CREATE INDEX "Form102_employerId_idx" ON "Form102"("employerId");

-- CreateIndex
CREATE INDEX "Form102_year_month_idx" ON "Form102"("year", "month");

-- CreateIndex
CREATE UNIQUE INDEX "Form102_employerId_year_month_key" ON "Form102"("employerId", "year", "month");

-- CreateIndex
CREATE INDEX "Form106_tenantId_idx" ON "Form106"("tenantId");

-- CreateIndex
CREATE INDEX "Form106_employeeId_idx" ON "Form106"("employeeId");

-- CreateIndex
CREATE INDEX "Form106_year_idx" ON "Form106"("year");

-- CreateIndex
CREATE UNIQUE INDEX "Form106_employeeId_year_key" ON "Form106"("employeeId", "year");

-- CreateIndex
CREATE INDEX "Form126_tenantId_idx" ON "Form126"("tenantId");

-- CreateIndex
CREATE INDEX "Form126_employerId_idx" ON "Form126"("employerId");

-- CreateIndex
CREATE INDEX "Form126_year_idx" ON "Form126"("year");

-- CreateIndex
CREATE UNIQUE INDEX "Form126_employerId_year_key" ON "Form126"("employerId", "year");

-- CreateIndex
CREATE INDEX "Report_tenantId_idx" ON "Report"("tenantId");

-- CreateIndex
CREATE INDEX "Report_employerId_idx" ON "Report"("employerId");

-- CreateIndex
CREATE INDEX "Report_year_month_idx" ON "Report"("year", "month");

-- CreateIndex
CREATE INDEX "Report_type_idx" ON "Report"("type");

-- CreateIndex
CREATE INDEX "Document_tenantId_idx" ON "Document"("tenantId");

-- CreateIndex
CREATE INDEX "Document_referenceModel_referenceId_idx" ON "Document"("referenceModel", "referenceId");

-- CreateIndex
CREATE INDEX "Document_employerId_idx" ON "Document"("employerId");

-- CreateIndex
CREATE INDEX "Document_employeeId_idx" ON "Document"("employeeId");

-- CreateIndex
CREATE INDEX "Document_category_idx" ON "Document"("category");

-- CreateIndex
CREATE INDEX "Document_fileType_idx" ON "Document"("fileType");

-- CreateIndex
CREATE INDEX "Document_s3Key_idx" ON "Document"("s3Key");

-- CreateIndex
CREATE INDEX "ImportExportHistory_tenantId_idx" ON "ImportExportHistory"("tenantId");

-- CreateIndex
CREATE INDEX "ImportExportHistory_userId_idx" ON "ImportExportHistory"("userId");

-- CreateIndex
CREATE INDEX "ImportExportHistory_action_entity_idx" ON "ImportExportHistory"("action", "entity");

-- CreateIndex
CREATE INDEX "ImportExportHistory_status_idx" ON "ImportExportHistory"("status");

-- CreateIndex
CREATE INDEX "ImportExportHistory_startedAt_idx" ON "ImportExportHistory"("startedAt");

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Alert" ADD CONSTRAINT "Alert_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Alert" ADD CONSTRAINT "Alert_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Alert" ADD CONSTRAINT "Alert_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Employer" ADD CONSTRAINT "Employer_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Department" ADD CONSTRAINT "Department_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Department" ADD CONSTRAINT "Department_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankAccount" ADD CONSTRAINT "BankAccount_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankAccount" ADD CONSTRAINT "BankAccount_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankAccount" ADD CONSTRAINT "BankAccount_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryRecord" ADD CONSTRAINT "SalaryRecord_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryRecord" ADD CONSTRAINT "SalaryRecord_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payslip" ADD CONSTRAINT "Payslip_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payslip" ADD CONSTRAINT "Payslip_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayslipItem" ADD CONSTRAINT "PayslipItem_payslipId_fkey" FOREIGN KEY ("payslipId") REFERENCES "Payslip"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PayslipItem" ADD CONSTRAINT "PayslipItem_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryTransaction" ADD CONSTRAINT "SalaryTransaction_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryTransaction" ADD CONSTRAINT "SalaryTransaction_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryTransaction" ADD CONSTRAINT "SalaryTransaction_departmentId_fkey" FOREIGN KEY ("departmentId") REFERENCES "Department"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SalaryTransaction" ADD CONSTRAINT "SalaryTransaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProvidentFund" ADD CONSTRAINT "ProvidentFund_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProvidentFundContribution" ADD CONSTRAINT "ProvidentFundContribution_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProvidentFundContribution" ADD CONSTRAINT "ProvidentFundContribution_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProvidentFundContribution" ADD CONSTRAINT "ProvidentFundContribution_fundId_fkey" FOREIGN KEY ("fundId") REFERENCES "ProvidentFund"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProvidentFundContribution" ADD CONSTRAINT "ProvidentFundContribution_payslipId_fkey" FOREIGN KEY ("payslipId") REFERENCES "Payslip"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NationalInsuranceRecord" ADD CONSTRAINT "NationalInsuranceRecord_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "NationalInsuranceRecord" ADD CONSTRAINT "NationalInsuranceRecord_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LeaveRecord" ADD CONSTRAINT "LeaveRecord_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LeaveRecord" ADD CONSTRAINT "LeaveRecord_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form101" ADD CONSTRAINT "Form101_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form101" ADD CONSTRAINT "Form101_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form101" ADD CONSTRAINT "Form101_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form102" ADD CONSTRAINT "Form102_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form102" ADD CONSTRAINT "Form102_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form102" ADD CONSTRAINT "Form102_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form106" ADD CONSTRAINT "Form106_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form106" ADD CONSTRAINT "Form106_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form106" ADD CONSTRAINT "Form106_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form126" ADD CONSTRAINT "Form126_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form126" ADD CONSTRAINT "Form126_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Form126" ADD CONSTRAINT "Form126_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Report" ADD CONSTRAINT "Report_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Report" ADD CONSTRAINT "Report_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Report" ADD CONSTRAINT "Report_generatedBy_fkey" FOREIGN KEY ("generatedBy") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_employerId_fkey" FOREIGN KEY ("employerId") REFERENCES "Employer"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Document" ADD CONSTRAINT "Document_employeeId_fkey" FOREIGN KEY ("employeeId") REFERENCES "Employee"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentToPayslip" ADD CONSTRAINT "DocumentToPayslip_documentId_fkey" FOREIGN KEY ("documentId") REFERENCES "Document"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "DocumentToPayslip" ADD CONSTRAINT "DocumentToPayslip_payslipId_fkey" FOREIGN KEY ("payslipId") REFERENCES "Payslip"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImportExportHistory" ADD CONSTRAINT "ImportExportHistory_tenantId_fkey" FOREIGN KEY ("tenantId") REFERENCES "Tenant"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ImportExportHistory" ADD CONSTRAINT "ImportExportHistory_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
