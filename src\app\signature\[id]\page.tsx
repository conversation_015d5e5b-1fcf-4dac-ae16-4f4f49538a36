"use client";

import { useParams } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Clock, FileText, Eye, Trash2, Download } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface SignaturePageData {
  form101: {
    id: string;
    taxYear: number;
    maritalStatus: string;
    spouseWorks: boolean;
    childrenCount: number;
    isMainEmployer: boolean;
    hasAdditionalIncome: boolean;
  };
  employee: {
    firstName: string;
    lastName: string;
    nationalId: string;
    email?: string;
    phone?: string;
  };
  employer: {
    name: string;
    businessNumber: string;
    address?: string;
    phone?: string;
  };
  expiresAt: Date;
  status: 'pending' | 'signed' | 'expired' | 'cancelled';
  pdfPreviewUrl?: string;
}

export default function SignaturePage() {
  const params = useParams();
  const signatureId = params?.id as string;

  const [data, setData] = useState<SignaturePageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [signing, setSigning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPdfPreview, setShowPdfPreview] = useState(false);
  const [signatureData, setSignatureData] = useState<string | null>(null);
  const [showSignaturePad, setShowSignaturePad] = useState(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (signatureId) {
      fetchSignatureData();
    }
  }, [signatureId]);

  const fetchSignatureData = async () => {
    try {
      const response = await fetch(`/api/signature/${signatureId}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setData({
          ...result.data,
          expiresAt: new Date(result.data.expiresAt),
        });
      } else {
        throw new Error(result.error || "Failed to fetch signature data");
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching signature data:", error);
      setError("שגיאה בטעינת נתוני החתימה");
      setLoading(false);
    }
  };

  // Signature pad functions
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.beginPath();
    const clientX = 'touches' in e ? e.touches[0]?.clientX ?? 0 : e.clientX;
    const clientY = 'touches' in e ? e.touches[0]?.clientY ?? 0 : e.clientY;
    ctx.moveTo(clientX - rect.left, clientY - rect.top);

    const draw = (moveEvent: MouseEvent | TouchEvent) => {
      const moveClientX = 'touches' in moveEvent ? moveEvent.touches[0]?.clientX ?? 0 : moveEvent.clientX;
      const moveClientY = 'touches' in moveEvent ? moveEvent.touches[0]?.clientY ?? 0 : moveEvent.clientY;
      ctx.lineTo(moveClientX - rect.left, moveClientY - rect.top);
      ctx.stroke();
    };

    const stopDrawing = () => {
      document.removeEventListener('mousemove', draw);
      document.removeEventListener('mouseup', stopDrawing);
      document.removeEventListener('touchmove', draw);
      document.removeEventListener('touchend', stopDrawing);

      // Save signature data
      const dataURL = canvas.toDataURL();
      setSignatureData(dataURL);
    };

    document.addEventListener('mousemove', draw);
    document.addEventListener('mouseup', stopDrawing);
    document.addEventListener('touchmove', draw);
    document.addEventListener('touchend', stopDrawing);
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setSignatureData(null);
  };

  const handleSign = async () => {
    if (!signatureData) {
      toast.error("אנא צייר חתימה לפני השלמת התהליך");
      return;
    }

    setSigning(true);
    try {
      const response = await fetch(`/api/signature/${signatureId}/sign`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          signatureData: signatureData,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setData(prev => prev ? { ...prev, status: 'signed' } : null);
        toast.success("הטופס נחתם בהצלחה!");
        setShowSignaturePad(false);

        // Refresh the data to get updated status
        await fetchSignatureData();
      } else {
        throw new Error(result.error || "Failed to sign form");
      }

      setSigning(false);
    } catch (error) {
      console.error("Error signing form:", error);
      toast.error("שגיאה בחתימה על הטופס");
      setSigning(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6">
            <Skeleton className="h-8 w-48 mb-4" />
            <Skeleton className="h-4 w-32 mb-6" />
            <Skeleton className="h-32 w-full mb-4" />
            <Skeleton className="h-10 w-32" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h1 className="text-xl font-semibold mb-2">שגיאה</h1>
            <p className="text-gray-600 mb-4">{error || "לא ניתן לטעון את נתוני החתימה"}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isExpired = new Date() > data.expiresAt;
  const isSigned = data.status === 'signed';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="flex items-center justify-center gap-2 mb-2">
            <FileText className="h-6 w-6" />
            <CardTitle>חתימה על טופס 101</CardTitle>
          </div>
          <p className="text-sm text-gray-600">
            שנת מס {data.form101.taxYear}
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Employee Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי העובד</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">שם:</span> {data.employee.firstName} {data.employee.lastName}</p>
              <p><span className="font-medium">תעודת זהות:</span> {data.employee.nationalId}</p>
              {data.employee.email && <p><span className="font-medium">אימייל:</span> {data.employee.email}</p>}
              {data.employee.phone && <p><span className="font-medium">טלפון:</span> {data.employee.phone}</p>}
            </div>
          </div>

          {/* Employer Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי המעסיק</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">שם החברה:</span> {data.employer.name}</p>
              <p><span className="font-medium">מספר עסק:</span> {data.employer.businessNumber}</p>
              {data.employer.address && <p><span className="font-medium">כתובת:</span> {data.employer.address}</p>}
              {data.employer.phone && <p><span className="font-medium">טלפון:</span> {data.employer.phone}</p>}
            </div>
          </div>

          {/* Form Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">פרטי הטופס</h3>
            <div className="space-y-1 text-sm">
              <p><span className="font-medium">מצב משפחתי:</span> {data.form101.maritalStatus === 'SINGLE' ? 'רווק/ה' : data.form101.maritalStatus}</p>
              <p><span className="font-medium">מספר ילדים:</span> {data.form101.childrenCount}</p>
              <p><span className="font-medium">מעסיק עיקרי:</span> {data.form101.isMainEmployer ? 'כן' : 'לא'}</p>
              <p><span className="font-medium">הכנסה נוספת:</span> {data.form101.hasAdditionalIncome ? 'כן' : 'לא'}</p>
            </div>
          </div>



          {/* Status */}
          <div className="text-center">
            {isSigned ? (
              <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">הטופס נחתם בהצלחה</span>
              </div>
            ) : isExpired ? (
              <div className="flex items-center justify-center gap-2 text-red-600 mb-4">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">פג תוקף הקישור לחתימה</span>
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2 text-blue-600 mb-4">
                <Clock className="h-5 w-5" />
                <span className="font-medium">ממתין לחתימה</span>
              </div>
            )}

            <p className="text-sm text-gray-600 mb-6">
              תוקף הקישור: {data.expiresAt.toLocaleDateString('he-IL')}
            </p>

            {!isSigned && !isExpired && (
              <div className="space-y-4">
                <Button
                  onClick={() => setShowPdfPreview(true)}
                  variant="outline"
                  className="w-full max-w-xs"
                >
                  <Eye className="h-4 w-4 ml-2" />
                  צפה בטופס לפני החתימה
                </Button>

                <Button
                  onClick={() => setShowSignaturePad(true)}
                  disabled={signing}
                  className="w-full max-w-xs"
                >
                  {signing ? "חותם..." : "חתום על הטופס"}
                </Button>
              </div>
            )}
          </div>

          {/* Legal Notice */}
          <div className="text-xs text-gray-500 text-center border-t pt-4">
            <p>בחתימה על טופס זה אני מאשר/ת כי הפרטים נכונים ומדויקים.</p>
            <p>החתימה מהווה הסכמה לעיבוד הנתונים לצורכי חישוב מס הכנסה.</p>
          </div>
        </CardContent>
      </Card>

      {/* PDF Preview Dialog */}
      <Dialog open={showPdfPreview} onOpenChange={setShowPdfPreview}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>תצוגה מקדימה - טופס 101</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            {data?.pdfPreviewUrl ? (
              <iframe
                src={data.pdfPreviewUrl}
                className="w-full h-[70vh] border rounded"
                title="Form 101 Preview"
              />
            ) : (
              <div className="flex items-center justify-center h-64 bg-gray-100 rounded">
                <p className="text-gray-500">תצוגה מקדימה לא זמינה</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Signature Pad Dialog */}
      <Dialog open={showSignaturePad} onOpenChange={setShowSignaturePad}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>חתימה על הטופס</DialogTitle>
          </DialogHeader>
          <div className="mt-4 space-y-4">
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded">
              <p>אנא צייר את החתימה שלך בתיבה למטה:</p>
            </div>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-white">
              <canvas
                ref={canvasRef}
                width={500}
                height={200}
                className="border border-gray-200 rounded cursor-crosshair w-full"
                onMouseDown={startDrawing}
                onTouchStart={startDrawing}
                style={{ touchAction: 'none' }}
              />
            </div>

            <div className="flex gap-2 justify-between">
              <Button
                variant="outline"
                onClick={clearSignature}
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                נקה חתימה
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowSignaturePad(false)}
                >
                  ביטול
                </Button>
                <Button
                  onClick={handleSign}
                  disabled={!signatureData || signing}
                  className="flex items-center gap-2"
                >
                  {signing ? "חותם..." : "אשר חתימה"}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
