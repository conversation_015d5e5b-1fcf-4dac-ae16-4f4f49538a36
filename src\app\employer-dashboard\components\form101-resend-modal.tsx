import React from "react";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/rtl-components";
import { type Form101 } from "@prisma/client";
import { AlertCircle } from "lucide-react";

interface Form101ResendModalProps {
  isOpen: boolean;
  onClose: () => void;
  onResend: () => Promise<void>;
  onCancelAndResend: () => Promise<void>;
  form101Data: Form101;
  isLoading?: boolean;
  isActive?: boolean;
}

export function Form101ResendModal({
  isOpen,
  onClose,
  onResend,
  onCancelAndResend,
  form101Data,
  isLoading = false,
  isActive = false
}: Form101ResendModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>שליחת טופס 101 לחתימה</DialogTitle>
        </DialogHeader>
        
        <div className="py-4">
          {isActive ? (
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2 text-amber-600">
                <AlertCircle className="h-5 w-5" />
                <p className="font-medium">קיימת בקשת חתימה פעילה</p>
              </div>
              <p className="text-sm text-muted-foreground">
                טופס 101 כבר נשלח לחתימה וממתין לתגובת העובד. שליחה חוזרת תבטל את הבקשה הקודמת ותיצור בקשה חדשה.
              </p>
            </div>
          ) : (
            <p>האם לשלוח את טופס 101 לחתימה?</p>
          )}
        </div>
        
        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={isLoading}
          >
            ביטול
          </Button>
          
          <Button 
            onClick={isActive ? onCancelAndResend : onResend}
            disabled={isLoading}
          >
            {isLoading ? "שולח..." : isActive ? "בטל ושלח מחדש" : "שלח לחתימה"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 