---
description: Cursor Rules configuration file
---

## AI_GUIDE

The AI guide file provides comprehensive information about the project structure, technology stack, and coding conventions.

- **Project Tech Stack**
  - This project uses the T3 Stack: tRPC, Prisma, PostgreSQL, Next.js, Tailwind, shadcn/ui
  - See the `AI_GUIDE.md` file for detailed information

- **File Structure and Conventions**
  - Follow the patterns and conventions described in the `AI_GUIDE.md` file
  - Use the specified folder structure for different types of components and functionality

- **Code Standards**
  - Group related hooks in dedicated files
  - Use proper loading states for data-dependent components
  - Implement prefetching and query invalidation patterns
  - Follow RTL (Right-to-Left) conventions for Hebrew content 