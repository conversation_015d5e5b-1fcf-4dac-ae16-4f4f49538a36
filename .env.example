# When adding additional environment variables, the schema in /src/env.js
# should be updated accordingly.

# Next Auth
# Secret used to sign NextAuth tokens
AUTH_SECRET=YOUR_AUTH_SECRET

# Next Auth Discord Provider
# OAuth client ID
AUTH_DISCORD_ID=
# OAuth client secret
AUTH_DISCORD_SECRET=

# Prisma
# Database connection string (pooled)
DATABASE_URL=**********************************************************************************

# For uses requiring a connection without pgbouncer
DATABASE_URL_UNPOOLED=************************************************************************************

# Parameters for constructing your own connection string
PGHOST=ep-holy-grass-a2x43t7c-pooler.eu-central-1.aws.neon.tech
PGHOST_UNPOOLED=ep-holy-grass-a2x43t7c.eu-central-1.aws.neon.tech
PGUSER=neondb_owner
PGDATABASE=neondb
PGPASSWORD=npg_aWD3mi8dGxzE


POSTGRES_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_URL_NON_POOLING=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
POSTGRES_USER=neondb_owner
POSTGRES_HOST=ep-holy-grass-a2x43t7c-pooler.eu-central-1.aws.neon.tech
POSTGRES_PASSWORD=npg_aWD3mi8dGxzE
POSTGRES_DATABASE=neondb
POSTGRES_URL_NO_SSL=postgres://neondb_owner:<EMAIL>/neondb
POSTGRES_PRISMA_URL=postgres://neondb_owner:<EMAIL>/neondb?connect_timeout=15&sslmode=require

OPENROUTER_API_KEY=***********************************************************************************************************************************************************************
anthropic
ANTHROPIC_API_KEY=************************************************************************************************************


AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=jsXv7/ggsmHo6zTA/soSSkVY1oRqZZOpPUDVC60W
AWS_S3_BUCKET_NAME=construction-internal-development
AWS_REGION=eu-central-1
SALARY_SYSTEM_USER_ID=
SALARY_SYSTEM_API_PASSWORD=
PGHOST=YOUR_DB_HOST
PGHOST_UNPOOLED=YOUR_DB_HOST_UNPOOLED
PGUSER=YOUR_DB_USER
PGDATABASE=YOUR_DB_NAME
PGPASSWORD=YOUR_DB_PASSWORD

POSTGRES_URL=**********************************************************************************
POSTGRES_URL_NON_POOLING=**********************************************************************************
POSTGRES_USER=YOUR_DB_USER
POSTGRES_HOST=YOUR_DB_HOST
POSTGRES_PASSWORD=YOUR_DB_PASSWORD
POSTGRES_DATABASE=YOUR_DB_NAME
POSTGRES_URL_NO_SSL=******************************************************************
POSTGRES_PRISMA_URL=*****************************************************************************************************

# API key for OpenRouter
OPENROUTER_API_KEY=YOUR_OPENROUTER_API_KEY
# API key for Anthropic
ANTHROPIC_API_KEY=YOUR_ANTHROPIC_API_KEY

# Amazon S3 credentials
AWS_ACCESS_KEY_ID=YOUR_AWS_ACCESS_KEY_ID
AWS_SECRET_ACCESS_KEY=YOUR_AWS_SECRET_ACCESS_KEY
AWS_S3_BUCKET_NAME=YOUR_S3_BUCKET_NAME
AWS_REGION=YOUR_AWS_REGION
