import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AssociationsTable } from "./associations-table";
import type { Association, AssociationType } from "../hooks";

export interface AssociationsTabsProps {
  associations: Association[];
  isLoading: boolean;
  page: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  employerId: string;
  onRefresh: () => void;
  currentType: AssociationType | "all";
  onChangeType: (type: AssociationType | "all") => void;
}

export function AssociationsTabs({
  associations,
  isLoading,
  page,
  totalPages,
  onPageChange,
  employerId,
  onRefresh,
  currentType,
  onChangeType,
}: AssociationsTabsProps) {
  // Filter associations based on type for each tab
  const filterByType = (type: AssociationType | "all") => {
    if (type === "all") return associations;
    return associations.filter((a) => a.associationType === type);
  };

  const handleTabChange = (value: string) => {
    onChangeType(value as AssociationType | "all");
  };

  return (
    <Tabs defaultValue={currentType} onValueChange={handleTabChange} className="w-full">
      <TabsList className="grid grid-cols-5 mb-4">
        <TabsTrigger value="all">הכל</TabsTrigger>
        <TabsTrigger value="DEPARTMENT">מחלקות</TabsTrigger>
        <TabsTrigger value="ROLE">תפקידים</TabsTrigger>
        <TabsTrigger value="SALARY_TEMPLATE">תבניות שכר</TabsTrigger>
        <TabsTrigger value="SALARY_AGREEMENT">הסכמי שכר</TabsTrigger>
      </TabsList>
      
      <TabsContent value="all">
        <AssociationsTable
          associations={associations}
          isLoading={isLoading}
          page={page}
          totalPages={totalPages}
          onPageChange={onPageChange}
          employerId={employerId}
          onRefresh={onRefresh}
        />
      </TabsContent>
      
      <TabsContent value="DEPARTMENT">
        <AssociationsTable
          associations={filterByType("DEPARTMENT")}
          isLoading={isLoading}
          page={page}
          totalPages={totalPages}
          onPageChange={onPageChange}
          employerId={employerId}
          onRefresh={onRefresh}
        />
      </TabsContent>
      
      <TabsContent value="ROLE">
        <AssociationsTable
          associations={filterByType("ROLE")}
          isLoading={isLoading}
          page={page}
          totalPages={totalPages}
          onPageChange={onPageChange}
          employerId={employerId}
          onRefresh={onRefresh}
        />
      </TabsContent>
      
      <TabsContent value="SALARY_TEMPLATE">
        <AssociationsTable
          associations={filterByType("SALARY_TEMPLATE")}
          isLoading={isLoading}
          page={page}
          totalPages={totalPages}
          onPageChange={onPageChange}
          employerId={employerId}
          onRefresh={onRefresh}
        />
      </TabsContent>
      
      <TabsContent value="SALARY_AGREEMENT">
        <AssociationsTable
          associations={filterByType("SALARY_AGREEMENT")}
          isLoading={isLoading}
          page={page}
          totalPages={totalPages}
          onPageChange={onPageChange}
          employerId={employerId}
          onRefresh={onRefresh}
        />
      </TabsContent>
    </Tabs>
  );
} 