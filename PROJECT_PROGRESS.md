# תיעוד התקדמות פרויקט - מערכת ניהול שכר

## סקירה כללית
פרויקט מערכת ניהול שכר מבוסס T3 Stack עם הגרסאות הבאות:
- **Next.js**: 15.3.2
- **React**: 19.1.0
- **TypeScript**: 5.8.3
- **Prisma**: 6.8.2
- **tRPC**: 11.0.0-rc.712
- **Tailwind CSS**: 3.4.1

## מבנה הפרויקט
```
salary-t3/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (main)/            # Layout ראשי עם ניווט
│   │   │   ├── associations/  # מסך שיוכים
│   │   │   └── attendance-agreements/ # מסך הסכמי נוכחות (בפיתוח)
│   ├── components/            # קומפוננטות משותפות
│   ├── server/               # Backend - tRPC routers
│   └── lib/                  # Utilities ופונקציות עזר
├── prisma/                   # סכמת DB
└── public/                   # קבצים סטטיים
```

## התקדמות לפי מסכים

### 1. מסך שיוכים - ✅ הושלם
**תיאור**: מסך לניהול שיוכי עובדים לישויות שונות (חברות, מחלקות, תפקידים, אתרים)

#### פונקציונליות שהושלמה:
- **תצוגת שיוכים**:
  - טבלה עם כל השיוכים הפעילים
  - הצגה בטאבים לפי סוג שיוך
  - מיון לפי כל עמודה
  - סינון לפי סוג שיוך

- **הוספת שיוך חדש**:
  - טופס מודאלי עם בחירת עובד וישות
  - אוטוקומפליט מתקדם עם חיפוש לפי שם/ת.ז./דרכון
  - תאריכי תחילה וסיום
  - הערות אופציונליות

- **עריכת שיוך קיים**:
  - עדכון כל השדות
  - שמירת היסטוריה

- **מחיקת שיוך**:
  - אישור לפני מחיקה
  - מחיקה רכה (soft delete)

- **בדיקות תקינות**:
  - בדיקת שיוכים חופפים
  - בדיקת שיוכים פתוחים ללא תאריך סיום
  - הצגת התראות בזמן אמת

#### קבצים עיקריים:
```typescript
// src/app/(main)/associations/
├── page.tsx                    // דף ראשי
├── components/
│   ├── AssociationTable.tsx   // טבלת שיוכים
│   ├── AssociationFormModal.tsx // טופס הוספה/עריכה
│   └── ValidationDialog.tsx    // דיאלוג בדיקות
├── hooks.ts                    // React hooks
├── types.ts                    // TypeScript types
└── .cursorrules               // תיעוד למסך
```

#### API Endpoints (tRPC):
```typescript
// src/server/api/routers/association.ts
- getAll()              // קבלת כל השיוכים
- create()              // יצירת שיוך חדש
- update()              // עדכון שיוך
- delete()              // מחיקת שיוך
- validateAssociations() // בדיקת תקינות
```

### 2. מסך הסכמי נוכחות - 🚧 בפיתוח
**תיאור**: מסך מקיף לניהול הסכמי נוכחות, משמרות, חוקי שעות נוספות והפסקות

#### מבנה המסך (5 טאבים):
1. **הגדרת הסכמים** - ניהול הסכמי נוכחות
2. **הגדרת משמרות** - ניהול סוגי משמרות
3. **חוקי שעות נוספות** - הגדרת חוקים לחישוב שעות נוספות
4. **חוקי הפסקות** - הגדרת חוקי הפסקות
5. **מיקומים וסוגי תנועות** - ניהול מיקומים וסוגי דיווח

#### קבצים שנוצרו:
```typescript
// src/app/(main)/attendance-agreements/
├── page.tsx                    // דף ראשי עם טאבים
├── layout.tsx                  // Layout בסיסי
├── types.ts                    // הגדרות TypeScript מלאות
├── hooks.ts                    // React hooks לכל הישויות
└── components/
    └── AgreementDefinitionTab.tsx // טאב הגדרת הסכמים (בפיתוח)
```

#### Types שהוגדרו:
- `AttendanceAgreement` - הסכם נוכחות
- `Shift` - משמרת
- `OvertimeRule` - חוק שעות נוספות
- `BreakRule` - חוק הפסקות
- `Location` - מיקום
- `MovementType` - סוג תנועה

#### Hooks שנוצרו:
- CRUD מלא לכל ישות
- פונקציות עזר לחישובים
- ולידציות

## החלטות טכניות

### 1. ארכיטקטורה
- **T3 Stack**: Next.js 15 עם App Router, tRPC, Prisma
- **UI Components**: shadcn/ui + Radix UI
- **State Management**: React Query (דרך tRPC)
- **Styling**: Tailwind CSS
- **Authentication**: NextAuth.js v5 (Auth.js)

### 2. מבנה קוד
- **Separation of Concerns**: הפרדה ברורה בין UI, לוגיקה ו-API
- **Type Safety**: שימוש ב-TypeScript + Zod לכל הפרויקט
- **Reusable Components**: קומפוננטות גנריות ב-`src/components`
- **Custom Hooks**: לוגיקה עסקית ב-hooks נפרדים

### 3. Best Practices
- **Error Handling**: טיפול בשגיאות בכל רמה
- **Loading States**: מצבי טעינה ברורים
- **Optimistic Updates**: עדכונים אופטימיסטיים ב-mutations
- **Form Validation**: Zod + React Hook Form
- **Accessibility**: ARIA labels ותמיכה במקלדת

## בעיות שנפתרו

### 1. לולאה אינסופית ב-useEffect
**בעיה**: useEffect במודאל עריכה גרם ללולאה אינסופית
**פתרון**: שימוש ב-useRef למעקב אחר שינויים + תיקון dependencies

### 2. אזהרות aria-describedby
**בעיה**: חסר DialogDescription בדיאלוגים
**פתרון**: הוספת DialogDescription לכל Dialog

### 3. שגיאת Prisma
**בעיה**: שימוש ב-`isActive: true` במקום `status: "ACTIVE"`
**פתרון**: עדכון השאילתה לשימוש בשדה הנכון

### 4. TypeScript Errors
**בעיה**: טיפוסים לא מתאימים בפונקציות
**פתרון**: הגדרת טיפוסים מדויקים והתאמה ל-API

## משימות בהמשך

### מיידי (Priority 1)
- [ ] השלמת מסך הסכמי נוכחות
  - [ ] טאב הגדרת הסכמים
  - [ ] טאב הגדרת משמרות
  - [ ] טאב חוקי שעות נוספות
  - [ ] טאב חוקי הפסקות
  - [ ] טאב מיקומים וסוגי תנועות

### בינוני (Priority 2)
- [ ] מסך דיווחי נוכחות
- [ ] מסך חישוב שכר
- [ ] מסך דוחות

### ארוך טווח (Priority 3)
- [ ] אינטגרציה עם מערכות חיצוניות
- [ ] מערכת הרשאות מתקדמת
- [ ] ממשק API חיצוני

## הערות למפתחים

### קונבנציות קוד
1. **Naming**:
   - Components: PascalCase
   - Functions/Hooks: camelCase
   - Types/Interfaces: PascalCase
   - Files: kebab-case

2. **Structure**:
   - קומפוננטה אחת לקובץ
   - Types בקובץ נפרד
   - Hooks בקובץ נפרד
   - Utils בתיקיית lib

3. **Comments**:
   - תיעוד בעברית לפונקציות עסקיות
   - JSDoc לפונקציות מורכבות
   - TODO comments למשימות עתידיות

### טיפים לפיתוח
1. השתמש ב-`.cursorrules` בכל מסך לתיעוד
2. בדוק תמיד את ה-TypeScript errors לפני commit
3. הרץ את הבדיקות לפני push
4. עדכן את התיעוד הזה בכל שינוי משמעותי

## סביבת פיתוח

### דרישות
- Node.js 18+
- PostgreSQL
- pnpm/npm/yarn

### התקנה
```bash
# Clone the repo
git clone [repo-url]

# Install dependencies
pnpm install

# Setup database
pnpm db:push

# Run development server
pnpm dev
```

### משתני סביבה
```env
DATABASE_URL="postgresql://..."
NEXTAUTH_SECRET="..."
NEXTAUTH_URL="http://localhost:3000"
```

## עדכונים אחרונים
- **2024-01-10**: השלמת מסך שיוכים עם כל הפונקציונליות
- **2024-01-10**: התחלת פיתוח מסך הסכמי נוכחות
- **2024-01-10**: תיקון בעיות טכניות ושיפורי UX
- **2024-01-10**: הוספת מודלים ל-Prisma schema עבור הסכמי נוכחות
- **2024-01-10**: יצירת tRPC router להסכמי נוכחות
- **2024-01-10**: יצירת קומפוננטות UI לטאב הגדרת הסכמים
- **2025-05-26**: עדכון seed script עם:
  - נתוני הסכמי נוכחות מלאים (הסכמים, משמרות, חוקי שעות נוספות, הפסקות, מיקומים)
  - תנועות נוכחות ל-90 יום אחורה
  - מקרי קצה: עובדי משמרת לילה, עובדים במשמרות מפוצלות, עובדים חלקיים, מנהלים בשעות גמישות
  - תלושי שכר למרץ ואפריל 2025 (משולמים/מאושרים) ומאי 2025 (מחושבים אך לא מאושרים)
  - 8 עובדים עם מקרי בדיקה מגוונים
  - 18 התראות compliance שונות
- **2025-05-26**: יצירת תשתית מלאה למסך הסכמי נוכחות:
  - יצירת types.ts עם כל הטיפוסים הנדרשים
  - יצירת hooks.ts עם כל ה-hooks לכל הישויות
  - יצירת 6 routers חדשים:
    - shift.ts - ניהול משמרות
    - overtime-rule.ts - חוקי שעות נוספות
    - break-rule.ts - חוקי הפסקות
    - location.ts - ניהול מיקומים
    - movement-type.ts - סוגי תנועות
    - employee-agreement.ts - שיוכי עובדים להסכמים
  - הוספת פונקציית calculateOvertime לחישוב שעות נוספות
  - הוספת validateOverlap לבדיקת חפיפות במשמרות
- **2025-05-27**: התחלת פיתוח מסך מרכיבי ניכוי:
  - יצירת types.ts עם כל הטיפוסים והקבועים
  - יצירת hooks.ts עם hooks מקיפים כולל:
    - CRUD operations
    - דוחות ואנליטיקה
    - ולידציות
    - פעולות bulk
    - ייצוא/ייבוא
  - יצירת deduction-component.ts router עם:
    - כל פעולות CRUD
    - שכפול רכיבים
    - ולידציה מתקדמת
    - סטטיסטיקות שימוש
    - דוח שינויים
    - עדכון מרובה
    - ייצוא/ייבוא
  - הוספת DeductionComponent model ל-Prisma schema
  - יצירת page.tsx - דף ראשי עם חיפוש ופילטרים
  - יצירת DeductionComponentsTable.tsx - טבלה מתקדמת עם:
    - מיון לפי עמודות
    - בחירה מרובה
    - פעולות לכל רכיב (עריכה, שכפול, מחיקה)
    - תצוגה ברורה של כל השדות
  - העברת המסכים למיקום הנכון ב-employer-dashboard
  - עדכון navigation.tsx עם המסכים החדשים
  - יצירת DeductionFormModal.tsx - טופס מלא להוספה/עריכה
  - יצירת ChangeReportModal.tsx - דוח שינויים עם ייצוא לאקסל
  - עדכון seed script עם 11 רכיבי ניכוי לדוגמה
  - תיקון imports ב-hooks.ts (שימוש ב-sonner במקום use-toast)
  - תיקון שגיאת תחביר ב-types.ts (הוספת סוגר סוגרת ל-DEDUCTION_GROUP_LABELS)
  - פתרון בעיות TypeScript ב-DeductionFormModal
  - הרצת prisma generate לעדכון types

---

*מסמך זה מתעדכן באופן שוטף עם התקדמות הפרויקט* 