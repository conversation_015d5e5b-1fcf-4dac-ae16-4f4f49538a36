"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Download, AlertCircle } from "lucide-react";
import { toast } from "sonner";
import { type Form101 } from "@prisma/client";
import { useGenerateForm101Preview } from "../hooks";

interface Form101PreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  form101Data: Form101;
  employeeName: string;
}

export function Form101PreviewModal({ isOpen, onClose, form101Data, employeeName }: Form101PreviewModalProps) {
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [fileName, setFileName] = useState<string>("");
  const { generatePreview, isGenerating, error } = useGenerateForm101Preview();

  useEffect(() => {
    if (isOpen && form101Data.id) {
      loadPreview();
    }

    // Cleanup URL when modal closes
    return () => {
      if (pdfUrl) {
        URL.revokeObjectURL(pdfUrl);
        setPdfUrl(null);
      }
    };
  }, [isOpen, form101Data.id]);

  const loadPreview = async () => {
    try {
      const result = await generatePreview({ form101Id: form101Data.id });

      // Convert base64 to blob and create URL
      const binaryString = atob(result.pdfData);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const blob = new Blob([bytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      setPdfUrl(url);
      setFileName(result.fileName);
    } catch (error) {
      console.error('Error loading PDF preview:', error);
      toast.error("שגיאה בטעינת תצוגה מקדימה");
    }
  };

  const handleDownload = () => {
    if (pdfUrl && fileName) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success("הקובץ הורד בהצלחה");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle>תצוגה מקדימה - טופס 101 - {employeeName}</DialogTitle>
            {pdfUrl && (
              <Button onClick={handleDownload} variant="outline" size="sm">
                <Download className="h-4 w-4 ml-2" />
                הורד PDF
              </Button>
            )}
          </div>
        </DialogHeader>

        <div className="flex-1 min-h-[600px]">
          {isGenerating ? (
            <div className="space-y-4">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-[500px] w-full" />
            </div>
          ) : error ? (
            <div className="flex flex-col items-center justify-center h-[500px] text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold mb-2">שגיאה בטעינת התצוגה המקדימה</h3>
              <p className="text-gray-600 mb-4">לא ניתן לטעון את הטופס כרגע</p>
              <Button onClick={loadPreview} variant="outline">
                נסה שוב
              </Button>
            </div>
          ) : pdfUrl ? (
            <iframe
              src={pdfUrl}
              className="w-full h-[600px] border rounded-lg"
              title="תצוגה מקדימה של טופס 101"
            />
          ) : (
            <div className="flex items-center justify-center h-[500px]">
              <p className="text-gray-500">טוען תצוגה מקדימה...</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}