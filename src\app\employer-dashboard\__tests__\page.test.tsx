import { describe, it, expect, vi } from 'vitest';
import { render } from '@testing-library/react';
import EmployerDashboardPage from '../page';

vi.mock('next-auth/react', () => ({
  useSession: () => ({ data: { user: { employerId: 'emp1' } }, status: 'authenticated' }),
}));

vi.mock('../hooks', () => ({
  useEmployerUsers: vi.fn(() => ({ users: [], isLoading: false, createUser: vi.fn() })),
  useEmployerAuditLogs: vi.fn(() => ({ logs: [], isLoading: false })),
}));

// eslint-disable-next-line @typescript-eslint/no-var-requires
const hooks = { useEmployerUsers: vi.fn(), useEmployerAuditLogs: vi.fn() };

describe('EmployerDashboardPage', () => {
  it.skip('uses employer ID from session', () => {
    render(<EmployerDashboardPage />);
    expect(hooks.useEmployerUsers).toHaveBeenCalledWith(1, 'emp1');
  });
});
