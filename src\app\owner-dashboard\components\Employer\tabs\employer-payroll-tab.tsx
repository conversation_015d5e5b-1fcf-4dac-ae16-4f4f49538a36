"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";
import { Calendar, Download } from "lucide-react";
import { type EmployerTabProps } from "../types";

export function EmployerPayrollTab({ employer }: EmployerTabProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>תלושי שכר</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Calendar className="ml-2 h-4 w-4" />
            בחירת חודש
          </Button>
          <Button size="sm">
            <Download className="ml-2 h-4 w-4" />
            הפקת תלושים
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground text-center py-8">
          אין תלושים להצגה עבור התקופה הנוכחית
        </p>
      </CardContent>
    </Card>
  );
} 