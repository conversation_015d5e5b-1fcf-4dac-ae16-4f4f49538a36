import { NextRequest, NextResponse } from "next/server";
import { db } from "@/server/db";

// Export route configuration
export const dynamic = 'force-dynamic';

// Define a simplified handler for signature request
export async function GET(req: NextRequest, context: any) {
  const id = context.params?.id;
  
  if (!id) {
    return new NextResponse(
      JSON.stringify({ success: false, error: "Signature ID is required" }),
      { status: 400, headers: { "Content-Type": "application/json" } }
    );
  }

  try {
    // Get signature request from database
    const signatureRequest = await db.signatureRequest.findUnique({
      where: { id },
      include: {
        form101: {
          include: {
            employee: {
              include: {
                tenant: true
              }
            }
          }
        }
      }
    });

    if (!signatureRequest) {
      return new NextResponse(
        JSON.stringify({ success: false, error: "Signature request not found" }),
        { status: 404, headers: { "Content-Type": "application/json" } }
      );
    }

    // Extract data safely with type assertions
    const form101 = signatureRequest.form101 as any;
    const employee = form101.employee as any;
    const tenant = employee.tenant as any;

    // Check if signature request is expired
    const now = new Date();
    const isExpired = now > signatureRequest.expiresAt;

    const responseData = {
      form101: {
        id: form101.id,
        taxYear: form101.taxYear,
        maritalStatus: form101.maritalStatus,
        spouseWorks: form101.spouseWorks,
        childrenCount: form101.childrenCount,
        isMainEmployer: form101.isMainEmployer,
        hasAdditionalIncome: form101.hasAdditionalIncome,
      },
      employee: {
        firstName: employee.firstName || "",
        lastName: employee.lastName || "",
        nationalId: employee.nationalId || "",
        email: employee.contact?.email || "",
        phone: employee.contact?.phone || "",
      },
      employer: {
        name: tenant.name || "",
        businessNumber: tenant.identifier || "",
        address: tenant.address ? JSON.stringify(tenant.address) : "",
        phone: tenant.contact?.phone || "",
      },
      expiresAt: signatureRequest.expiresAt,
      status: isExpired ? 'expired' : signatureRequest.status,
      pdfPreviewUrl: `/api/form101/${form101.id}/pdf`
    };

    return new NextResponse(
      JSON.stringify({ success: true, data: responseData }),
      { status: 200, headers: { "Content-Type": "application/json" } }
    );

  } catch (error) {
    console.error("Error fetching signature data:", error);
    return new NextResponse(
      JSON.stringify({ success: false, error: "Internal server error" }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    );
  }
}
