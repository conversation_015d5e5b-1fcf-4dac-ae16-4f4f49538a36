// ============================================
// Attendance Agreement Types
// ============================================

export interface AttendanceAgreement {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  status: "ACTIVE" | "INACTIVE" | "DRAFT";
  workDaysPerWeek: number;
  hoursPerDay: number;
  monthlyHours: number;
  overtimeThreshold: number;
  nightShiftStart?: string | null;
  nightShiftEnd?: string | null;
  weekendDays?: number[];
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface Shift {
  id: string;
  agreementId: string;
  code: string;
  name: string;
  description?: string | null;
  startTime: string;
  endTime: string;
  breakMinutes: number;
  isNightShift: boolean;
  isFlexible: boolean;
  color?: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface OvertimeRule {
  id: string;
  agreementId: string;
  name: string;
  description?: string | null;
  fromHour: number;
  toHour?: number | null;
  rate: number;
  dayType: "WEEKDAY" | "WEEKEND" | "HOLIDAY";
  priority: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface BreakRule {
  id: string;
  agreementId: string;
  name: string;
  description?: string | null;
  minWorkHours: number;
  breakDuration: number; // in minutes
  isPaid: boolean;
  isMandatory: boolean;
  canBeSplit: boolean;
  minSplitDuration?: number | null; // in minutes
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface Location {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  address?: string | null;
  latitude?: number | null;
  longitude?: number | null;
  radius?: number | null; // in meters for geofencing
  timezone: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface MovementType {
  id: string;
  code: string;
  name: string;
  description?: string | null;
  category: "CHECK_IN" | "CHECK_OUT" | "BREAK_START" | "BREAK_END" | "OTHER";
  isDefault: boolean;
  requiresApproval: boolean;
  color?: string | null;
  icon?: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
}

export interface EmployeeAgreement {
  id: string;
  employeeId: string;
  agreementId: string;
  startDate: Date;
  endDate?: Date | null;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
  // Relations
  employee?: {
    id: string;
    firstName: string;
    lastName: string;
    nationalId: string;
  };
  agreement?: AttendanceAgreement;
}

export interface Movement {
  id: string;
  employeeId: string;
  typeId: string;
  locationId?: string | null;
  timestamp: Date;
  source: "MANUAL" | "MOBILE_APP" | "WEB_APP" | "BIOMETRIC" | "CARD_READER" | "API";
  ipAddress?: string | null;
  deviceId?: string | null;
  notes?: string | null;
  isApproved: boolean;
  approvedBy?: string | null;
  approvedAt?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
  // Relations
  type?: MovementType;
  location?: Location;
}

// ============================================
// Form Types
// ============================================

export interface AttendanceAgreementFormData {
  code: string;
  name: string;
  description?: string;
  status: "ACTIVE" | "INACTIVE" | "DRAFT";
  workDaysPerWeek: number;
  hoursPerDay: number;
  monthlyHours: number;
  overtimeThreshold: number;
  nightShiftStart?: string;
  nightShiftEnd?: string;
  weekendDays?: number[];
}

export interface ShiftFormData {
  agreementId: string;
  code: string;
  name: string;
  description?: string;
  startTime: string;
  endTime: string;
  breakMinutes: number;
  isNightShift: boolean;
  isFlexible: boolean;
  color?: string;
}

export interface OvertimeRuleFormData {
  agreementId: string;
  name: string;
  description?: string;
  fromHour: number;
  toHour?: number;
  rate: number;
  dayType: "WEEKDAY" | "WEEKEND" | "HOLIDAY";
  priority: number;
}

export interface BreakRuleFormData {
  agreementId: string;
  name: string;
  description?: string;
  minWorkHours: number;
  breakDuration: number;
  isPaid: boolean;
  isMandatory: boolean;
  canBeSplit: boolean;
  minSplitDuration?: number;
}

export interface LocationFormData {
  code: string;
  name: string;
  description?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  timezone: string;
  isActive: boolean;
}

export interface MovementTypeFormData {
  code: string;
  name: string;
  description?: string;
  category: "CHECK_IN" | "CHECK_OUT" | "BREAK_START" | "BREAK_END" | "OTHER";
  isDefault: boolean;
  requiresApproval: boolean;
  color?: string;
  icon?: string;
}

// ============================================
// Utility Types
// ============================================

export interface OvertimeCalculation {
  regularHours: number;
  overtimeHours: {
    rate: number;
    hours: number;
    amount: number;
  }[];
  totalOvertimeHours: number;
  totalOvertimeAmount: number;
}

export interface ShiftValidation {
  isValid: boolean;
  conflicts?: {
    shiftId: string;
    shiftName: string;
    startTime: string;
    endTime: string;
  }[];
  warnings?: string[];
} 