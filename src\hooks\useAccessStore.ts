import type { Role } from "@prisma/client";
import { create } from "zustand";

export interface AccessState {
	role?: Role;
	extra?: Record<string, unknown>;
	setRole: (role: Role, extra?: Record<string, unknown>) => void;
	clearRole: () => void;
}

export const useAccessStore = create<AccessState>((set) => ({
	role: undefined,
	extra: undefined,
	setRole: (role, extra) => set({ role, extra }),
	clearRole: () => set({ role: undefined, extra: undefined }),
}));
