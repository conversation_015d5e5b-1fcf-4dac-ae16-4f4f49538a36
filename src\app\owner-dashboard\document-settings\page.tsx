"use client";

import { useEffect, useState } from "react";
import { api } from "@/trpc/react";
import { DOCUMENT_CATEGORY_CONFIG } from "@/app/employer-dashboard/cache-config";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/rtl-components";

export default function DocumentSettingsPage() {
  const { data, isLoading } = api.documentSettings.getSettings.useQuery();
  const utils = api.useContext();
  const updateMutation = api.documentSettings.updateSettings.useMutation({
    onSuccess: () => utils.documentSettings.getSettings.invalidate(),
  });
  const [selected, setSelected] = useState<string[]>([]);

  useEffect(() => {
    if (data?.allowedCategories) {
      const categories = Array.isArray(data.allowedCategories) 
        ? data.allowedCategories.filter((item): item is string => typeof item === 'string')
        : [];
      setSelected(categories);
    }
  }, [data]);

  const toggle = (cat: string) => {
    setSelected((prev) =>
      prev.includes(cat) ? prev.filter((c) => c !== cat) : [...prev, cat]
    );
  };

  const save = () => {
    updateMutation.mutate({ allowedCategories: selected });
  };

  if (isLoading) return <div>טוען...</div>;

  return (
    <div className="space-y-4 p-4">
      <h1 className="text-2xl font-bold">הגדרות מסמכים</h1>
      {Object.entries(DOCUMENT_CATEGORY_CONFIG).map(([key, cfg]) => (
        <div key={key} className="flex items-center space-x-2">
          <Checkbox
            id={key}
            checked={selected.includes(key) || key === "form-101"}
            disabled={key === "form-101"}
            onCheckedChange={() => toggle(key)}
          />
          <label htmlFor={key}>{cfg.displayName}</label>
        </div>
      ))}
      <Button onClick={save} disabled={updateMutation.isPending}>שמירה</Button>
    </div>
  );
}
