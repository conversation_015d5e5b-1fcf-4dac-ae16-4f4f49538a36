import { z } from "zod";
import { isValidIsraeliBusinessId } from "@/utils/validation";

// Define address and contact schemas for better typing
export const addressSchema = z.object({
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  country: z.string().optional(),
});

export const contactSchema = z.object({
  name: z.string().optional(),
  email: z.string().email({ message: "כתובת אימייל לא תקינה" }).optional(),
  phone: z.string().optional(),
  position: z.string().optional(),
});

// Base schema for employer data
export const employerSchema = z.object({
  id: z.string(),
  name: z.string().min(1, { message: "שם המעסיק הוא שדה חובה" }),
  identifier: z.string()
    .min(1, { message: "מזהה הוא שדה חובה" })
    .length(9, { message: "המזהה חייב להכיל בדיוק 9 ספרות" })
    .regex(/^\d+$/, { message: "המזהה חייב לכלול רק ספרות" })
    .refine(isValidIsraeliBusinessId, { message: "מזהה עסקי לא תקין" }),
  employeeCount: z.number().int().nonnegative(),
  status: z.enum(["active", "inactive"]),
  lastPayslip: z.string(),
  registrationDate: z.string().optional(),
  accountManager: z.string().optional(),
  payrollDay: z.number().int().positive().max(31).optional(),
});

// Input schema for creating a new employer
export const createEmployerSchema = z.object({
  name: z.string().min(1, { message: "שם המעסיק הוא שדה חובה" }),
  identifier: z.string()
    .min(1, { message: "מזהה הוא שדה חובה" })
    .length(9, { message: "המזהה חייב להכיל בדיוק 9 ספרות" })
    .regex(/^\d+$/, { message: "המזהה חייב לכלול רק ספרות" })
    .refine(isValidIsraeliBusinessId, { message: "מזהה עסקי לא תקין" }),
  status: z.enum(["active", "inactive"]).default("active"),
  taxId: z.string().optional(),
  registrationDate: z.string().optional(),
  accountManager: z.string().optional(),
  payrollDay: z.number().int().positive().max(31).optional(),
  profilePictureUrl: z.string().optional(),
  address: addressSchema.optional(),
  contact: contactSchema.optional(),
});

// Input schema for updating an employer
export const updateEmployerSchema = z.object({
  id: z.string().min(1, { message: "מזהה הוא שדה חובה" }),
  name: z.string().min(1, { message: "שם המעסיק הוא שדה חובה" }).optional(),
  identifier: z.string()
    .min(1, { message: "מזהה הוא שדה חובה" })
    .length(9, { message: "המזהה חייב להכיל בדיוק 9 ספרות" })
    .regex(/^\d+$/, { message: "המזהה חייב לכלול רק ספרות" })
    .refine(isValidIsraeliBusinessId, { message: "מזהה עסקי לא תקין" })
    .optional(),
  taxId: z.string().optional(),
  status: z.enum(["active", "inactive"]).optional(),
  registrationDate: z.string().optional(),
  accountManager: z.string().optional(),
  payrollDay: z.number().int().positive().max(31).optional(),
  profilePictureUrl: z.string().optional(),
  address: addressSchema.optional(),
  contact: contactSchema.optional(),
});

// Input schema for fetching employers with pagination
export const getEmployersSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  status: z.enum(["active", "inactive", "all"]).optional().default("all"),
  search: z.string().optional(),
});

// Types derived from schemas
export type Employer = z.infer<typeof employerSchema>;
export type CreateEmployerInput = z.infer<typeof createEmployerSchema>;
export type UpdateEmployerInput = z.infer<typeof updateEmployerSchema>;
export type GetEmployersInput = z.infer<typeof getEmployersSchema>;
export type Address = z.infer<typeof addressSchema>;
export type Contact = z.infer<typeof contactSchema>;

// Schema for employer document upload
export const uploadEmployerDocumentSchema = z.object({
  employerId: z.string().min(1, { message: "מזהה המעסיק הוא שדה חובה" }),
  title: z.string().optional(),
  category: z.string().optional(),
  file: z.custom<File>((val) => val instanceof File || val === null, {
    message: "נדרש קובץ תקין",
  }).nullable(),
});

// Schema for profile picture upload
export const uploadProfilePictureSchema = z.object({
  employerId: z.string().min(1, { message: "מזהה המעסיק הוא שדה חובה" }),
  file: z.custom<File>((val) => val instanceof File || val === null, {
    message: "נדרש קובץ תקין",
  }).nullable(),
}); 