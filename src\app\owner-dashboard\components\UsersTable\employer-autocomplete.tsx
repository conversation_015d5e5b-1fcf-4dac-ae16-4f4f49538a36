"use client";

import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { api } from "@/trpc/react";
import { Check, ChevronsUpDown } from "lucide-react";
import { useSession } from "next-auth/react";
import * as React from "react";

type EmployerAutocompleteProps = {
	selectedEmployerId: string;
	onSelect: (employerId: string) => void;
	disabled?: boolean;
};

export function EmployerAutocomplete({
	selectedEmployerId,
	onSelect,
	disabled = false,
}: EmployerAutocompleteProps) {
	const [open, setOpen] = React.useState(false);
        const { data: session } = useSession();
        const employerId = session?.user?.employerId ?? "";

	const {
		data: employer,
		isLoading: isLoadingEmployers,
		error: employersError,
	} = api.employer.getById.useQuery(
		{ id: employerId },
		{
			staleTime: 5 * 60 * 1000,
			refetchOnWindowFocus: false,
			enabled: !!employerId,
		},
	);

	const employersData = employer
		? { employers: [employer] }
		: { employers: [] };

	// בחירת המעסיק הנוכחי
	const selectedEmployer = React.useMemo(() => {
		return employersData?.employers?.find(
			(employer) => employer.id === selectedEmployerId,
		);
	}, [employersData?.employers, selectedEmployerId]);

	// מציג סקלטון בזמן טעינה
	if (isLoadingEmployers) {
		return (
			<div className="h-10 w-full">
				<Skeleton className="h-10 w-full" />
			</div>
		);
	}

	// מציג שגיאה אם יש
	if (employersError) {
		return (
			<Button
				variant="outline"
				className="w-full bg-red-50 text-right text-red-800"
				disabled={true}
			>
				שגיאה בטעינת מעסיקים
			</Button>
		);
	}

	// אם אין מעסיקים, נציג הודעה מתאימה
	if (!employersData?.employers?.length) {
		return (
			<Button
				variant="outline"
				className="w-full bg-muted text-right text-muted-foreground"
				disabled={true}
			>
				אין מעסיקים זמינים
			</Button>
		);
	}

	// יש לנו מעסיקים תקינים - נציג אותם
	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					aria-expanded={open}
					className="mx-auto w-full justify-between text-right"
					disabled={disabled}
				>
					{selectedEmployer ? selectedEmployer.name : "בחר מעסיק..."}
					<ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-full p-0">
				<Command>
					<CommandEmpty>
						{isLoadingEmployers ? (
							<Skeleton className="h-10 w-full" />
						) : (
							"לא נמצאו מעסיקים"
						)}
					</CommandEmpty>
					<CommandGroup className="max-h-64 overflow-y-auto">
						{employersData?.employers?.map((employer) => (
							<CommandItem
								key={employer.id}
								value={employer.name}
								onSelect={() => {
									onSelect(employer.id);
									setOpen(false);
								}}
								className="flex flex-row-reverse"
							>
								<Check
									className={cn(
										"mr-2 h-4 w-4",
										selectedEmployerId === employer.id
											? "opacity-100"
											: "opacity-0",
									)}
								/>
								{employer.name}
							</CommandItem>
						))}
					</CommandGroup>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
