"use client";

import { useEffect, type ReactNode } from "react";
import OwnerNavbar from "@/app/owner-dashboard/components/layout/navbar";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
interface DashboardLayoutProps {
  children: ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const { data: session, status } = useSession();
  
  // Show loading state while checking authentication
  if (status === "loading") {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }
  
  // Move the redirect logic outside of useEffect to avoid conditional hooks
  if (status === "unauthenticated") {
    redirect("/login");
  }
  // אם הרול לא אדמיין תעביר למסך מעסיק
  if (session?.user?.role !== "admin") {
    redirect("/employer-dashboard");
  }
  // הבדיקה הוסרה - מטופלת ב-middleware
  
  return (
    <div className="min-h-screen flex flex-col" dir="rtl">
      <OwnerNavbar userName={session?.user?.name || "מנהל מערכת"} />
      <main className="flex-1 container mx-auto px-4 py-6">
        {children}
      </main>
      <footer className="border-t border-border py-4 px-6 text-center text-sm text-muted-foreground">
        <p>© {new Date().getFullYear()} SMARTCHI. כל הזכויות שמורות.</p>
      </footer>
    </div>
  );
}
