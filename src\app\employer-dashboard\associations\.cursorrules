---
description: Rules and guidelines for the Associations screen implementation
globs: src/app/employer-dashboard/associations/**/*
alwaysApply: true
---

# מסך שיוכים - Associations Screen

## **תיאור כללי**
מסך שיוכים מאפשר למנהל המערכת להגדיר, לערוך ולנהל את מערכת השיוכים והקשרים בין ישויות שונות במערכת השכר.

## **מבנה הקבצים**
- `page.tsx` - הדף הראשי של מסך השיוכים
- `hooks.ts` - כל ה-hooks לניהול הנתונים והלוגיקה
- `components/` - קומפוננטות המסך:
  - `associations-table.tsx` - טבלת השיוכים עם מיון ועימוד
  - `associations-tabs.tsx` - טאבים לסינון לפי סוג שיוך
  - `association-form-modal.tsx` - טו<PERSON><PERSON> הוספה/עריכה של שיוך
  - `association-history-modal.tsx` - מודל היסטוריית שינויים

## **סוגי שיוכים נתמכים**
1. **DEPARTMENT** - שיוך עובד למחלקה
2. **ROLE** - שיוך עובד לתפקיד
3. **SALARY_TEMPLATE** - שיוך עובד לתבנית שכר
4. **SALARY_AGREEMENT** - שיוך עובד להסכם שכר

## **פונקציונליות עיקרית**

### **הצגת נתונים**
- טבלה עם כל השיוכים הפעילים
- אפשרות מיון לפי כל עמודה (לחיצה על כותרת העמודה)
- עימוד לטבלאות גדולות
- סינון לפי סוג שיוך באמצעות טאבים

### **סינון וחיפוש**
- חיפוש טקסט חופשי
- סינון לפי סוג שיוך
- סינון לפי טווח תאריכים

### **פעולות**
- **הוספת שיוך** - יצירת שיוך חדש
- **עריכת שיוך** - עדכון שיוך קיים
- **מחיקת שיוך** - הסרת שיוך
- **בדיקות תקינות** - בדיקת חפיפות וקונפליקטים
- **דוח שינויים** - הפקת דוח על שינויים שבוצעו
- **היסטוריה** - צפייה בהיסטוריית שינויים לכל שיוך

## **ולידציות ובדיקות**

### **בדיקות בזמן הוספה/עריכה**
- תאריך סיום חייב להיות אחרי תאריך התחלה
- אזהרה על שיוכים חופפים (לא חוסמת שמירה)
- חובה לבחור עובד וישות לשיוך

### **בדיקות תקינות כלליות**
- זיהוי שיוכים חופפים מאותו סוג לאותו עובד
- זיהוי שיוכים פתוחים (ללא תאריך סיום) כאשר יש שיוך חדש יותר
- הצגת סיכום הבעיות שנמצאו

## **טיפוסי נתונים**

```typescript
type Association = {
  id: string;
  employeeId: string;
  employeeName: string;
  employeeIdentifier: string;
  associationType: AssociationType;
  associatedEntityId: string;
  associatedEntityName: string;
  startDate: string;
  endDate: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
};

type AssociationType = 
  | "DEPARTMENT" 
  | "ROLE" 
  | "SALARY_TEMPLATE" 
  | "SALARY_AGREEMENT";
```

## **הנחיות לפיתוח**

### **שימוש ב-Hooks**
- השתמש ב-hooks המוגדרים ב-`hooks.ts` לכל הפעולות
- אל תבצע קריאות API ישירות
- השתמש ב-`useAssociations` לטעינת נתונים
- השתמש ב-`useCreateAssociation`, `useUpdateAssociation`, `useDeleteAssociation` לפעולות CRUD

### **טיפול בשגיאות**
- הצג הודעות שגיאה ברורות למשתמש
- השתמש ב-toast notifications להודעות הצלחה/כישלון
- טפל במצבי טעינה עם Skeleton components

### **נגישות**
- כל הכפתורים והשדות חייבים להיות נגישים למקלדת
- השתמש ב-ARIA labels מתאימים
- ודא ניגודיות צבעים מספקת

## **דוגמאות קוד**

### **שימוש בהוק לטעינת נתונים**
```typescript
const {
  data: associationsData,
  isLoading,
  refetch
} = useAssociations(
  page,
  employerId,
  selectedType === "all" ? undefined : selectedType,
  searchTerm,
  startDate,
  endDate
);
```

### **הוספת שיוך חדש**
```typescript
const { mutate: createAssociation } = useCreateAssociation(employerId);

createAssociation({
  employeeId: "123",
  associationType: "DEPARTMENT",
  departmentId: "456",
  startDate: "2024-01-01",
  endDate: null,
  notes: "העברה למחלקת פיתוח"
});
```

## **הערות חשובות**
- שיוכים משפיעים ישירות על חישובי שכר
- ניתן להגדיר שיוכים עתידיים לתכנון מראש
- המערכת שומרת היסטוריה מלאה של כל השינויים
- לכל עובד יכול להיות רק שיוך אחד פעיל מכל סוג בזמן נתון 