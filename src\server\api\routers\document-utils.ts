import { TRPCError } from "@trpc/server";
import { Prisma } from "@prisma/client";
import type { Logger } from "pino";

interface ErrorHandlerContext {
  logger?: Logger;
  userId: string;
  employeeId?: string;
  documentId?: string;
  operation: string;
}

/**
 * A utility function to handle document-related errors in a more graceful way.
 * It logs the error and returns an appropriate error response.
 */
export function handleDocumentError(
  error: unknown, 
  context: ErrorHandlerContext
) {
  // Extract context for logging
  const { logger, userId, employeeId, documentId, operation } = context;
  
  // Log the error with context
  logger?.error(
    { 
      err: error, 
      userId, 
      employeeId, 
      documentId 
    },
    `Error ${operation}`
  );

  // Handle Prisma-specific errors more gracefully
  if (error instanceof Prisma.PrismaClientKnownRequestError) {
    // Handle known Prisma errors
    if (error.code === 'P2022') {
      // Missing column error
      return {
        success: false as const,
        error: `Schema mismatch: ${error.meta?.column || 'Unknown column'} doesn't exist. Database needs migration.`,
        code: "SCHEMA_ERROR",
        documents: []
      };
    } else if (error.code === 'P2023') {
      // Invalid data in the database
      return {
        success: false as const,
        error: "הנתונים בבסיס הנתונים אינם תקינים",
        code: "DATA_INTEGRITY_ERROR",
        documents: []
      };
    } else if (error.code === 'P2025') {
      // Record not found
      return {
        success: false as const,
        error: "המסמך המבוקש לא נמצא",
        code: "NOT_FOUND",
        documents: []
      };
    }
  }

  // For any other errors, return a generic error
  return {
    success: false as const,
    error: "אירעה שגיאה בלתי צפויה במערכת. אנא נסה שוב מאוחר יותר",
    code: "INTERNAL_SERVER_ERROR",
    documents: []
  };
}

/**
 * A wrapper function for document operations that catches and handles errors
 */
export async function safeDocumentOperation<T>(
  operation: () => Promise<T>,
  context: ErrorHandlerContext
): Promise<T | { success: false; error: string; code: string; documents: [] }> {
  try {
    return await operation();
  } catch (error) {
    return handleDocumentError(error, context) as { success: false; error: string; code: string; documents: [] };
  }
} 