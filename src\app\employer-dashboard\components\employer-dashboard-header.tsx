"use client";

import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import type { Dispatch, SetStateAction } from "react";
import { useSession } from "next-auth/react";

export type EmployerDashboardHeaderProps = {
  isLoading?: boolean;
  selectedPeriod: "current" | "previous" | "quarter" | "year";
  setSelectedPeriod: Dispatch<SetStateAction<"current" | "previous" | "quarter" | "year">>;
  refreshData: () => void;
};

export function EmployerDashboardHeader({
  isLoading = false,
  selectedPeriod,
  setSelectedPeriod,
  refreshData,
}: EmployerDashboardHeaderProps) {
  const { data: session } = useSession();
  
  // Memoize employer name to ensure consistent processing
  const employerName = React.useMemo(() => {
    return session?.user?.employerName || "מעסיק";
  }, [session]);
  
  if (isLoading) {
    return <EmployerDashboardHeaderSkeleton />;
  }

  return (
    <header className="mb-2 flex items-center justify-between">
      <h1 className="text-3xl font-bold tracking-tight">לוח בקרה {employerName}</h1>
    </header>
  );
}

function EmployerDashboardHeaderSkeleton() {
  return (
    <header className="mb-2 flex items-center justify-between">
      <Skeleton className="h-10 w-64" />
    </header>
  );
}
