"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";

type Report = {
  id: string;
  title: string;
  description: string;
  details: string;
};

type ReportsListProps = {
  reports?: Report[];
  isLoading: boolean;
  generateReport?: (data: { reportId: string }) => void;
  isGenerating?: boolean;
};

export function ReportsList({ 
  reports, 
  isLoading, 
  generateReport,
  isGenerating
}: ReportsListProps) {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">דוחות מערכת</h2>
      {isLoading ? (
        <ReportsListSkeleton />
      ) : reports && reports.length > 0 ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {reports.map((report) => (
            <Card key={report.id}>
              <CardHeader>
                <CardTitle>{report.title}</CardTitle>
                <CardDescription>{report.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <p>{report.details}</p>
              </CardContent>
              <CardFooter>
                <Button 
                  onClick={() => generateReport && generateReport({ reportId: report.id })}
                  disabled={isGenerating}
                >
                  הפק דוח
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div>אין דוחות זמינים</div>
      )}
    </div>
  );
}

function ReportsListSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: 3 }).map((_, index) => (
        <Card key={index}>
          <CardHeader>
            <Skeleton className="h-5 w-3/4 mb-2" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-9 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
} 