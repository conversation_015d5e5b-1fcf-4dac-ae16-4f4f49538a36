# מערכת ניהול מסמכי עובדים עם S3

## סקירה כללית

מערכת ניהול מסמכי עובדים מספקת פתרון מקיף לאחסון, ניהול וצפייה במסמכים של עובדים באמצעות Amazon S3. המערכת כוללת תמיכה מלאה בתעודות זהות, טפסי 101, וקטגוריות מסמכים נוספות.

## תכונות עיקריות

### 1. **קטגוריות מסמכים**

```typescript
const DOCUMENT_CATEGORIES = {
  'id-card': 'תעודת זהות',           // תמונות תעודת זהות (קדמי ואחורי)
  'form-101': 'טופס 101',           // טפסי 101 לפי שנים
  'contract': 'חוזה עבודה',         // חוזי עבודה
  'bank-details': 'פרטי בנק',      // פרטי חשבון בנק
  'medical': 'מסמכים רפואיים',      // תעודות רפואיות
  'education': 'תעודות השכלה',      // תעודות לימודים
  'general': 'כללי'                // מסמכים כלליים
};
```

### 2. **העלאת קבצים**

- **Drag & Drop**: גרירה ושחרור קבצים
- **בחירת קבצים**: לחיצה לבחירת קבצים
- **אימות סוגי קבצים**: בדיקה אוטומטית של סוגי קבצים מותרים
- **מטא-דאטה**: הוספת כותרת, תיאור ושנה למסמכים

### 3. **ניהול מסמכים**

- **צפייה**: הורדה מאובטחת עם URL חתום
- **עריכה**: עדכון מטא-דאטה של מסמכים
- **מחיקה**: מחיקה מ-S3 ומבסיס הנתונים
- **חיפוש**: סינון לפי קטגוריה ושנה

### 4. **ארגון לפי טאבים**

- **כל המסמכים**: תצוגה כללית של כל המסמכים
- **תעודת זהות**: מסמכי זהות עם הצגת מספר ת.ז
- **טופס 101**: ארגון לפי שנים עם אפשרות סינון
- **לפי קטגוריות**: ארגון לפי סוגי מסמכים

## מבנה טכני

### Backend (tRPC Procedures)

```typescript
// קבלת מסמכי עובד
getDocuments: protectedProcedure
  .input(z.object({
    employeeId: z.string(),
    category: z.string().optional(),
    page: z.number().default(1),
    limit: z.number().default(20)
  }))

// קבלת URL חתום להעלאה
getDocumentUploadUrl: protectedProcedure
  .input(z.object({
    employeeId: z.string(),
    fileName: z.string(),
    fileType: z.string(),
    category: z.string()
  }))

// רישום מסמך אחרי העלאה
registerDocument: protectedProcedure
  .input(z.object({
    employeeId: z.string(),
    title: z.string().optional(),
    fileName: z.string(),
    fileType: z.string(),
    fileSize: z.number(),
    fileKey: z.string(),
    category: z.string(),
    year: z.number().optional(),
    description: z.string().optional()
  }))
```

### Frontend Components

#### EmployeeDocumentsManager
הקומפוננטה הראשית לניהול מסמכי עובד:

```typescript
interface EmployeeDocumentsManagerProps {
  employeeId: string;
  employeeName: string;
  employeeNationalId: string;
}
```

#### DocumentUploadModal
מודל להעלאת מסמכים חדשים:

```typescript
interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  employeeId: string;
  category: string;
  onUploadSuccess: () => void;
}
```

#### DocumentCard
כרטיס להצגת מסמך בודד:

```typescript
interface DocumentCardProps {
  document: Document;
  employeeId: string;
  onEdit: (document: Document) => void;
  onRefresh: () => void;
}
```

### Hooks מותאמים

```typescript
// קבלת מסמכי עובד
const { documents, isLoading, refetch } = useEmployeeDocuments(employeeId, category, page);

// קבלת טפסי 101
const { documents, documentsByYear, availableYears } = useEmployeeForm101Documents(employeeId, year);

// קבלת תעודות זהות
const { employee, documents } = useEmployeeIdCardDocuments(employeeId);

// העלאת מסמך
const { uploadDocument, isUploading } = useUploadEmployeeDocument(employeeId);

// מחיקת מסמך
const { deleteDocument, isDeleting } = useDeleteEmployeeDocument(employeeId);

// הורדת מסמך
const { downloadDocument, isDownloading } = useDownloadEmployeeDocument();
```

## הגדרות Cache

### זמני Cache מותאמים

```typescript
const CACHE_CONFIG = {
  // מסמכים כלליים - 10 דקות
  employeeDocuments: {
    staleTime: 10 * 60 * 1000,
    refetchInterval: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000
  },
  
  // תעודות זהות - שעה (סטטי)
  employeeIdCards: {
    staleTime: 60 * 60 * 1000,
    refetchInterval: false,
    gcTime: 60 * 60 * 1000
  },
  
  // טפסי 101 - שעתיים (סטטי מאוד)
  employeeForm101: {
    staleTime: 2 * 60 * 60 * 1000,
    refetchInterval: false,
    gcTime: 2 * 60 * 60 * 1000
  }
};
```

### אסטרטגיות Invalidation

```typescript
const INVALIDATION_STRATEGIES = {
  // כשמסמך עובד משתנה
  employeeDocumentOperation: (utils, employeeId, category) => {
    void utils.employee.getDocuments.invalidate({ employeeId });
    
    if (category === 'form-101') {
      void utils.employee.getForm101Documents.invalidate({ employeeId });
    } else if (category === 'id-card') {
      void utils.employee.getIdCardDocuments.invalidate({ employeeId });
    }
    
    void utils.employee.getById.invalidate({ id: employeeId });
  }
};
```

## אבטחה

### הרשאות גישה

1. **אימות משתמש**: כל הפעולות דורשות אימות
2. **בדיקת Tenant**: מסמכים נגישים רק למשתמשי אותו tenant
3. **בדיקת עובד**: גישה רק למסמכי עובדים שייכים לאותו מעסיק
4. **URL חתומים**: גישה מוגבלת בזמן לקבצים ב-S3

### מבנה S3

```
employee-docs/
├── {employeeId}/
│   ├── id-card/
│   │   ├── {uuid}-front.jpg
│   │   └── {uuid}-back.jpg
│   ├── form-101/
│   │   ├── {uuid}-form-101-2024.pdf
│   │   └── {uuid}-form-101-2023.pdf
│   ├── contract/
│   │   └── {uuid}-contract.pdf
│   └── general/
│       └── {uuid}-document.pdf
```

## שימוש במערכת

### 1. העלאת מסמך חדש

```typescript
const { uploadDocument } = useUploadEmployeeDocument(employeeId);

await uploadDocument(file, 'id-card', {
  title: 'תעודת זהות - צד קדמי',
  description: 'תמונת תעודת זהות צד קדמי',
  year: 2024
});
```

### 2. הורדת מסמך

```typescript
const { downloadDocument } = useDownloadEmployeeDocument();

await downloadDocument(documentId, employeeId);
```

### 3. עדכון מטא-דאטה

```typescript
const { updateDocumentMetadata } = useUpdateEmployeeDocumentMetadata(employeeId);

updateDocumentMetadata({
  documentId,
  employeeId,
  title: 'כותרת חדשה',
  description: 'תיאור מעודכן',
  year: 2024
});
```

### 4. מחיקת מסמך

```typescript
const { deleteDocument } = useDeleteEmployeeDocument(employeeId);

deleteDocument({ documentId, employeeId });
```

## תכונות מתקדמות

### 1. **Optimistic Updates**

המערכת מבצעת עדכונים אופטימיסטיים לחוויית משתמש טובה יותר:

```typescript
onMutate: async ({ documentId }) => {
  // ביטול בקשות יוצאות
  await utils.employee.getDocuments.cancel({ employeeId });
  
  // שמירת מצב קודם
  const previousDocuments = utils.employee.getDocuments.getData({ employeeId });
  
  // עדכון אופטימיסטי
  utils.employee.getDocuments.setData({ employeeId }, {
    ...previousDocuments,
    documents: previousDocuments.documents.filter(doc => doc.id !== documentId)
  });
  
  return { previousDocuments };
}
```

### 2. **Prefetching חכם**

```typescript
// Prefetching אוטומטי של נתונים קשורים
PREFETCH_STRATEGIES.prefetchEmployeeRelatedData = (utils, employeeId) => {
  void utils.employee.getDocuments.prefetch({ employeeId });
  void utils.employee.getIdCardDocuments.prefetch({ employeeId });
  void utils.employee.getForm101Documents.prefetch({ employeeId });
};
```

### 3. **אימות קבצים**

```typescript
const ALLOWED_FILE_TYPES = {
  'id-card': ['image/jpeg', 'image/png', 'application/pdf'],
  'form-101': ['application/pdf', 'image/jpeg', 'image/png'],
  'contract': ['application/pdf', 'application/msword'],
  'general': ['application/pdf', 'image/jpeg', 'image/png']
};
```

## ביצועים

### מדדי ביצועים

- **זמן העלאה**: 2-5 שניות (תלוי בגודל הקובץ)
- **זמן הורדה**: מיידי (URL חתום)
- **זמן טעינת רשימה**: 0.5-1 שניה (עם cache)
- **זמן חיפוש**: 0.2-0.5 שניה

### אופטימיזציות

1. **Cache מותאם**: זמני cache שונים לסוגי נתונים שונים
2. **Lazy Loading**: טעינה עצלה של תמונות
3. **Pagination**: חלוקה לעמודים למסמכים רבים
4. **Background Sync**: עדכון ברקע לנתונים קריטיים

## תחזוקה ומעקב

### Audit Logs

כל פעולה נרשמת ב-audit log:

```typescript
await db.auditLog.create({
  data: {
    tenantId: user.tenantId,
    userId: userId,
    action: "CREATE", // CREATE, UPDATE, DELETE
    modelName: "Document",
    recordId: document.id,
    newValues: {
      title: document.title,
      fileName: document.fileName,
      employeeId: document.employeeId,
      category: document.category
    }
  }
});
```

### מעקב שגיאות

```typescript
try {
  await uploadDocument(file, category, options);
} catch (error) {
  ctx.logger?.error(
    { err: error, userId, employeeId },
    "Error uploading employee document"
  );
  throw new TRPCError({
    code: "INTERNAL_SERVER_ERROR",
    message: "Failed to upload document"
  });
}
```

## הרחבות עתידיות

### 1. **OCR Integration**
- זיהוי טקסט אוטומטי במסמכים
- חילוץ נתונים מתעודות זהות

### 2. **Digital Signatures**
- חתימה דיגיטלית על מסמכים
- אימות אותנטיות

### 3. **Version Control**
- ניהול גרסאות של מסמכים
- היסטוריית שינויים

### 4. **Bulk Operations**
- העלאה מרובה של קבצים
- פעולות קבוצתיות

### 5. **Advanced Search**
- חיפוש בתוכן המסמכים
- תגיות ומטא-דאטה מתקדמת

## דוגמאות שימוש

### העלאת תעודת זהות

```typescript
// בקומפוננטה
const { uploadDocument, isUploading } = useUploadEmployeeDocument(employeeId);

const handleIdCardUpload = async (file: File, side: 'front' | 'back') => {
  await uploadDocument(file, 'id-card', {
    title: `תעודת זהות - ${side === 'front' ? 'קדמי' : 'אחורי'}`,
    description: `תמונת תעודת זהות צד ${side === 'front' ? 'קדמי' : 'אחורי'}`,
  });
};
```

### העלאת טופס 101

```typescript
const handleForm101Upload = async (file: File, year: number) => {
  await uploadDocument(file, 'form-101', {
    title: `טופס 101 - ${year}`,
    description: `טופס 101 לשנת ${year}`,
    year
  });
};
```

### צפייה במסמכים לפי שנה

```typescript
const { documentsByYear, availableYears } = useEmployeeForm101Documents(employeeId);

// הצגת מסמכים לפי שנה
{Object.entries(documentsByYear).map(([year, documents]) => (
  <div key={year}>
    <h3>שנת {year}</h3>
    {documents.map(doc => <DocumentCard key={doc.id} document={doc} />)}
  </div>
))}
```

המערכת מספקת פתרון מקיף ומתקדם לניהול מסמכי עובדים עם דגש על ביצועים, אבטחה וחוויית משתמש מעולה. 