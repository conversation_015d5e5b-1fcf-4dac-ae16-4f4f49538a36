"use client";

import { useState, useEffect } from "react";
import { PlusCircle, FileText, UserPlus, Printer, Search, Settings, FileOutput, Loader2 } from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/rtl-components";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/rtl-components";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/rtl-components";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/rtl-components";
import { Checkbox } from "@/components/ui/checkbox";
import { useEmployers } from "@/app/owner-dashboard/hooks/useEmployer";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default function EmployersPage() {
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [status, setStatus] = useState<"active" | "inactive" | "all">("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newEmployer, setNewEmployer] = useState({ name: "", identifier: "" });
  
  // Use the hook to fetch employers
  const { 
    employers, 
    totalCount, 
    totalPages, 
    isLoading, 
    isError, 
    createEmployer, 
    isCreating,
    refetch
  } = useEmployers(page, status);

  // Update status when showInactive changes
  useEffect(() => {
    setStatus(showInactive ? "all" : "active");
  }, [showInactive]);

  // Client-side search filtering (server-side would be better for large datasets)
  const filteredEmployers = employers?.filter(employer => 
    employer.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
    employer.identifier.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  const handleCreateEmployer = async () => {
    if (newEmployer.name && newEmployer.identifier) {
      try {
        await createEmployer({
          name: newEmployer.name.trim(),
          identifier: newEmployer.identifier.trim(),
          status: "active"
        });
        setNewEmployer({ name: "", identifier: "" });
        setIsCreateDialogOpen(false);
        setPage(1); // Reset to first page to show the new employer
        refetch();
      } catch (error: any) {
        // Using a proper toast/alert component instead of window.alert
        const errorMessage = error.response?.status === 409 
          ? "מעסיק עם נתונים זהים כבר קיים במערכת"
          : "יצירת המעסיק נכשלה";
          
        // Replace this with your toast system when implemented
        alert(errorMessage);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">ניהול מעסיקים</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Printer className="ml-2 h-4 w-4" />
            הדפסה
          </Button>
          <Button variant="outline" size="sm">
            <FileOutput className="ml-2 h-4 w-4" />
            יצוא אקסל
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <PlusCircle className="ml-2 h-4 w-4" />
                הוספת מעסיק
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>הוספת מעסיק חדש</DialogTitle>
                <DialogDescription>
                  הזן את פרטי המעסיק החדש. שדות המסומנים ב־* הם שדות חובה.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name">שם מעסיק *</Label>
                  <Input
                    id="name"
                    value={newEmployer.name}
                    onChange={(e) => setNewEmployer(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="identifier">מספר זיהוי / ח.פ. *</Label>
                  <Input
                    id="identifier"
                    value={newEmployer.identifier}
                    onChange={(e) => setNewEmployer(prev => ({ ...prev, identifier: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button 
                  onClick={handleCreateEmployer} 
                  disabled={!newEmployer.name || !newEmployer.identifier || isCreating}
                >
                  {isCreating ? (
                    <>
                      <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                      יוצר מעסיק...
                    </>
                  ) : "יצירת מעסיק"}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Card>
        <CardHeader className="px-6 py-4">
          <CardTitle className="text-lg">רשימת מעסיקים</CardTitle>
        </CardHeader>
        <CardContent className="px-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="relative w-64">
                <Search className="absolute right-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="חיפוש מעסיק..."
                  className="pl-4 pr-10"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex items-center gap-2">
                <Checkbox 
                  id="showInactive" 
                  checked={showInactive} 
                  onCheckedChange={(checked) => {
                    if (typeof checked === "boolean") setShowInactive(checked);
                  }}
                />
                <label
                  htmlFor="showInactive"
                  className="text-sm font-medium leading-none cursor-pointer"
                >
                  הצג לא פעילים
                </label>
              </div>
            </div>
            
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="icon" 
                className="mr-2"
                onClick={() => refetch()}
              >
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {isError ? (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>שגיאה</AlertTitle>
              <AlertDescription>
                לא ניתן לטעון את רשימת המעסיקים. אנא נסה שוב מאוחר יותר.
              </AlertDescription>
            </Alert>
          ) : null}
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox />
                  </TableHead>
                  <TableHead>שם מעסיק</TableHead>
                  <TableHead>מזהה</TableHead>
                  <TableHead>מספר עובדים</TableHead>
                  <TableHead>סטטוס</TableHead>
                  <TableHead>תלוש אחרון</TableHead>
                  <TableHead className="w-36">פעולות</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <Loader2 className="h-6 w-6 animate-spin ml-2" />
                        <span>טוען מעסיקים...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredEmployers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      לא נמצאו מעסיקים
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEmployers.map((employer) => (
                    <TableRow key={employer.id}>
                      <TableCell>
                        <Checkbox />
                      </TableCell>
                      <TableCell className="font-medium">{employer.name}</TableCell>
                      <TableCell>{employer.identifier}</TableCell>
                      <TableCell>{employer.employeeCount}</TableCell>
                      <TableCell>
                        <Badge variant={employer.status === "active" ? "default" : "secondary"}>
                          {employer.status === "active" ? "פעיל" : "לא פעיל"}
                        </Badge>
                      </TableCell>
                      <TableCell>{employer.lastPayslip}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="icon">
                            <FileText className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <UserPlus className="h-4 w-4" />
                          </Button>
                          <Link href={`/owner-dashboard/employers/${employer.id}/edit`}>
                            <Button variant="outline" size="sm">
                              עריכה
                            </Button>
                          </Link>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex items-center justify-between py-4">
            <div className="text-sm text-muted-foreground">
              {totalCount !== undefined ? (
                <>
                  מציג <strong>{filteredEmployers.length}</strong> מתוך <strong>{totalCount}</strong> מעסיקים
                </>
              ) : null}
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                disabled={page <= 1 || isLoading}
                onClick={() => setPage(prev => Math.max(prev - 1, 1))}
              >
                הקודם
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                disabled={
                  isLoading ||
                  filteredEmployers.length === 0 ||
                  (!!totalPages && page >= totalPages)
                }
                onClick={() => setPage(prev => prev + 1)}
              >
                הבא
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 