"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/rtl-components";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from "@/components/ui/rtl-components";
import { Input } from "@/components/ui/input";
import { Form, FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserPlus, Send } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { type EmployerTabProps } from "../types";

export function EmployerEmployeesTab({ employer }: EmployerTabProps) {
  const [addEmployeeOpen, setAddEmployeeOpen] = useState(false);

  const employeeSchema = z.object({
    name: z.string().min(1, "יש להזין שם"),
    identifier: z.string().min(1, "יש להזין מזהה")
  });

  type EmployeeValues = z.infer<typeof employeeSchema>;

  const form = useForm<EmployeeValues>({
    resolver: zodResolver(employeeSchema),
    defaultValues: { name: "", identifier: "" }
  });

  const handleAddEmployee = () => {
    setAddEmployeeOpen(true);
  };

  const onSubmit = (values: EmployeeValues) => {
    console.log("Add employee", values);
    toast.success("העובד נוסף לרשימה");
    setAddEmployeeOpen(false);
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>רשימת עובדים</CardTitle>
          <Button size="sm" onClick={handleAddEmployee}>
            <UserPlus className="mr-2 h-4 w-4" />
            הוספת עובד חדש
          </Button>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">
            לחץ על "הוספת עובד חדש" כדי להוסיף עובדים למעסיק זה
          </p>
        </CardContent>
      </Card>

      <Dialog open={addEmployeeOpen} onOpenChange={setAddEmployeeOpen}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle>הוספת עובד חדש</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>שם העובד</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="identifier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>מזהה</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />
              <DialogFooter className="mt-2">
                <Button type="submit" size="sm">
                  <Send className="ml-2 h-4 w-4" />שמירה
                </Button>
                <DialogClose asChild>
                  <Button type="button" variant="outline" size="sm">
                    ביטול
                  </Button>
                </DialogClose>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
