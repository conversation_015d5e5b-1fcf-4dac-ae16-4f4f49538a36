"use client";

import { AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";

export type EmployerAlert = {
  id: string;
  message: string;
  type: string;
};

export type EmployerSystemAlertsProps = {
  alerts?: EmployerAlert[];
  isLoading?: boolean;
};

export function EmployerSystemAlerts({ alerts, isLoading = false }: EmployerSystemAlertsProps) {
  if (isLoading) {
    return <EmployerSystemAlertsSkeleton />;
  }

  if (!alerts || alerts.length === 0) return null;

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>התראת מערכת</AlertTitle>
      <AlertDescription>{alerts[0]?.message || "התראת מערכת לא מוגדרת"}</AlertDescription>
    </Alert>
  );
}

function EmployerSystemAlertsSkeleton() {
  return (
    <div className="mb-4 rounded-md border border-gray-200 p-4">
      <div className="flex items-start space-x-2">
        <Skeleton className="mr-2 h-4 w-4" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-full" />
        </div>
      </div>
    </div>
  );
}
