"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/rtl-components";
import { Badge } from "@/components/ui/rtl-components";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from "@/components/ui/rtl-components";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Settings, Send } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { type EmployerTabProps } from "../types";
import { type Employer } from "@/schema/employer";

// Extended employer type with additional UI properties
interface ExtendedEmployer extends Employer {
  departments: string[];
  payrollDay?: number;
}

export function EmployerSettingsTab({ employer }: EmployerTabProps) {
  const [deptDialogOpen, setDeptDialogOpen] = useState(false);
  const [workDaysOpen, setWorkDaysOpen] = useState(false);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);

  const [departments, setDepartments] = useState<string[]>((employer as ExtendedEmployer).departments);

  const departmentSchema = z.object({ name: z.string().min(1, "יש להזין שם מחלקה") });
  const deptForm = useForm<{ name: string }>({
    resolver: zodResolver(departmentSchema),
    defaultValues: { name: "" }
  });

  const workDaysSchema = z.object({
    days: z.array(z.string()).min(1)
  });
  const workDaysForm = useForm<{ days: string[] }>({
    resolver: zodResolver(workDaysSchema),
    defaultValues: { days: [] }
  });

  const paymentSchema = z.object({ payrollDay: z.number().min(1).max(31) });
  const paymentForm = useForm<{ payrollDay: number }>({
    resolver: zodResolver(paymentSchema),
    defaultValues: { payrollDay: (employer as ExtendedEmployer).payrollDay ?? 1 }
  });

  const handleAddDepartment = (data: { name: string }) => {
    setDepartments(prev => [...prev, data.name]);
    toast.success("המחלקה נוספה");
    deptForm.reset();
    setDeptDialogOpen(false);
  };

  const handleSaveWorkDays = (data: { days: string[] }) => {
    console.log("Work days", data.days);
    toast.success("ימי העבודה עודכנו");
    setWorkDaysOpen(false);
  };

  const handleSavePayment = (data: { payrollDay: number }) => {
    console.log("Payment settings", data);
    toast.success("הגדרות התשלום עודכנו");
    setPaymentDialogOpen(false);
  };

  return (
    <>
    <Card>
      <CardHeader>
        <CardTitle>הגדרות מעסיק</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-2">מחלקות</h3>
            <Separator className="mb-4" />
            <div className="flex flex-wrap gap-2">
              {departments.map((dept) => (
                <Badge key={`dept-${dept}`} variant="secondary">{dept}</Badge>
              ))}
              <Button
                variant="outline"
                size="sm"
                className="h-6"
                onClick={() => setDeptDialogOpen(true)}
              >
                <Settings className="ml-1 h-3 w-3" />
                ניהול מחלקות
              </Button>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">ימי עבודה</h3>
            <Separator className="mb-4" />
            <p className="text-muted-foreground">הגדרות ימי ושעות עבודה טרם הוגדרו</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 h-6"
              onClick={() => setWorkDaysOpen(true)}
            >
              <Settings className="ml-2 h-4 w-4" />
              הגדרת ימי עבודה
            </Button>
          </div>
          
          <div>
            <h3 className="text-lg font-medium mb-2">הגדרות תשלום</h3>
            <Separator className="mb-4" />
 <p>יום תשלום: <strong>{(employer as ExtendedEmployer).payrollDay ?? 'לא הוגדר'} לחודש</strong></p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2 h-6"
              onClick={() => setPaymentDialogOpen(true)}
            >
              <Settings className="ml-2 h-4 w-4" />
              עדכון הגדרות
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    {/* Department dialog */}
    <Dialog open={deptDialogOpen} onOpenChange={setDeptDialogOpen}>
      <DialogContent className="sm:max-w-[350px]">
        <DialogHeader>
          <DialogTitle>הוספת מחלקה</DialogTitle>
          <DialogDescription>הזן שם מחלקה חדשה.</DialogDescription>
        </DialogHeader>
        <Form {...deptForm}>
          <form onSubmit={deptForm.handleSubmit(handleAddDepartment)} className="space-y-4">
            <FormField
              control={deptForm.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>שם מחלקה</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <DialogFooter className="mt-2">
              <Button type="submit" size="sm">
                <Send className="ml-2 h-4 w-4" />שמירה
              </Button>
              <DialogClose asChild>
                <Button type="button" variant="outline" size="sm">
                  ביטול
                </Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>

    {/* Work days dialog */}
    <Dialog open={workDaysOpen} onOpenChange={setWorkDaysOpen}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>הגדרת ימי עבודה</DialogTitle>
        </DialogHeader>
        <Form {...workDaysForm}>
          <form onSubmit={workDaysForm.handleSubmit(handleSaveWorkDays)} className="space-y-2">
            {['א', 'ב', 'ג', 'ד', 'ה', 'ו'].map((day) => (
              <FormField
                key={day}
                control={workDaysForm.control}
                name="days"
                render={() => (
                  <FormItem className="flex items-center gap-2">
                    <FormControl>
                      <Checkbox
                        checked={workDaysForm.watch('days').includes(day)}
                        onCheckedChange={(checked) => {
                          const current = workDaysForm.getValues('days');
                          if (checked) {
                            workDaysForm.setValue('days', [...current, day]);
                          } else {
                            workDaysForm.setValue('days', current.filter((d) => d !== day));
                          }
                        }}
                      />
                    </FormControl>
                    <FormLabel className="cursor-pointer select-none">יום {day}</FormLabel>
                  </FormItem>
                )}
              />
            ))}
            <DialogFooter className="mt-2">
              <Button type="submit" size="sm">
                <Send className="ml-2 h-4 w-4" />שמירה
              </Button>
              <DialogClose asChild>
                <Button type="button" variant="outline" size="sm">
                  ביטול
                </Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>

    {/* Payment dialog */}
    <Dialog open={paymentDialogOpen} onOpenChange={setPaymentDialogOpen}>
      <DialogContent className="sm:max-w-[350px]">
        <DialogHeader>
          <DialogTitle>עדכון הגדרות תשלום</DialogTitle>
        </DialogHeader>
        <Form {...paymentForm}>
          <form onSubmit={paymentForm.handleSubmit(handleSavePayment)} className="space-y-4">
            <FormField
              control={paymentForm.control}
              name="payrollDay"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>יום תשלום בחודש</FormLabel>
                  <FormControl>
                    <Input type="number" min="1" max="31" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            <DialogFooter className="mt-2">
              <Button type="submit" size="sm">
                <Send className="ml-2 h-4 w-4" />שמירה
              </Button>
              <DialogClose asChild>
                <Button type="button" variant="outline" size="sm">
                  ביטול
                </Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
    </>
  );
}
