import { Home, Users, Receipt, FileText, Building, Settings, Link2, Clock, Calculator } from "lucide-react";

export const employerDashboardNavItems = [
	{
		title: "לוח בקרה",
		href: "/employer-dashboard",
		icon: <Home className="h-4 w-4" />,
	},
	{
		title: "עובדים",
		href: "/employer-dashboard/employees",
		icon: <Users className="h-4 w-4" />,
	},
	{
		title: "שיוכים",
		href: "/employer-dashboard/associations",
		icon: <Link2 className="h-4 w-4" />,
	},
	{
		title: "הסכמי נוכחות",
		href: "/employer-dashboard/attendance-agreements",
		icon: <Clock className="h-4 w-4" />,
	},
	{
		title: "מרכיבי ניכוי",
		href: "/employer-dashboard/deduction-components",
		icon: <Calculator className="h-4 w-4" />,
	},
	{
		title: "תלושי שכר",
		href: "/employer-dashboard/payslips",
		icon: <Receipt className="h-4 w-4" />,
	},
	{
		title: "טפסים",
		href: "/employer-dashboard/forms",
		icon: <FileText className="h-4 w-4" />,
	},
	{
		title: "מעסיקים",
		href: "/employer-dashboard/employers",
		icon: <Building className="h-4 w-4" />,
	},
	{
		title: "הגדרות",
		href: "/employer-dashboard/settings",
		icon: <Settings className="h-4 w-4" />,
	},
]; 