"use client";

import Link from "next/link";
import { Button } from "@/components/ui/rtl-components";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { type Employer } from "@/schema/employer";

interface EmployerStatsProps {
  employer: Employer;
}

export function EmployerStats({ employer }: EmployerStatsProps) {
  return (
    <div className="grid gap-4 md:grid-cols-4">
      <Card>
        <CardHeader className="p-4">
          <CardTitle className="text-sm font-medium text-muted-foreground">מספר עובדים</CardTitle>
          <CardDescription className="text-2xl font-bold">{employer.employeeCount}</CardDescription>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <Link href={`/owner-dashboard/employers/${employer.id}/employees`} className="text-sm text-primary hover:underline">
            צפייה בעובדים
          </Link>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="p-4">
          <CardTitle className="text-sm font-medium text-muted-foreground">תלוש אחרון</CardTitle>
          <CardDescription className="text-2xl font-bold">{employer.lastPayslip}</CardDescription>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <Button variant="link" className="p-0 h-auto text-sm">
            הפקת תלושים
          </Button>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="p-4">
          <CardTitle className="text-sm font-medium text-muted-foreground">תיק ניכויים</CardTitle>
          <CardDescription className="text-2xl font-bold">{employer.identifier}</CardDescription>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <Button variant="link" className="p-0 h-auto text-sm">
            דוחות למס הכנסה
          </Button>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="p-4">
          <CardTitle className="text-sm font-medium text-muted-foreground">יום תשלום</CardTitle>
          <CardDescription className="text-2xl font-bold">20 לחודש</CardDescription>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <Button variant="link" className="p-0 h-auto text-sm">
            הגדרות תשלום
          </Button>
        </CardContent>
      </Card>
    </div>
  );
} 