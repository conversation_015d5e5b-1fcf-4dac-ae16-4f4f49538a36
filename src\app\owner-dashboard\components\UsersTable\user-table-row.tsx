"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>Row, <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/ui/rtl-components";
import { type User } from "./types";

type UserTableRowProps = {
  user: User;
  showEmployer?: boolean;
};

export function UserTableRow({ user, showEmployer = false }: UserTableRowProps) {
  return (
    <TableRow>
      <TableCell className="font-medium">{user.name || "-"}</TableCell>
      <TableCell>{user.email || "-"}</TableCell>
      <TableCell>{user.role || "-"}</TableCell>
      {showEmployer && (
        <TableCell className="font-medium text-primary">{user.employerName || "-"}</TableCell>
      )}
      <TableCell>
        <Badge variant={user.status === "active" ? "default" : "secondary"}>
          {user.status === "active" ? "פעיל" : "לא פעיל"}
        </Badge>
      </TableCell>
      <TableCell>
        <Button variant="outline" size="sm" asChild>
          <a href={`/owner-dashboard/users/${user.id}`}>עריכה</a>
        </Button>
      </TableCell>
    </TableRow>
  );
} 