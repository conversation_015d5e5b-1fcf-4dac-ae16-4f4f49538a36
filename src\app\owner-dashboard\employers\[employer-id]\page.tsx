"use client";

import { useEffect } from "react";
import { useParams } from "next/navigation";
import { 
  EmployerHeader, 
  EmployerStats, 
  EmployerTabs
} from "@/app/owner-dashboard/components/Employer";
import { useEmployer } from "@/app/owner-dashboard/hooks/useEmployer";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { type Employer } from "@/schema/employer";

// Define extended EmployerData interface with UI-specific properties
interface EmployerData extends Employer {
  // Additional UI specific fields
  lastPayroll?: string;
  address?: string;
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  taxId?: string;
  registrationDate?: string;
  accountManager?: string;
  payrollDay?: number;
  departments?: string[];
  recentActivity?: Array<{date: string; action: string; user: string}>;
  isActive?: boolean; // For backward compatibility
}

interface ApiEmployer {
  id: string;
  name: string;
  identifier?: string;
  employeeCount?: number;
  status?: string;
  lastPayslip?: string;
  address?: string | { street?: string; city?: string; zipCode?: string; country?: string };
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  taxId?: string;
  registrationDate?: string;
  accountManager?: string;
  payrollDay?: number;
  departments?: string[];
  recentActivity?: Array<{date: string; action: string; user: string}>;
}

export default function EmployerPage() {
  const params = useParams();
  const employerId = params["employer-id"] as string;
  
  const { 
    employer, 
    isLoading, 
    isError, 
    updateEmployer, 
    deleteEmployer 
  } = useEmployer(employerId);

  // Transform API employer data to our component's EmployerData type
  const mapEmployerData = (apiEmployer: ApiEmployer): EmployerData => {
    // Format address if it's an object, otherwise use default string
    let formattedAddress = "לא הוגדר";
    if (apiEmployer.address) {
      if (typeof apiEmployer.address === 'object') {
        const addr = apiEmployer.address;
        formattedAddress = [
          addr.street,
          addr.city,
          addr.zipCode,
          addr.country
        ].filter(Boolean).join(", ");
      } else {
        formattedAddress = apiEmployer.address;
      }
    }

    return {
      id: apiEmployer.id,
      name: apiEmployer.name,
      identifier: apiEmployer.identifier || "",
      employeeCount: apiEmployer.employeeCount || 0,
      status: (apiEmployer.status as "active" | "inactive") || "inactive",
      lastPayslip: apiEmployer.lastPayslip || "",
      // Additional UI properties
      isActive: apiEmployer.status === "active",
      lastPayroll: apiEmployer.lastPayslip || "N/A",
      address: formattedAddress,
      contactPerson: apiEmployer.contactPerson || "לא הוגדר",
      contactEmail: apiEmployer.contactEmail || "לא הוגדר",
      contactPhone: apiEmployer.contactPhone || "לא הוגדר", 
      taxId: apiEmployer.taxId || "לא הוגדר",
      registrationDate: apiEmployer.registrationDate || "לא הוגדר",
      accountManager: apiEmployer.accountManager || "לא הוגדר",
      payrollDay: apiEmployer.payrollDay || 10,
      departments: apiEmployer.departments || ["הנהלה", "כספים"],
      recentActivity: apiEmployer.recentActivity || [
        { date: "לא קיימים נתונים", action: "אין פעילות אחרונה", user: "" }
      ]
    };
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-12 w-full" />
        <div className="grid gap-4 md:grid-cols-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  if (isError || !employer) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>שגיאה</AlertTitle>
        <AlertDescription>
          לא ניתן לטעון את נתוני המעסיק. אנא נסה שוב מאוחר יותר.
        </AlertDescription>
      </Alert>
    );
  }

  const employerData = mapEmployerData(employer);

  return (
    <div className="space-y-6">
      <EmployerHeader employer={employerData} />
      <EmployerStats employer={employerData} />
      <EmployerTabs employer={employerData} />
    </div>
  );
}
