import { AlertType, AlertCategory } from "@prisma/client";

export interface AlertData {
  id?: string;
  type: AlertType;
  category: AlertCategory;
  message: string;
  employeeId?: string;
  employeeName?: string;
  dueDate?: Date;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  context?: Record<string, unknown>;
  isResolved?: boolean;
}

export interface ComplianceIssue {
  category: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  employeeId: string;
  employeeName: string;
  dueDate?: Date;
  context?: Record<string, unknown>;
}

/**
 * Generate visa expiry alerts for foreign workers
 */
export function generateVisaExpiryAlerts(
  employees: Array<{
    id: string;
    firstName: string;
    lastName: string;
    isForeign: boolean;
    visaExpiry?: Date;
    country?: string;
  }>
): AlertData[] {
  const alerts: AlertData[] = [];
  const now = new Date();

  employees.forEach(employee => {
    if (!employee.isForeign || !employee.visaExpiry) return;

    const daysUntilExpiry = Math.ceil(
      (employee.visaExpiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );

    let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    let shouldAlert = false;
    let message = '';

    if (daysUntilExpiry < 0) {
      severity = 'CRITICAL';
      shouldAlert = true;
      message = `אשרת העבודה של ${employee.firstName} ${employee.lastName} פגה לפני ${Math.abs(daysUntilExpiry)} ימים`;
    } else if (daysUntilExpiry <= 30) {
      severity = 'CRITICAL';
      shouldAlert = true;
      message = `אשרת העבודה של ${employee.firstName} ${employee.lastName} תפוג בעוד ${daysUntilExpiry} ימים`;
    } else if (daysUntilExpiry <= 60) {
      severity = 'HIGH';
      shouldAlert = true;
      message = `אשרת העבודה של ${employee.firstName} ${employee.lastName} תפוג בעוד ${daysUntilExpiry} ימים`;
    } else if (daysUntilExpiry <= 90) {
      severity = 'MEDIUM';
      shouldAlert = true;
      message = `אשרת העבודה של ${employee.firstName} ${employee.lastName} תפוג בעוד ${daysUntilExpiry} ימים`;
    }

    if (shouldAlert) {
      const dueDate = new Date(employee.visaExpiry);
      dueDate.setDate(dueDate.getDate() - 30); // Alert 30 days before expiry

      alerts.push({
        type: severity === 'CRITICAL' ? AlertType.CRITICAL : AlertType.WARNING,
        category: AlertCategory.VISA_EXPIRATION,
        message,
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        dueDate,
        severity,
        context: {
          visaExpiry: employee.visaExpiry,
          country: employee.country,
          daysUntilExpiry
        }
      });
    }
  });

  return alerts;
}

/**
 * Generate missing Form 101 alerts
 */
export function generateMissingForm101Alerts(
  employees: Array<{
    id: string;
    firstName: string;
    lastName: string;
    startDate: Date;
    form101?: { id: string; completedAt?: Date } | null;
  }>
): AlertData[] {
  const alerts: AlertData[] = [];
  const now = new Date();

  employees.forEach(employee => {
    const daysSinceStart = Math.ceil(
      (now.getTime() - employee.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Alert if no Form 101 after 30 days of employment
    if (daysSinceStart > 30 && (!employee.form101 || !employee.form101.completedAt)) {
      let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';
      
      if (daysSinceStart > 90) {
        severity = 'CRITICAL';
      } else if (daysSinceStart > 60) {
        severity = 'HIGH';
      }

      alerts.push({
        type: severity === 'CRITICAL' ? AlertType.CRITICAL : AlertType.WARNING,
        category: AlertCategory.MISSING_FORM101,
        message: `טופס 101 חסר עבור ${employee.firstName} ${employee.lastName} (${daysSinceStart} ימים מתחילת העבודה)`,
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        severity,
        context: {
          startDate: employee.startDate,
          daysSinceStart
        }
      });
    }
  });

  return alerts;
}

/**
 * Generate missing critical documents alerts
 */
export function generateMissingDocumentAlerts(
  employees: Array<{
    id: string;
    firstName: string;
    lastName: string;
    documents?: Array<{
      category: string;
      uploadedAt: Date;
      metadata?: any;
    }>;
    startDate: Date;
  }>
): AlertData[] {
  const alerts: AlertData[] = [];
  const currentYear = new Date().getFullYear();

  employees.forEach(employee => {
    const documents = employee.documents || [];
    
    // Check for ID card or passport
    const hasIdCard = documents.some(doc => doc.category === 'id-card');
    const hasPassport = documents.some(doc => doc.category === 'passport');
    
    if (!hasIdCard && !hasPassport) {
      alerts.push({
        type: AlertType.CRITICAL,
        category: AlertCategory.GENERAL, // Using GENERAL until MISSING_DOCUMENTS is added to schema
        message: `תעודת זהות או דרכון חסרים עבור ${employee.firstName} ${employee.lastName}`,
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        severity: 'CRITICAL',
        context: {
          missingDocuments: ['id-card-or-passport'],
          employeeStartDate: employee.startDate
        }
      });
    }
    
    // Check for Form 101 for current year
    const hasCurrentYearForm101 = documents.some(
      doc => doc.category === 'form-101' && 
      (doc.metadata?.year === currentYear || 
       new Date(doc.uploadedAt).getFullYear() === currentYear)
    );
    
    if (!hasCurrentYearForm101) {
      alerts.push({
        type: AlertType.CRITICAL,
        category: AlertCategory.MISSING_FORM101,
        message: `טופס 101 לשנת ${currentYear} חסר עבור ${employee.firstName} ${employee.lastName}`,
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        severity: 'CRITICAL',
        context: {
          missingYear: currentYear,
          employeeStartDate: employee.startDate
        }
      });
    }
  });

  return alerts;
}

/**
 * Generate overtime limit alerts
 */
export function generateOvertimeLimitAlerts(
  payslips: Array<{
    employeeId: string;
    employeeName: string;
    year: number;
    month: number;
    items: Array<{
      kod?: string;
      units?: number;
      percentage?: number;
    }>;
  }>
): AlertData[] {
  const alerts: AlertData[] = [];

  payslips.forEach(payslip => {
    let totalOvertimeHours = 0;
    let criticalOvertimeHours = 0;

    payslip.items.forEach(item => {
      if (item.kod && item.units) {
        // Check overtime codes
        if (['1001', '1002', '1003', '1004'].includes(item.kod)) {
          totalOvertimeHours += item.units;
          
          // Critical overtime (175% and 200%)
          if (['1003', '1004'].includes(item.kod)) {
            criticalOvertimeHours += item.units;
          }
        }
      }
    });

    // Alert for excessive overtime
    if (totalOvertimeHours > 120) {
      alerts.push({
        type: AlertType.CRITICAL,
        category: AlertCategory.OVERTIME_LIMIT,
        message: `חריגת שעות נוספות עבור ${payslip.employeeName}: ${totalOvertimeHours} שעות (${payslip.month}/${payslip.year})`,
        employeeId: payslip.employeeId,
        employeeName: payslip.employeeName,
        severity: 'CRITICAL',
        context: {
          totalOvertimeHours,
          criticalOvertimeHours,
          month: payslip.month,
          year: payslip.year
        }
      });
    } else if (totalOvertimeHours > 80) {
      alerts.push({
        type: AlertType.WARNING,
        category: AlertCategory.OVERTIME_LIMIT,
        message: `שעות נוספות גבוהות עבור ${payslip.employeeName}: ${totalOvertimeHours} שעות (${payslip.month}/${payslip.year})`,
        employeeId: payslip.employeeId,
        employeeName: payslip.employeeName,
        severity: 'HIGH',
        context: {
          totalOvertimeHours,
          criticalOvertimeHours,
          month: payslip.month,
          year: payslip.year
        }
      });
    }
  });

  return alerts;
}

/**
 * Generate deposit compliance alerts for foreign workers
 */
export function generateDepositComplianceAlerts(
  employees: Array<{
    id: string;
    firstName: string;
    lastName: string;
    isForeign: boolean;
    grossSalary: number;
    netDeposit?: number;
    country?: string;
  }>
): AlertData[] {
  const alerts: AlertData[] = [];

  employees.forEach(employee => {
    if (!employee.isForeign) return;

    // Calculate required deposit (20% of gross salary for foreign workers)
    const requiredDeposit = employee.grossSalary * 0.20;
    const actualDeposit = employee.netDeposit || 0;
    const shortfall = requiredDeposit - actualDeposit;

    if (shortfall > 100) { // Allow small variance
      let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'MEDIUM';
      
      if (shortfall > requiredDeposit * 0.5) {
        severity = 'CRITICAL';
      } else if (shortfall > requiredDeposit * 0.25) {
        severity = 'HIGH';
      }

      alerts.push({
        type: severity === 'CRITICAL' ? AlertType.CRITICAL : AlertType.WARNING,
        category: AlertCategory.DEPOSIT_COMPLIANCE,
        message: `בעיית ציות פיקדון עבור ${employee.firstName} ${employee.lastName}: חסר ${shortfall.toFixed(0)} ש"ח`,
        employeeId: employee.id,
        employeeName: `${employee.firstName} ${employee.lastName}`,
        severity,
        context: {
          requiredDeposit,
          actualDeposit,
          shortfall,
          country: employee.country
        }
      });
    }
  });

  return alerts;
}

/**
 * Generate unusual deduction alerts
 */
export function generateUnusualDeductionAlerts(
  payslips: Array<{
    employeeId: string;
    employeeName: string;
    year: number;
    month: number;
    grossPay: number;
    items: Array<{
      description: string;
      amount: number;
      isDebit: boolean;
      kod?: string;
    }>;
  }>
): AlertData[] {
  const alerts: AlertData[] = [];

  payslips.forEach(payslip => {
    let totalDeductions = 0;
    const unusualDeductions: Array<{ description: string; amount: number }> = [];

    payslip.items.forEach(item => {
      if (item.isDebit && item.amount > 0) {
        totalDeductions += item.amount;

        // Check for unusually large deductions
        const deductionPercentage = (item.amount / payslip.grossPay) * 100;
        
        if (deductionPercentage > 15 && !['TAX', 'NI_EMP', 'NI_HEALTH', 'PENSION_EMP'].includes(item.kod || '')) {
          unusualDeductions.push({
            description: item.description,
            amount: item.amount
          });
        }
      }
    });

    // Alert if total deductions exceed 60% of gross pay
    const totalDeductionPercentage = (totalDeductions / payslip.grossPay) * 100;
    
    if (totalDeductionPercentage > 60) {
      alerts.push({
        type: AlertType.CRITICAL,
        category: AlertCategory.UNUSUAL_DEDUCTION,
        message: `ניכויים חריגים עבור ${payslip.employeeName}: ${totalDeductionPercentage.toFixed(1)}% מהשכר הברוטו`,
        employeeId: payslip.employeeId,
        employeeName: payslip.employeeName,
        severity: 'CRITICAL',
        context: {
          totalDeductionPercentage,
          totalDeductions,
          grossPay: payslip.grossPay,
          unusualDeductions,
          month: payslip.month,
          year: payslip.year
        }
      });
    } else if (unusualDeductions.length > 0) {
      alerts.push({
        type: AlertType.WARNING,
        category: AlertCategory.UNUSUAL_DEDUCTION,
        message: `ניכויים חריגים זוהו עבור ${payslip.employeeName} (${payslip.month}/${payslip.year})`,
        employeeId: payslip.employeeId,
        employeeName: payslip.employeeName,
        severity: 'MEDIUM',
        context: {
          unusualDeductions,
          month: payslip.month,
          year: payslip.year
        }
      });
    }
  });

  return alerts;
}

/**
 * Generate comprehensive compliance alerts
 */
export function generateComplianceAlerts(
  employees: Array<{
    id: string;
    firstName: string;
    lastName: string;
    isForeign: boolean;
    visaExpiry?: Date;
    country?: string;
    startDate: Date;
    grossSalary: number;
    netDeposit?: number;
    form101?: { id: string; completedAt?: Date } | null;
    documents?: Array<{
      category: string;
      uploadedAt: Date;
      metadata?: any;
    }>;
  }>,
  payslips: Array<{
    employeeId: string;
    employeeName: string;
    year: number;
    month: number;
    grossPay: number;
    items: Array<{
      description: string;
      amount: number;
      isDebit: boolean;
      kod?: string;
      units?: number;
      percentage?: number;
    }>;
  }>
): AlertData[] {
  const allAlerts: AlertData[] = [];

  // Generate all types of alerts
  allAlerts.push(...generateVisaExpiryAlerts(employees));
  allAlerts.push(...generateMissingForm101Alerts(employees));
  allAlerts.push(...generateMissingDocumentAlerts(employees));
  allAlerts.push(...generateOvertimeLimitAlerts(payslips));
  allAlerts.push(...generateDepositComplianceAlerts(employees));
  allAlerts.push(...generateUnusualDeductionAlerts(payslips));

  // Sort by severity and due date
  return allAlerts.sort((a, b) => {
    const severityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
    const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
    
    if (severityDiff !== 0) return severityDiff;
    
    // If same severity, sort by due date
    if (a.dueDate && b.dueDate) {
      return a.dueDate.getTime() - b.dueDate.getTime();
    }
    
    return 0;
  });
}

/**
 * Create alert summary for dashboard
 */
export function createAlertSummary(alerts: AlertData[]): {
  total: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  byCategory: Record<AlertCategory, number>;
  urgentAlerts: AlertData[];
} {
  const summary = {
    total: alerts.length,
    critical: 0,
    high: 0,
    medium: 0,
    low: 0,
    byCategory: {} as Record<AlertCategory, number>,
    urgentAlerts: [] as AlertData[]
  };

  // Initialize category counts
  Object.values(AlertCategory).forEach(category => {
    summary.byCategory[category] = 0;
  });

  alerts.forEach(alert => {
    // Count by severity
    switch (alert.severity) {
      case 'CRITICAL':
        summary.critical++;
        break;
      case 'HIGH':
        summary.high++;
        break;
      case 'MEDIUM':
        summary.medium++;
        break;
      case 'LOW':
        summary.low++;
        break;
    }

    // Count by category
    summary.byCategory[alert.category]++;

    // Add to urgent alerts if critical or high severity
    if (alert.severity === 'CRITICAL' || alert.severity === 'HIGH') {
      summary.urgentAlerts.push(alert);
    }
  });

  // Sort urgent alerts by severity and due date
  summary.urgentAlerts.sort((a, b) => {
    if (a.severity === 'CRITICAL' && b.severity !== 'CRITICAL') return -1;
    if (b.severity === 'CRITICAL' && a.severity !== 'CRITICAL') return 1;
    
    if (a.dueDate && b.dueDate) {
      return a.dueDate.getTime() - b.dueDate.getTime();
    }
    
    return 0;
  });

  return summary;
}

/**
 * Get alert priority score for sorting
 */
export function getAlertPriorityScore(alert: AlertData): number {
  let score = 0;

  // Base score by severity
  switch (alert.severity) {
    case 'CRITICAL':
      score += 1000;
      break;
    case 'HIGH':
      score += 100;
      break;
    case 'MEDIUM':
      score += 10;
      break;
    case 'LOW':
      score += 1;
      break;
  }

  // Additional score by category urgency
  switch (alert.category) {
    case AlertCategory.VISA_EXPIRATION:
      score += 500;
      break;
    case AlertCategory.OVERTIME_LIMIT:
      score += 300;
      break;
    case AlertCategory.DEPOSIT_COMPLIANCE:
      score += 200;
      break;
    case AlertCategory.MISSING_FORM101:
      score += 150;
      break;
    case AlertCategory.UNUSUAL_DEDUCTION:
      score += 100;
      break;
    default:
      score += 50;
  }

  // Reduce score based on due date (more urgent = higher score)
  if (alert.dueDate) {
    const daysUntilDue = Math.ceil(
      (alert.dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysUntilDue < 0) {
      score += 200; // Overdue
    } else if (daysUntilDue <= 7) {
      score += 100; // Due within a week
    } else if (daysUntilDue <= 30) {
      score += 50; // Due within a month
    }
  }

  return score;
} 