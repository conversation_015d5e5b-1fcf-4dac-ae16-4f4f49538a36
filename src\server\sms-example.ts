// Example usage of SMS functionality
// Note: You need to run `npx prisma generate` to update the Prisma client with the new SmsLog model

import { sendMessage, sendTemplatedSMS, sendBulkSMS, SMS_TEMPLATES, validateIsraeliPhoneNumber, formatPhoneForSMS } from "./sms";

// Example 1: Send a simple SMS with audit logging
async function sendSimpleSMS() {
  const phone = "0502466626"; // Israeli phone number
  const message = "שלום, זוהי הודעת בדיקה";
  const employeeId = "employee-uuid-here";
  const tenantId = "tenant-uuid-here";
  const userId = "user-uuid-here"; // User sending the SMS
  const userEmail = "<EMAIL>"; // User's email for audit
  
  // Validate and format phone number
  if (validateIsraeliPhoneNumber(phone)) {
    const formattedPhone = formatPhoneForSMS(phone);
    const success = await sendMessage(
      formattedPhone, 
      message, 
      employeeId, 
      tenantId,
      userId,
      userEmail
    );
    console.log("SMS sent:", success);
  } else {
    console.error("Invalid phone number");
  }
}

// Example 2: Send templated SMS with audit logging
async function sendPayslipNotification() {
  const phone = "0502466626";
  const employeeId = "employee-uuid-here";
  const tenantId = "tenant-uuid-here";
  const userId = "user-uuid-here";
  const userEmail = "<EMAIL>";
  
  const variables = {
    firstName: "יוסי",
    month: "12",
    year: "2024"
  };
  
  if (validateIsraeliPhoneNumber(phone)) {
    const formattedPhone = formatPhoneForSMS(phone);
    const success = await sendTemplatedSMS(
      formattedPhone,
      SMS_TEMPLATES.PAYSLIP_READY,
      variables,
      employeeId,
      tenantId,
      userId,
      userEmail
    );
    console.log("Payslip notification sent:", success);
  }
}

// Example 3: Send bulk SMS with audit logging
async function sendBulkNotifications() {
  const tenantId = "tenant-uuid-here";
  const userId = "user-uuid-here";
  const userEmail = "<EMAIL>";
  
  const recipients = [
    {
      phone: formatPhoneForSMS("0502466626"),
      employeeId: "employee1-uuid",
      variables: { firstName: "יוסי", month: "12", year: "2024" }
    },
    {
      phone: formatPhoneForSMS("0502345678"),
      employeeId: "employee2-uuid",
      variables: { firstName: "דני", month: "12", year: "2024" }
    }
  ];
  
  const results = await sendBulkSMS(
    recipients,
    SMS_TEMPLATES.PAYSLIP_READY,
    tenantId,
    userId,
    userEmail
  );
  
  console.log("Bulk SMS results:", results);
}

// Example 4: Integration with your application (with user context)
export async function notifyEmployeePayslipReady(
  employeeId: string,
  tenantId: string,
  employeePhone: string,
  employeeName: string,
  month: number,
  year: number,
  userId?: string,
  userEmail?: string
) {
  if (!validateIsraeliPhoneNumber(employeePhone)) {
    console.error(`Invalid phone number for employee ${employeeId}`);
    return false;
  }
  
  const formattedPhone = formatPhoneForSMS(employeePhone);
  const variables = {
    firstName: employeeName,
    month: month.toString(),
    year: year.toString()
  };
  
  return await sendTemplatedSMS(
    formattedPhone,
    SMS_TEMPLATES.PAYSLIP_READY,
    variables,
    employeeId,
    tenantId,
    userId,
    userEmail
  );
}

// Example 5: Integration with tRPC or API route
export async function sendSmsFromApi(
  ctx: { session: { user: { id: string; email: string } } },
  input: {
    employeeId: string;
    tenantId: string;
    message: string;
    phone: string;
  }
) {
  const { employeeId, tenantId, message, phone } = input;
  const { id: userId, email: userEmail } = ctx.session.user;
  
  if (!validateIsraeliPhoneNumber(phone)) {
    throw new Error("Invalid phone number");
  }
  
  const formattedPhone = formatPhoneForSMS(phone);
  
  const success = await sendMessage(
    formattedPhone,
    message,
    employeeId,
    tenantId,
    userId,
    userEmail
  );
  
  return { success };
} 