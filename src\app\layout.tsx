import "@/styles/globals.css";

import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "@/components/ui/sonner";

import { TRPCReactProvider } from "@/trpc/react";

export const metadata: Metadata = {
	title: "SMARTCHI",
	description: "Israeli payroll management system",
	icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default function RootLayout({
	children,
}: Readonly<{ children: React.ReactNode }>) {
	return (
		<html lang="he" dir="rtl" suppressHydrationWarning>
			<body className="to-background text-foreground relative ">
				<TRPCReactProvider>
					<SessionProvider>
					
							{children}
							<Toaster richColors />
						
					</SessionProvider>
				</TRPCReactProvider>
			</body>
		</html>
	);
}
