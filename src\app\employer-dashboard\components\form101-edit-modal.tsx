import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/rtl-components";
import { Form, FormField, FormItem, FormLabel, FormControl } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { type Form101, type MaritalStatus } from "@prisma/client";

// Form schema for editing Form 101
export const form101EditSchema = z.object({
  maritalStatus: z.enum(['SINGLE', 'MARRIED', 'DIVORCED', 'WIDOWED'] as const),
  spouseWorks: z.boolean(),
  childrenCount: z.number().min(0),
  childrenUnder5: z.number().min(0),
  childrenUnder18: z.number().min(0),
  isMainEmployer: z.boolean(),
  hasAdditionalIncome: z.boolean(),
  additionalCreditPoints: z.number().min(0).optional(),
  taxCoordinationPercent: z.number().min(0).max(100).optional(),
  overrideTaxRate: z.number().min(0).max(100).optional(),
});

export type Form101EditData = z.infer<typeof form101EditSchema>;

interface Form101EditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Form101EditData) => Promise<void>;
  form101Data: Form101;
}

export function Form101EditModal({ isOpen, onClose, onSave, form101Data }: Form101EditModalProps) {
  // Default values for the form based on the current form101 data
  const defaultValues: Form101EditData = {
    maritalStatus: (form101Data.maritalStatus as MaritalStatus) || 'SINGLE',
    spouseWorks: form101Data.spouseWorks || false,
    childrenCount: form101Data.childrenCount || 0,
    childrenUnder5: form101Data.childrenUnder5 || 0,
    childrenUnder18: form101Data.childrenUnder18 || 0,
    isMainEmployer: form101Data.isMainEmployer || true,
    hasAdditionalIncome: form101Data.hasAdditionalIncome || false,
    additionalCreditPoints: form101Data.additionalCreditPoints ? Number(form101Data.additionalCreditPoints) : 0,
    taxCoordinationPercent: form101Data.taxCoordinationNumber ? 
      Number(form101Data.taxCoordinationNumber) : undefined,
    overrideTaxRate: form101Data.overrideTaxRate ? Number(form101Data.overrideTaxRate) : undefined,
  };

  const form = useForm<Form101EditData>({
    resolver: zodResolver(form101EditSchema),
    defaultValues
  });

  const handleSubmit = async (data: Form101EditData) => {
    await onSave(data);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>עריכת טופס 101</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="maritalStatus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>מצב משפחתי</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="בחר מצב משפחתי" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="SINGLE">רווק/ה</SelectItem>
                      <SelectItem value="MARRIED">נשוי/אה</SelectItem>
                      <SelectItem value="DIVORCED">גרוש/ה</SelectItem>
                      <SelectItem value="WIDOWED">אלמן/ה</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="spouseWorks"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>בן/בת זוג עובד/ת</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="childrenCount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>מספר ילדים</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      {...field}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="childrenUnder5"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ילדים עד גיל 5</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      {...field}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="childrenUnder18"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>ילדים עד גיל 18</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      {...field}
                      onChange={e => field.onChange(Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isMainEmployer"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>מעסיק עיקרי</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hasAdditionalIncome"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>הכנסה נוספת</FormLabel>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="additionalCreditPoints"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>נקודות זיכוי נוספות</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      step={0.25}
                      {...field}
                      value={field.value ?? ''}
                      onChange={e => field.onChange(e.target.value === '' ? undefined : Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="taxCoordinationPercent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>אחוז תיאום מס (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      max={100}
                      {...field}
                      value={field.value ?? ''}
                      onChange={e => field.onChange(e.target.value === '' ? undefined : Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="overrideTaxRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>שיעור מס קבוע (%)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min={0}
                      max={100}
                      {...field}
                      value={field.value ?? ''}
                      onChange={e => field.onChange(e.target.value === '' ? undefined : Number(e.target.value))}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
              >
                ביטול
              </Button>
              <Button 
                type="submit" 
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? "שומר..." : "שמור"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 