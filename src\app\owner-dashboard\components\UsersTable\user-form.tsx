"use client";

import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/rtl-components";
import { DialogFooter } from "@/components/ui/dialog";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, type SubmitHandler } from "react-hook-form";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { userFormSchema, type UserFormValues, Role } from "./types";
import { EmployerAutocomplete } from "./employer-autocomplete";
import React from "react";

type UserFormProps = {
  onSubmit?: (data: UserFormValues) => void;
  isSubmitting?: boolean;
  initialData?: Partial<UserFormValues>;
};

// מיפוי של תפקידים לתצוגה בעברית
const roleLabels: Record<Role, string> = {
  [Role.OWNER]: "בעלים",
  [Role.ADMIN]: "מנהל מערכת",
  [Role.ACCOUNTANT]: "רואה חשבון",
  [Role.HR]: "משאבי אנוש",
  [Role.EMPLOYEE]: "עובד"
};

export function UserForm({ 
  onSubmit, 
  isSubmitting = false,
  initialData
}: UserFormProps) {
  // יצירת רשימת תפקידים לתצוגה
  const roleOptions = Object.entries(roleLabels).map(([role, label]) => ({
    value: role,
    label
  }));

  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      email: initialData?.email || "",
      role: initialData?.role || Role.EMPLOYEE,
      employerId: initialData?.employerId || "",
      status: initialData?.status || "active",
    },
  });

  // תצוגת מידע למטרות בדיקה
  React.useEffect(() => {
    console.log("UserForm - Form values:", form.getValues());
  }, [form]);

  const handleSubmit: SubmitHandler<UserFormValues> = (data) => {
    console.log("Submitting form with data:", data);
    if (onSubmit) {
      onSubmit(data);
      form.reset(); // Reset form after submission
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>שם המשתמש</FormLabel>
              <FormControl>
                <Input placeholder="הזן שם משתמש" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>דוא&quot;ל</FormLabel>
              <FormControl>
                <Input placeholder="הזן כתובת דוא&quot;ל" type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>תפקיד</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isSubmitting}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="בחר תפקיד" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {roleOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="employerId"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>מעסיק</FormLabel>
              <FormControl>
                <EmployerAutocomplete
                  selectedEmployerId={field.value}
                  onSelect={field.onChange}
                  disabled={isSubmitting}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem className="text-right">
              <FormLabel>סטטוס</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isSubmitting}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="בחר סטטוס" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="active">פעיל</SelectItem>
                  <SelectItem value="inactive">לא פעיל</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <DialogFooter className="mt-4">
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "מוסיף..." : "הוסף משתמש"}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
} 