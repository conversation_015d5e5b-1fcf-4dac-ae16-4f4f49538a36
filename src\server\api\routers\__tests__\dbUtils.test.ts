import { describe, it, expect, vi } from 'vitest';
process.env.SKIP_ENV_VALIDATION = 'true';
import { getOrCreate, findFirstOrCreate } from '@/server/utils/db';

const baseTx = () => ({
  user: {
    upsert: vi.fn(),
    findFirst: vi.fn(),
    create: vi.fn(),
  },
});

describe('getOrCreate', () => {
  it('creates a record when none exists', async () => {
    const tx = baseTx();
    tx.user.upsert.mockResolvedValue({ id: 'u1' });
    tx.user.findFirst.mockResolvedValue([]);

    const res = await getOrCreate(tx as any, 'user', { id: 'u1' }, { id: 'u1' });
    expect(tx.user.upsert).toHaveBeenCalled();
    expect(res._created).toBe(true);
  });

  it('returns existing record when found', async () => {
    const tx = baseTx();
    tx.user.upsert.mockResolvedValue({ id: 'u1' });
    tx.user.findFirst.mockResolvedValue([{ id: 'u1' }, { id: 'u2' }]);

    const res = await getOrCreate(tx as any, 'user', { id: 'u1' }, { id: 'u1' });
    expect(res._created).toBe(false);
  });
});

describe('findFirstOrCreate', () => {
  it('returns existing record if present', async () => {
    const tx = baseTx();
    tx.user.findFirst.mockResolvedValue({ id: 'u1' });

    const res = await findFirstOrCreate(tx as any, 'user', { id: 'u1' }, { id: 'u1' });
    expect(res).toEqual({ id: 'u1' });
  });

  it('creates when not found', async () => {
    const tx = baseTx();
    tx.user.findFirst.mockResolvedValueOnce(null).mockResolvedValueOnce(null);
    tx.user.create.mockResolvedValue({ id: 'u1' });

    const res = await findFirstOrCreate(tx as any, 'user', { id: 'u1' }, { id: 'u1' });
    expect(tx.user.create).toHaveBeenCalled();
    expect(res._created).toBe(true);
  });

  it('handles race condition', async () => {
    const tx = baseTx();
    tx.user.findFirst.mockResolvedValueOnce(null).mockResolvedValueOnce({ id: 'u1' });
    tx.user.create.mockRejectedValue(new Error('duplicate'));

    const res = await findFirstOrCreate(tx as any, 'user', { id: 'u1' }, { id: 'u1' });
    expect(res).toEqual({ id: 'u1' });
  });
});
