"use client";

import { useState } from "react";
import { Save } from "lucide-react";

import { Button } from "@/components/ui/rtl-components";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/rtl-components";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>Content, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/rtl-components";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  RadioGroup, 
  RadioGroupItem 
} from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";

export default function PreferencesPage() {
  const [colorTheme, setColorTheme] = useState<string>("system");
  const [fontFamily, setFontFamily] = useState<string>("heebo");
  const [decimals, setDecimals] = useState<string>("2");
  const [afterSaveAction, setAfterSaveAction] = useState<string>("stay");
  const [employeeNameDisplay, setEmployeeNameDisplay] = useState<string>("firstLast");
  
  // ערכים של העדפות כלליות למשתמש
  const generalPreferences = {
    notifications: {
      payslipReady: true,
      reportDeadlines: true,
      systemUpdates: true,
      newEmployees: false
    },
    dateFormat: "dd/mm/yyyy",
    startDayOfWeek: "sunday",
    language: "he"
  };
  
  // ערכים עבור העדפות תצוגה
  const displayPreferences = {
    fontSize: 16,
    lineHeight: 1.5,
    spacing: 4,
    darkMode: false,
    highContrast: false,
    animations: true
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // כאן יהיה קוד לשמירת ההעדפות
    alert("ההעדפות נשמרו בהצלחה");
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">העדפות</h1>

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="general">הגדרות כלליות</TabsTrigger>
            <TabsTrigger value="account">חשבון</TabsTrigger>
            <TabsTrigger value="display">תצוגה</TabsTrigger>
            <TabsTrigger value="notifications">התראות</TabsTrigger>
          </TabsList>
          
          {/* טאב הגדרות כלליות */}
          <TabsContent value="general">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>העדפות כלליות</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="decimals">מספר ספרות אחרי הנקודה</Label>
                    <Select value={decimals} onValueChange={setDecimals}>
                      <SelectTrigger id="decimals">
                        <SelectValue placeholder="בחר מספר ספרות" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0 ספרות</SelectItem>
                        <SelectItem value="1">ספרה אחת</SelectItem>
                        <SelectItem value="2">2 ספרות</SelectItem>
                        <SelectItem value="3">3 ספרות</SelectItem>
                        <SelectItem value="4">4 ספרות</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="afterSaveAction">התנהגות לאחר שמירה</Label>
                    <Select value={afterSaveAction} onValueChange={setAfterSaveAction}>
                      <SelectTrigger id="afterSaveAction">
                        <SelectValue placeholder="בחר התנהגות" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="stay">הישאר במסך הנוכחי</SelectItem>
                        <SelectItem value="new">עבור לרשומה חדשה</SelectItem>
                        <SelectItem value="list">עבור לרשימה</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="employeeNameDisplay">תצוגת שמות עובדים</Label>
                    <Select value={employeeNameDisplay} onValueChange={setEmployeeNameDisplay}>
                      <SelectTrigger id="employeeNameDisplay">
                        <SelectValue placeholder="בחר תצוגה" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="firstLast">שם פרטי שם משפחה</SelectItem>
                        <SelectItem value="lastFirst">שם משפחה, שם פרטי</SelectItem>
                        <SelectItem value="firstOnly">שם פרטי בלבד</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="dateFormat">תבנית תאריך</Label>
                    <RadioGroup
                      defaultValue={generalPreferences.dateFormat}
                      className="pt-2"
                    >
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="dd/mm/yyyy" id="dateFormat1" />
                        <Label htmlFor="dateFormat1">DD/MM/YYYY</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="mm/dd/yyyy" id="dateFormat2" />
                        <Label htmlFor="dateFormat2">MM/DD/YYYY</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="yyyy/mm/dd" id="dateFormat3" />
                        <Label htmlFor="dateFormat3">YYYY/MM/DD</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="startDayOfWeek">יום התחלת שבוע</Label>
                    <RadioGroup
                      defaultValue={generalPreferences.startDayOfWeek}
                      className="pt-2"
                    >
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="sunday" id="startDay1" />
                        <Label htmlFor="startDay1">יום ראשון</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="monday" id="startDay2" />
                        <Label htmlFor="startDay2">יום שני</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="saturday" id="startDay3" />
                        <Label htmlFor="startDay3">יום שבת</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>העדפות תצוגה</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="colorTheme">ערכת צבעים</Label>
                    <Select value={colorTheme} onValueChange={setColorTheme}>
                      <SelectTrigger id="colorTheme">
                        <SelectValue placeholder="בחר ערכת צבעים" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">בהיר</SelectItem>
                        <SelectItem value="dark">כהה</SelectItem>
                        <SelectItem value="system">לפי הגדרות מערכת</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="fontFamily">גופן</Label>
                    <Select value={fontFamily} onValueChange={setFontFamily}>
                      <SelectTrigger id="fontFamily">
                        <SelectValue placeholder="בחר גופן" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="heebo">Heebo</SelectItem>
                        <SelectItem value="openSans">Open Sans</SelectItem>
                        <SelectItem value="assistant">Assistant</SelectItem>
                        <SelectItem value="rubik">Rubik</SelectItem>
                        <SelectItem value="davidLibre">David Libre</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-4">
                    <Label>גודל גופן</Label>
                    <Slider
                      defaultValue={[displayPreferences.fontSize]}
                      min={12}
                      max={24}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>קטן</span>
                      <span>בינוני</span>
                      <span>גדול</span>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <Label>ריווח</Label>
                    <Slider
                      defaultValue={[displayPreferences.spacing]}
                      min={1}
                      max={8}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>צפוף</span>
                      <span>רגיל</span>
                      <span>מרווח</span>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Checkbox 
                        id="darkMode" 
                        defaultChecked={displayPreferences.darkMode} 
                      />
                      <Label htmlFor="darkMode" className="cursor-pointer">
                        מצב כהה
                      </Label>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Checkbox 
                        id="highContrast" 
                        defaultChecked={displayPreferences.highContrast} 
                      />
                      <Label htmlFor="highContrast" className="cursor-pointer">
                        ניגודיות גבוהה
                      </Label>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Checkbox 
                        id="animations" 
                        defaultChecked={displayPreferences.animations} 
                      />
                      <Label htmlFor="animations" className="cursor-pointer">
                        הפעל אנימציות
                      </Label>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* טאב חשבון */}
          <TabsContent value="account">
            <Card>
              <CardHeader>
                <CardTitle>פרטי חשבון</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPassword">סיסמה נוכחית</Label>
                    <Input id="currentPassword" type="password" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="newPassword">סיסמה חדשה</Label>
                    <Input id="newPassword" type="password" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="confirmPassword">אימות סיסמה</Label>
                    <Input id="confirmPassword" type="password" />
                  </div>
                  
                  <Button className="mt-2" type="button">
                    עדכן סיסמה
                  </Button>
                </div>
                
                <div className="my-6 border-t pt-6">
                  <div className="space-y-2">
                    <Label htmlFor="email">כתובת דואר אלקטרוני</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  
                  <Button className="mt-2" type="button">
                    עדכן דואר אלקטרוני
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* טאב התראות */}
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>הגדרות התראות</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">התראות מערכת</h3>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="payslipReady" 
                      defaultChecked={generalPreferences.notifications.payslipReady} 
                    />
                    <Label htmlFor="payslipReady" className="cursor-pointer">
                      תלוש שכר מוכן
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="reportDeadlines" 
                      defaultChecked={generalPreferences.notifications.reportDeadlines} 
                    />
                    <Label htmlFor="reportDeadlines" className="cursor-pointer">
                      תזכורות מועדי דיווח
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="systemUpdates" 
                      defaultChecked={generalPreferences.notifications.systemUpdates} 
                    />
                    <Label htmlFor="systemUpdates" className="cursor-pointer">
                      עדכוני מערכת
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="newEmployees" 
                      defaultChecked={generalPreferences.notifications.newEmployees} 
                    />
                    <Label htmlFor="newEmployees" className="cursor-pointer">
                      עובדים חדשים
                    </Label>
                  </div>
                </div>
                
                <div className="space-y-4 pt-4 border-t">
                  <h3 className="text-lg font-medium">אפשרויות משלוח</h3>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox id="emailNotifications" defaultChecked={true} />
                    <Label htmlFor="emailNotifications" className="cursor-pointer">
                      קבל התראות בדואר אלקטרוני
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox id="smsNotifications" defaultChecked={false} />
                    <Label htmlFor="smsNotifications" className="cursor-pointer">
                      קבל התראות ב-SMS
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox id="appNotifications" defaultChecked={true} />
                    <Label htmlFor="appNotifications" className="cursor-pointer">
                      קבל התראות באפליקציה
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* טאב תצוגה */}
          <TabsContent value="display">
            <Card>
              <CardHeader>
                <CardTitle>הגדרות תצוגה</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">צבעים</h3>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div className="flex flex-col items-center">
                      <div className="h-10 w-10 rounded-full bg-primary"></div>
                      <span className="mt-1 text-sm">צבע ראשי</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-10 w-10 rounded-full bg-secondary"></div>
                      <span className="mt-1 text-sm">צבע משני</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-10 w-10 rounded-full bg-accent"></div>
                      <span className="mt-1 text-sm">צבע הדגשה</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-10 w-10 rounded-full bg-destructive"></div>
                      <span className="mt-1 text-sm">צבע שגיאה</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-10 w-10 rounded-full bg-muted"></div>
                      <span className="mt-1 text-sm">צבע מעומעם</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <div className="h-10 w-10 rounded-full bg-card"></div>
                      <span className="mt-1 text-sm">צבע כרטיסיה</span>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-6 space-y-4">
                  <h3 className="text-lg font-medium">אפשרויות תצוגה נוספות</h3>
                  
                  <div className="space-y-2">
                    <Label>צפיפות תצוגת טבלאות</Label>
                    <RadioGroup
                      defaultValue="medium"
                      className="pt-2"
                    >
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="compact" id="density1" />
                        <Label htmlFor="density1">דחוסה</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="medium" id="density2" />
                        <Label htmlFor="density2">בינונית</Label>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <RadioGroupItem value="relaxed" id="density3" />
                        <Label htmlFor="density3">מרווחת</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-end mt-8">
          <Button type="submit">
            <Save className="ml-2 h-4 w-4" />
            שמור העדפות
          </Button>
        </div>
      </form>
    </div>
  );
} 