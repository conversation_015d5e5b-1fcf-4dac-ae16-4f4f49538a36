"use client";

import { CardContent } from "@/components/ui/card";
import { Table, TableBody, TableHead, TableHeader, TableRow } from "@/components/ui/rtl-components";
import { UserTableRow } from "./user-table-row";
import { UsersTableSkeleton } from "./users-table-skeleton";
import { type User } from "./types";

type UsersTableViewProps = {
  users?: User[];
  isLoading: boolean;
};

export function UsersTableView({ users, isLoading }: UsersTableViewProps) {
  if (isLoading) {
    return (
      <CardContent className="p-0">
        <UsersTableSkeleton />
      </CardContent>
    );
  }

  if (!users || users.length === 0) {
    return (
      <CardContent className="p-0">
        <div className="p-4">אין משתמשים זמינים</div>
      </CardContent>
    );
  }

  // Check if we have valid employer information
  const hasEmployerInfo = users.some(user => user.employerName && user.employerName.trim() !== "");

  return (
    <CardContent className="p-0">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>שם</TableHead>
            <TableHead>דוא&quot;ל</TableHead>
            <TableHead>תפקיד</TableHead>
            {hasEmployerInfo && (
              <TableHead className="font-bold text-primary">שם העסק</TableHead>
            )}
            <TableHead>סטטוס</TableHead>
            <TableHead>פעולות</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <UserTableRow 
              key={user.id} 
              user={user} 
              showEmployer={hasEmployerInfo}
            />
          ))}
        </TableBody>
      </Table>
    </CardContent>
  );
} 