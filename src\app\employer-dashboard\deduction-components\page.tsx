"use client";

import { useState } from "react";
import { useDeductionComponents } from "./hooks";
import DeductionComponentsTable from "./components/DeductionComponentsTable";
import DeductionFormModal from "./components/DeductionFormModal";
import ChangeReportModal from "./components/ChangeReportModal";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, FileText } from "lucide-react";
import type { DeductionComponent, DeductionComponentFilters } from "./types";

export default function DeductionComponentsPage() {
  const [filters, setFilters] = useState<DeductionComponentFilters>({});
  const [selectedComponent, setSelectedComponent] = useState<DeductionComponent | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isReportOpen, setIsReportOpen] = useState(false);

  const { data: components = [], isLoading } = useDeductionComponents(filters);

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search }));
  };

  const handleAdd = () => {
    setSelectedComponent(null);
    setIsFormOpen(true);
  };

  const handleEdit = (component: DeductionComponent) => {
    setSelectedComponent(component);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setSelectedComponent(null);
    setIsFormOpen(false);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">מרכיבי ניכוי</h1>
        <Button
          onClick={() => setIsReportOpen(true)}
          variant="outline"
        >
          <FileText className="h-4 w-4 ml-2" />
          דוח שינויים
        </Button>
      </div>

      {/* Actions Bar */}
      <div className="flex gap-4 items-center">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="חיפוש לפי שם או קוד..."
              className="pr-10"
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 ml-2" />
          הוספת רכיב ניכוי
        </Button>
      </div>

      {/* Table */}
      <DeductionComponentsTable
        components={components.map(comp => ({
          ...comp,
          percentageOfSalary: comp.percentageOfSalary ? Number(comp.percentageOfSalary) : null
        }))}
        isLoading={isLoading}
        onEdit={handleEdit}
        onFiltersChange={setFilters}
        filters={filters}
      />

      {/* Modals */}
      <DeductionFormModal
        open={isFormOpen}
        onClose={handleCloseForm}
        deductionComponent={selectedComponent}
      />

      <ChangeReportModal
        open={isReportOpen}
        onClose={() => setIsReportOpen(false)}
      />
    </div>
  );
} 
