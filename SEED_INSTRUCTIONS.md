# Seed Data Instructions

## Overview
We have created two seed files to populate the database with test data:

1. **`scripts/seed.ts`** - Original seed file with basic data
2. **`scripts/seed-enhanced.ts`** - Enhanced seed file with comprehensive payroll test scenarios

**Note:** Both seed files automatically delete all existing data before creating new data, ensuring a clean state.

## Running the Enhanced Seed

The enhanced seed file includes all the payroll improvements and creates comprehensive test scenarios to validate the system.

### Prerequisites
1. Ensure your database is running
2. Ensure all dependencies are installed: `npm install`
3. Ensure Prisma client is generated: `npx prisma generate`

### To run the enhanced seed:

```bash
# The seed will automatically clear existing data
npx tsx scripts/seed-enhanced.ts
```

**Note:** No need to manually reset the database - the seed script will delete all existing data automatically.

## Test Scenarios Created

The enhanced seed creates the following test scenarios:

### 1. **Visa Expiry Alerts**
- **So<PERSON><PERSON><PERSON> (FW001)**: Visa expires in 25 days → CRITICAL alert
- **<PERSON> (FW002)**: Visa already expired → CRITICAL compliance issue
- **<PERSON> (FW005)**: Visa expires in 45 days → HIGH alert

### 2. **Form 101 Compliance**
- **<PERSON><PERSON><PERSON>panadze (FW003)**: Missing Form 101 for 90+ days → CRITICAL alert
- **Aziz <PERSON>himov (FW005)**: Missing Form 101 → HIGH alert

### 3. **Overtime Violations**
- **Somchai Jaidee (FW001)**: 220 total hours with excessive overtime
- **Aziz Rahimov (FW005)**: 250 hours with 120 overtime hours → CRITICAL violation

### 4. **Minimum Wage Compliance**
- **Wei Zhang (FW002)**: 5,500 NIS (below minimum wage of 5,880)
- **Aziz Rahimov (FW005)**: 5,700 NIS (below minimum wage)

### 5. **Deposit Compliance (Foreign Workers)**
- **Vasile Popescu (FW004)**: Insufficient deposit (5% instead of 20%)
- **Aziz Rahimov (FW005)**: Insufficient deposit

### 6. **Unusual Deductions**
- **משה כהן (Israeli)**: Excessive deductions totaling 45% of gross salary

### 7. **Payslip Statuses**
Each employee has 4 payslips with different statuses:
- October 2024: `PAID`
- November 2024: `APPROVED`
- December 2024: `CALCULATED`
- January 2025: `DRAFT`

## Login Credentials

All users have the same password: `123456789`

### Available Users:
- **Owner**: `<EMAIL>`
- **Payroll Manager**: `<EMAIL>`

## Database Overview

After running the enhanced seed, you will have:
- **2 Tenants**: default, TALS
- **1 Employer**: אברהם טורגמן משאבי אנוש לעובדים זרים בבנייה בע"מ
- **2 Departments**: עובדי בניין זרים, הנהלה
- **6 Employees**: Mix of foreign workers and Israeli employees
- **24 Payslips**: 4 per employee with different statuses
- **Multiple Alerts**: Based on compliance issues

## Viewing the Data

To view the generated alerts and compliance issues:

```sql
-- View all alerts
SELECT * FROM "Alert" ORDER BY severity DESC, "createdAt" DESC;

-- View payslips by status
SELECT status, COUNT(*) FROM "Payslip" GROUP BY status;

-- View employees with compliance issues
SELECT e."firstName", e."lastName", e."visaExpiry", e."baseSalary"
FROM "Employee" e
WHERE e."isForeign" = true
AND (e."visaExpiry" < CURRENT_DATE + INTERVAL '90 days' 
     OR e."baseSalary" < 5880);
```

## Testing the Improvements

The seed data allows you to test:

1. **Alert System**: Check the alerts table for visa, Form 101, overtime, and deposit alerts
2. **Payroll Calculations**: Verify overtime calculations, tax calculations, and deductions
3. **Compliance Validation**: Test minimum wage, working hours, and foreign worker compliance
4. **Data Consistency**: Validate employee IDs, national IDs, and payslip calculations

## Resetting the Database

To reset and reseed the database:

```bash
# Reset database and run enhanced seed
npx prisma db push --force-reset && npx tsx scripts/seed-enhanced.ts
```

## Troubleshooting

If you encounter issues:

1. **Import errors**: Ensure all utility files are properly compiled
2. **Database connection**: Check your DATABASE_URL in .env
3. **Type errors**: Run `npx prisma generate` to regenerate Prisma client

## Next Steps

After seeding the database, you can:

1. Login to the application with the provided credentials
2. Navigate to the alerts dashboard to see compliance issues
3. Review payslips in different statuses
4. Test the payroll calculation features
5. Verify data validation is working correctly

The comprehensive test data ensures all edge cases and compliance scenarios are covered for thorough testing of the payroll system improvements. 