---
description: Development guidelines for the deduction components management screen
globs: src/app/(main)/deduction-components/**/*.{ts,tsx}
alwaysApply: true
---

# מסך מרכיבי ניכוי - הנחיות פיתוח

## **סקירה כללית**
מסך מרכיבי ניכוי מאפשר למנהל המערכת להגדיר, לערוך ולנהל את כל סוגי רכיבי הניכוי במערכת השכר. רכיבי ניכוי הם סכומים המופחתים משכר העובד.

## **מבנה קבצים**
```
deduction-components/
├── page.tsx                    # דף ראשי
├── types.ts                    # טיפוסים וקבועים
├── hooks.ts                    # React hooks
├── components/
│   ├── DeductionComponentsTable.tsx  # טבלת רכיבים
│   ├── DeductionFormModal.tsx        # טופס הוספה/עריכה
│   ├── ChangeReportModal.tsx         # דוח שינויים
│   └── BulkActionsBar.tsx           # פעולות מרובות
└── .cursorrules               # קובץ זה
```

## **טיפוסים עיקריים**

### DeductionComponent
```typescript
interface DeductionComponent {
  id: string;
  code: string;                    // קוד ייחודי
  name: string;                    // שם הניכוי
  description?: string;            // תיאור
  deductionType: DeductionType;   // סוג ניכוי
  taxCalculation: TaxCalculationType;
  socialSecurityCalculation: SocialSecurityCalculationType;
  affectsPension: boolean;        // קובע לקצבה ופיצויים
  percentageOfSalary?: number;     // אחוז מהמשכורת
  rewardCode?: string;             // קוד תגמול
  displayOrder: number;            // סדר תצוגה
  group?: string;                  // קבוצה
  isOneTime: boolean;              // חד פעמי
  isActive: boolean;               // פעיל
}
```

### סוגי ניכוי (DeductionType)
- `INCOME_TAX` - מס הכנסה
- `NATIONAL_INSURANCE` - ביטוח לאומי
- `HEALTH_INSURANCE` - ביטוח בריאות
- `PENSION` - פנסיה
- `EDUCATION_FUND` - קרן השתלמות
- `LOAN_REPAYMENT` - החזר הלוואה
- `OTHER` - אחר

## **פונקציונליות עיקרית**

### 1. תצוגת רכיבים
- טבלה עם כל רכיבי הניכוי
- מיון לפי עמודות
- חיפוש וסינון
- תצוגת סטטוס (פעיל/לא פעיל)

### 2. ניהול רכיבים
- הוספת רכיב חדש
- עריכת רכיב קיים
- שכפול רכיב
- מחיקת רכיב (עם בדיקת תלויות)

### 3. פעולות מתקדמות
- עדכון מרובה
- ייצוא לקובץ
- ייבוא מקובץ
- דוח שינויים

### 4. ולידציות
- בדיקת קוד ייחודי
- בדיקת שמות דומים
- אזהרות על אחוזים גבוהים
- בדיקת שימוש בתלושי שכר

## **API Endpoints (tRPC)**

### Queries
```typescript
// קבלת כל הרכיבים
getAll(filters?: DeductionComponentFilters)

// קבלת רכיב לפי ID
getById(id: string)

// ולידציה
validate(data: DeductionComponentFormData)

// סטטיסטיקות שימוש
getUsageStats()

// דוח שינויים
getChangeReport(fromDate: Date, toDate: Date)
```

### Mutations
```typescript
// יצירת רכיב
create(data: DeductionComponentFormData)

// עדכון רכיב
update(id: string, data: DeductionComponentFormData)

// מחיקת רכיב
delete(id: string)

// שכפול רכיב
duplicate(id: string)

// עדכון מרובה
bulkUpdate(ids: string[], updates: Partial<DeductionComponent>)

// ייצוא
export()

// ייבוא
import(components: DeductionComponentFormData[])
```

## **כללי עסקיים**

### 1. קוד רכיב
- חייב להיות ייחודי
- לא ניתן לשינוי אם בשימוש
- נוצר אוטומטית בשכפול

### 2. מחיקת רכיב
- לא ניתן למחוק רכיב בשימוש
- מחיקה רכה (soft delete)
- שמירת היסטוריה

### 3. חישובי מס
- `TAX_LIABLE` - חייב במס הכנסה
- `TAX_EXEMPT` - פטור ממס הכנסה

### 4. חישובי ביטוח לאומי
- `SS_LIABLE` - חייב בביטוח לאומי
- `SS_EXEMPT` - פטור מביטוח לאומי

## **UI/UX Guidelines**

### 1. טבלה
- עמודות ניתנות למיון
- הצגת אייקונים לסטטוס
- צבעים להבחנה בין סוגים
- פעולות נגישות לכל שורה

### 2. טופס
- ולידציה בזמן אמת
- הודעות שגיאה ברורות
- ברירות מחדל חכמות
- תצוגה מקדימה של השפעות

### 3. חיפוש וסינון
- חיפוש מיידי
- סינון לפי סוג
- סינון לפי קבוצה
- סינון לפי סטטוס

## **בדיקות נדרשות**

### 1. ולידציות
- [ ] קוד ייחודי
- [ ] שדות חובה
- [ ] טווחי ערכים
- [ ] תלויות

### 2. פונקציונליות
- [ ] CRUD מלא
- [ ] שכפול עובד
- [ ] מחיקה עם בדיקות
- [ ] עדכון מרובה

### 3. ביצועים
- [ ] טעינה מהירה
- [ ] חיפוש יעיל
- [ ] מיון חלק
- [ ] עדכונים אופטימיסטיים

## **הרחבות עתידיות**
1. **נוסחאות חישוב** - הגדרת נוסחאות מורכבות
2. **תנאים** - הגדרת תנאים להפעלת ניכוי
3. **קבוצות מורכבות** - היררכיה של קבוצות
4. **תבניות** - שמירת תבניות ניכויים
5. **היסטוריה** - מעקב אחר שינויים לאורך זמן

## **הערות חשובות**
- רכיבי ניכוי משפיעים ישירות על חישוב השכר
- שינויים משפיעים על כל העובדים
- יש לבדוק השפעות לפני שינויים
- חובה לשמור גיבויים לפני שינויים מהותיים 