import type { Role } from "@prisma/client";

export function mapRoleToHebrew(role: Role): string {
	switch (role) {
		case "OWNER":
			return "בעלים";
		case "ADMIN":
			return "מנהל";
		case "ACCOUNTANT":
			return "רואה חשבון";
		case "HR":
			return "משאבי אנוש";
		case "EMPLOYEE":
			return "עובד";
		default:
			return "לא ידוע";
	}
}

export function mapHebrewToRole(hebrewRole: string): Role {
	switch (hebrewRole) {
		case "בעלים":
			return "OWNER";
		case "מנהל":
			return "ADMIN";
		case "רואה חשבון":
			return "ACCOUNTANT";
		case "משאבי אנוש":
			return "HR";
		case "עובד":
			return "EMPLOYEE";
		default:
			return "EMPLOYEE"; // Default to employee
	}
}
