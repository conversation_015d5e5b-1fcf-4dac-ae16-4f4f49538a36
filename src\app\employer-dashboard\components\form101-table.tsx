import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/rtl-components";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  FileText, 
  Send, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  RefreshCw,
  Download,
  Eye,
  Edit,
  Search
} from "lucide-react";
import { format } from "date-fns";
import { he } from "date-fns/locale";
import { type Form101 } from "@prisma/client";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSendForm101ForSignature, useCancelForm101SignatureRequest } from "../hooks";
import { toast } from "sonner";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON><PERSON>ooter } from "@/components/ui/dialog";

interface Form101WithEmployee extends Form101 {
  employee: {
    id: string;
    firstName: string;
    lastName: string;
    nationalId: string;
    email: string | null;
    phone: string | null;
  };
}

interface Form101TableProps {
  forms: Form101WithEmployee[];
  year: number;
  onViewForm: (form: Form101WithEmployee) => void;
  onEditForm: (form: Form101WithEmployee) => void;
  onPreviewForm: (form: Form101WithEmployee) => void;
  isLoading?: boolean;
}

export function Form101Table({ 
  forms, 
  year, 
  onViewForm, 
  onEditForm, 
  onPreviewForm,
  isLoading = false 
}: Form101TableProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedForm, setSelectedForm] = useState<Form101WithEmployee | null>(null);
  const [showResendModal, setShowResendModal] = useState(false);
  
  const { sendForSignature, isSending } = useSendForm101ForSignature();
  const { cancelSignatureRequest, isCancelling } = useCancelForm101SignatureRequest();
  
  const isSendingOrCancelling = isSending || isCancelling;

  const handleSendForSignature = async () => {
    if (!selectedForm || !selectedForm.employee.email) return;

    try {
      await sendForSignature({
        form101Id: selectedForm.id,
        employeeEmail: selectedForm.employee.email,
        employeePhone: selectedForm.employee.phone || undefined
      });
      toast.success("הטופס נשלח לחתימה בהצלחה");
      setShowResendModal(false);
      setSelectedForm(null);
    } catch (error) {
      toast.error("שגיאה בשליחת הטופס לחתימה");
    }
  };

  const handleCancelAndResend = async () => {
    if (!selectedForm || !selectedForm.signatureRequestId) return;

    try {
      // Cancel current request
      await cancelSignatureRequest({ form101Id: selectedForm.id });
      // Send new request
      await handleSendForSignature();
      toast.success("הטופס נשלח מחדש בהצלחה");
      setShowResendModal(false);
    } catch (error) {
      toast.error("שגיאה בשליחה מחדש של הטופס");
    }
  };

  const handleResend = (form: Form101WithEmployee) => {
    setSelectedForm(form);
    setShowResendModal(true);
  };

  const getFormStatus = (form: Form101WithEmployee) => {
    if (form.signedAt) {
      return 'signed';
    }

    if (form.signatureRequestId) {
      return form.signatureRequestExpiresAt && form.signatureRequestExpiresAt < new Date()
        ? 'expired'
        : 'sent';
    }

    return 'draft';
  };

  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'signed':
        return <Badge variant="default" className="bg-green-600">נחתם</Badge>;
      case 'sent':
        return <Badge variant="secondary" className="bg-blue-600">ממתין לחתימה</Badge>;
      case 'expired':
        return <Badge variant="destructive">פג תוקף</Badge>;
      case 'draft':
        return <Badge variant="outline">טיוטה</Badge>;
      default:
        return null;
    }
  };

  const filteredForms = forms
    .filter(form => {
      const fullName = `${form.employee.firstName} ${form.employee.lastName}`;
      const searchMatch = searchQuery === "" || 
        fullName.toLowerCase().includes(searchQuery.toLowerCase()) || 
        form.employee.nationalId.includes(searchQuery);
      
      if (statusFilter === "all") return searchMatch;
      
      const formStatus = getFormStatus(form);
      return searchMatch && formStatus === statusFilter;
    });

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>טפסי 101 - {year}</CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center space-x-2 rtl:space-x-reverse">
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="סטטוס" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">הכל</SelectItem>
                  <SelectItem value="draft">טיוטה</SelectItem>
                  <SelectItem value="sent">ממתין לחתימה</SelectItem>
                  <SelectItem value="signed">נחתם</SelectItem>
                  <SelectItem value="expired">פג תוקף</SelectItem>
                </SelectContent>
              </Select>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="חיפוש עובד..."
                  className="pl-8 w-[200px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>שם העובד</TableHead>
                <TableHead>ת.ז.</TableHead>
                <TableHead>סטטוס</TableHead>
                <TableHead>תאריך עדכון</TableHead>
                <TableHead>פעולות</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredForms.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8">
                    {isLoading ? (
                      <span>טוען נתונים...</span>
                    ) : (
                      <span>לא נמצאו טפסים</span>
                    )}
                  </TableCell>
                </TableRow>
              ) : (
                filteredForms.map((form) => {
                  const status = getFormStatus(form);
                  const lastUpdateDate = 
                    form.signedAt || 
                    form.signatureRequestSentAt || 
                    form.updatedAt;
                  
                  return (
                    <TableRow key={form.id}>
                      <TableCell>
                        {form.employee.firstName} {form.employee.lastName}
                      </TableCell>
                      <TableCell>{form.employee.nationalId}</TableCell>
                      <TableCell>{renderStatusBadge(status)}</TableCell>
                      <TableCell>
                        {lastUpdateDate ? format(lastUpdateDate, 'dd/MM/yyyy', { locale: he }) : '-'}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => onPreviewForm(form)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {status === 'draft' && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => onEditForm(form)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {status === 'draft' && form.employee.email && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleResend(form)}
                              title="שלח לחתימה"
                            >
                              <Send className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {(status === 'sent' || status === 'expired') && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              onClick={() => handleResend(form)}
                              title="שלח שוב"
                            >
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {status === 'signed' && form.documentUrl && (
                            <Button 
                              variant="outline" 
                              size="sm" 
                              asChild
                            >
                              <a 
                                href={form.documentUrl} 
                                download 
                                target="_blank" 
                                rel="noopener noreferrer"
                                title="הורד טופס חתום"
                              >
                                <Download className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Resend Modal */}
      {selectedForm && (
        <Dialog open={showResendModal} onOpenChange={setShowResendModal}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>שליחה חוזרת של טופס 101</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <p>האם לשלוח שוב את טופס 101 לחתימה?</p>
              {getFormStatus(selectedForm) === 'sent' && (
                <p className="text-sm text-muted-foreground mt-2">
                  הטופס נשלח כבר ועדיין ממתין לחתימה. שליחה חוזרת תבטל את הבקשה הקודמת.
                </p>
              )}
            </div>
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowResendModal(false);
                  setSelectedForm(null);
                }}
                disabled={isSendingOrCancelling}
              >
                ביטול
              </Button>
              <Button 
                onClick={getFormStatus(selectedForm) === 'sent' ? handleCancelAndResend : handleSendForSignature} 
                disabled={isSendingOrCancelling}
              >
                {isSendingOrCancelling ? "שולח..." : "שלח שוב"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
} 