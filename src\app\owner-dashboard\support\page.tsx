"use client";

import { useState } from "react";
import { Send, RotateCcw, MessageSquareHeart } from "lucide-react";

import { Button } from "@/components/ui/rtl-components";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/rtl-components";

export default function SupportPage() {
  const [supportMessage, setSupportMessage] = useState("");
  const [supportSubject, setSupportSubject] = useState("");
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // כאן יהיה קוד לשליחת הודעת תמיכה
    alert("הודעת התמיכה נשלחה בהצלחה");
    setSupportMessage("");
    setSupportSubject("");
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold tracking-tight">תמיכה</h1>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>פנייה לתמיכה</CardTitle>
            <CardDescription>
              נתקלת בבעיה? שלח לנו הודעה ונחזור אליך בהקדם
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder="נושא הפנייה"
                  value={supportSubject}
                  onChange={(e) => setSupportSubject(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Textarea
                  placeholder="תיאור הבעיה..."
                  rows={8}
                  value={supportMessage}
                  onChange={(e) => setSupportMessage(e.target.value)}
                  required
                  className="resize-none"
                />
              </div>
              <Button type="submit" className="w-full">
                <Send className="ml-2 h-4 w-4" />
                שלח פנייה
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>פרטי יצירת קשר</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-[24px_1fr] gap-4">
                <span className="flex h-6 w-6 items-center justify-center rounded-full bg-muted">
                  <span className="text-xs">1</span>
                </span>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">מוקד תמיכה טלפוני</p>
                  <p className="text-sm text-muted-foreground">03-1234567</p>
                  <p className="text-xs text-muted-foreground">שעות פעילות: ימים א'-ה', 08:00-17:00</p>
                </div>
              </div>
              
              <div className="grid grid-cols-[24px_1fr] gap-4">
                <span className="flex h-6 w-6 items-center justify-center rounded-full bg-muted">
                  <span className="text-xs">2</span>
                </span>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">דואר אלקטרוני</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                  <p className="text-xs text-muted-foreground">זמן תגובה ממוצע: 24 שעות</p>
                </div>
              </div>
              
              <div className="grid grid-cols-[24px_1fr] gap-4">
                <span className="flex h-6 w-6 items-center justify-center rounded-full bg-muted">
                  <span className="text-xs">3</span>
                </span>
                <div className="space-y-1">
                  <p className="text-sm font-medium leading-none">צ'אט חי</p>
                  <p className="text-sm text-muted-foreground">זמין דרך האפליקציה</p>
                  <p className="text-xs text-muted-foreground">בשעות פעילות המוקד</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>כלים מהירים</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button className="w-full" variant="outline">
                <RotateCcw className="ml-2 h-4 w-4" />
                איפוס הדפדפן
              </Button>
              
              <Button className="w-full" variant="outline">
                <MessageSquareHeart className="ml-2 h-4 w-4" />
                המלצה על משכורשת
              </Button>
            </CardContent>
          </Card>
          
          <Alert>
            <AlertDescription>
              צוות התמיכה שלנו זמין גם בימי שישי בין השעות 08:00-13:00 לפניות דחופות בלבד.
            </AlertDescription>
          </Alert>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-bold">שאלות נפוצות</h2>
        <Separator />
        
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">איך אוכל לעדכן את פרטי המעסיק?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                כדי לעדכן את פרטי המעסיק, לחץ על "עדכון מעסיק" בסרגל הניווט העליון,
                בחר את המעסיק הרצוי מהרשימה הנפתחת ועדכן את הפרטים בטופס. לאחר העדכון, לחץ על "שמור שינויים".
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-base">כיצד להפיק תלושי שכר?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                להפקת תלושי שכר, לחץ על "עובדים" בתפריט הראשי, בחר את העובדים להם ברצונך להפיק תלושים,
                ולחץ על "הפק תלושים". המערכת תבצע חישוב אוטומטי ותציג את התלושים לבדיקה לפני אישור.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-base">איך להגדיר הרשאות למשתמשים?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                לניהול הרשאות, עבור למסך "משתמשים" תחת תפריט "הגדרות מערכת". בחר את המשתמש הרצוי
                ולחץ על "ערוך הרשאות". שם תוכל להגדיר את רמת הגישה והפעולות המותרות לכל משתמש.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-base">שכחתי את הסיסמה שלי, מה לעשות?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                במסך הכניסה למערכת, לחץ על "שכחתי סיסמה" והזן את כתובת הדואר האלקטרוני שלך.
                תקבל הוראות לאיפוס הסיסמה במייל. אם הבעיה נמשכת, פנה למוקד התמיכה.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 