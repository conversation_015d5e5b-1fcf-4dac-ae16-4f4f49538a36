"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2 } from "lucide-react";
import type { DeductionComponent } from "../types";
import {
  DeductionType,
  TaxCalculationType,
  SocialSecurityCalculationType,
  DEDUCTION_TYPE_LABELS,
  TAX_CALCULATION_LABELS,
  SOCIAL_SECURITY_LABELS,
  DEDUCTION_GROUP_LABELS,
} from "../types";
import {
  useCreateDeductionComponent,
  useUpdateDeductionComponent,
} from "../hooks";

const formSchema = z.object({
  code: z.string().min(1, "קוד חובה"),
  name: z.string().min(1, "שם חובה"),
  description: z.string().optional(),
  deductionType: z.nativeEnum(DeductionType),
  taxCalculation: z.nativeEnum(TaxCalculationType),
  socialSecurityCalculation: z.nativeEnum(SocialSecurityCalculationType),
  affectsPension: z.boolean().default(false),
  percentageOfSalary: z.number().min(0).max(100).optional(),
  rewardCode: z.string().optional(),
  displayOrder: z.number().default(0),
  group: z.string().optional(),
  isOneTime: z.boolean().default(false),
  isActive: z.boolean().default(true),
});

interface DeductionFormModalProps {
  open: boolean;
  onClose: () => void;
  deductionComponent?: DeductionComponent | null;
}

export default function DeductionFormModal({
  open,
  onClose,
  deductionComponent,
}: DeductionFormModalProps) {
  const isEdit = !!deductionComponent;
  const createMutation = useCreateDeductionComponent();
  const updateMutation = useUpdateDeductionComponent();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: "",
      name: "",
      description: "",
      deductionType: DeductionType.OTHER,
      taxCalculation: TaxCalculationType.TAX_LIABLE,
      socialSecurityCalculation: SocialSecurityCalculationType.SS_LIABLE,
      affectsPension: false,
      percentageOfSalary: undefined,
      rewardCode: "",
      displayOrder: 0,
      group: "",
      isOneTime: false,
      isActive: true,
    },
  });

  useEffect(() => {
    if (deductionComponent) {
      form.reset({
        code: deductionComponent.code,
        name: deductionComponent.name,
        description: deductionComponent.description || "",
        deductionType: deductionComponent.deductionType,
        taxCalculation: deductionComponent.taxCalculation,
        socialSecurityCalculation: deductionComponent.socialSecurityCalculation,
        affectsPension: deductionComponent.affectsPension,
        percentageOfSalary: deductionComponent.percentageOfSalary || undefined,
        rewardCode: deductionComponent.rewardCode || "",
        displayOrder: deductionComponent.displayOrder,
        group: deductionComponent.group || "",
        isOneTime: deductionComponent.isOneTime,
        isActive: deductionComponent.isActive,
      });
    } else {
      form.reset();
    }
  }, [deductionComponent, form]);

  const onSubmit = async (data: any) => {
    try {
      if (isEdit) {
        await updateMutation.mutateAsync({
          id: deductionComponent.id,
          ...data,
        });
      } else {
        await createMutation.mutateAsync(data);
      }
      onClose();
    } catch (error) {
      // Error is handled by the mutation hooks
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEdit ? "עריכת רכיב ניכוי" : "הוספת רכיב ניכוי"}
          </DialogTitle>
          <DialogDescription>
            {isEdit
              ? "עדכן את פרטי רכיב הניכוי"
              : "הזן את פרטי רכיב הניכוי החדש"}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>קוד רכיב *</FormLabel>
                    <FormControl>
                      <Input {...field} disabled={isEdit} />
                    </FormControl>
                    <FormDescription>
                      קוד ייחודי לזיהוי הרכיב
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>שם רכיב *</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>תיאור</FormLabel>
                  <FormControl>
                    <Textarea {...field} rows={3} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="deductionType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>סוג ניכוי *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(DEDUCTION_TYPE_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="group"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>קבוצה</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="בחר קבוצה" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(DEDUCTION_GROUP_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="taxCalculation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>חישוב מס *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(TAX_CALCULATION_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="socialSecurityCalculation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>חישוב ביטוח לאומי *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(SOCIAL_SECURITY_LABELS).map(
                          ([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="percentageOfSalary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>אחוז מהמשכורת</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        {...field}
                        onChange={(e) =>
                          field.onChange(
                            e.target.value ? parseFloat(e.target.value) : undefined
                          )
                        }
                      />
                    </FormControl>
                    <FormDescription>
                      אם הניכוי מחושב כאחוז מהמשכורת
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rewardCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>קוד תגמול</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormDescription>
                      קוד תגמול לדיווח למס הכנסה
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="displayOrder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>סדר תצוגה</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormDescription>
                    סדר הופעת הרכיב בתלוש השכר
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="affectsPension"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        קובע לקצבה ופיצויים
                      </FormLabel>
                      <FormDescription>
                        האם הרכיב משפיע על חישוב קצבה ופיצויי פיטורין
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isOneTime"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">ניכוי חד פעמי</FormLabel>
                      <FormDescription>
                        האם זהו ניכוי חד פעמי שלא חוזר על עצמו
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">פעיל</FormLabel>
                      <FormDescription>
                        האם הרכיב פעיל וזמין לשימוש
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={onClose}>
                ביטול
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
                {isEdit ? "עדכון" : "הוספה"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 
