import { z } from "zod";

export const associationTypeEnum = z.enum([
  "DEPARTMENT",
  "ROLE",
  "SALARY_TEMPLATE",
  "SALARY_AGREEMENT",
]);

export type AssociationType = z.infer<typeof associationTypeEnum>;

// Base schema for creating associations
export const createAssociationSchema = z.object({
  employeeId: z.string().uuid(),
  associationType: associationTypeEnum,
  departmentId: z.string().uuid().optional(),
  roleId: z.string().uuid().optional(),
  salaryTemplateId: z.string().uuid().optional(),
  salaryAgreementId: z.string().uuid().optional(),
  startDate: z.string().or(z.date()),
  endDate: z.string().or(z.date()).optional().nullable(),
  notes: z.string().optional().nullable(),
});

// Schema for updating associations
export const updateAssociationSchema = createAssociationSchema.partial().extend({
  id: z.string().uuid(),
});

// Schema for filtering associations
export const filterAssociationsSchema = z.object({
  employerId: z.string(),
  page: z.number().default(1),
  limit: z.number().default(10),
  associationType: associationTypeEnum.optional(),
  searchTerm: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// Schema for generating association changes report
export const associationReportSchema = z.object({
  employerId: z.string(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

// Schema for retrieving association history
export const associationHistorySchema = z.object({
  associationId: z.string(),
  employerId: z.string(),
}); 