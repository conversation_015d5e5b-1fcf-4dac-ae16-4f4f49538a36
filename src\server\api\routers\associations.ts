/**
 * Associations Router
 * 
 * This router provides endpoints to manage associations between employees and other entities
 * (departments, roles, salary templates, and salary agreements).
 * 
 * IMPLEMENTATION STATUS:
 * 
 * 1. COMPLETED: All CRUD operations for associations
 * 2. COMPLETED: Generation of association change reports
 * 3. COMPLETED: Data fetching endpoints for related entities
 * 4. ADDED: Association history tracking endpoint
 * 
 * KNOWN ISSUES:
 * 
 * 1. Type errors: The current implementation has TypeScript errors due to missing Prisma types.
 *    These errors will be resolved once proper Prisma migrations are run and the client is generated.
 * 
 * 2. Database schema: The database needs to be properly migrated to include the new models:
 *    - EmployeeRole
 *    - SalaryTemplate
 *    - SalaryAgreement
 *    - Association
 *    - AssociationChangeHistory
 * 
 * TO FINISH IMPLEMENTATION:
 * 
 * 1. Run proper Prisma migrations to apply schema changes
 *    - npx prisma migrate dev --name add_associations
 *    - npx prisma generate
 * 
 * 2. Resolve permission issues with Prisma client generation
 *    - May require restarting the application or using a different environment
 * 
 * 3. Update type annotations if needed once Prisma client is properly generated
 */

import { TRPCError } from "@trpc/server";
import { z } from "zod";
import { 
  createTRPCRouter, 
  protectedProcedure 
} from "../trpc";
import { 
  createAssociationSchema, 
  updateAssociationSchema, 
  filterAssociationsSchema,
  associationReportSchema,
  associationTypeEnum,
  associationHistorySchema
} from "../schemas/associations";
import { Prisma, type User } from "@prisma/client";

// Type augmentation for session user with tenant ID
type SessionUser = {
  id: string;
  role?: string;
  employerId?: string;
  employerName?: string;
  tenantId?: string;
} & User;

export const associationsRouter = createTRPCRouter({
  // Get all associations with filters
  getAll: protectedProcedure
    .input(filterAssociationsSchema)
    .query(async ({ ctx, input }) => {
      const { 
        employerId, 
        associationType, 
        searchTerm, 
        startDate, 
        endDate, 
        page = 1, 
        limit = 10 
      } = input;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Get tenant ID from the employer record instead of requiring it in the session
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      // Build filter conditions
      const where: any = {
        tenantId,
        employee: {
          employerId,
        },
      };

      // Add associationType filter if provided
      if (associationType) {
        where.associationType = associationType;
      }

      // Add date range filters if provided
      if (startDate) {
        where.startDate = {
          gte: new Date(startDate),
        };
      }

      if (endDate) {
        where.OR = [
          { endDate: null },
          { endDate: { gte: new Date(endDate) } },
        ];
      }

      // Add search term filter if provided
      if (searchTerm) {
        where.OR = [
          {
            employee: {
              OR: [
                { firstName: { contains: searchTerm, mode: "insensitive" } },
                { lastName: { contains: searchTerm, mode: "insensitive" } },
                { nationalId: { contains: searchTerm, mode: "insensitive" } },
              ],
            },
          },
          {
            department: {
              name: { contains: searchTerm, mode: "insensitive" },
            },
          },
          {
            role: {
              name: { contains: searchTerm, mode: "insensitive" },
            },
          },
          {
            salaryTemplate: {
              name: { contains: searchTerm, mode: "insensitive" },
            },
          },
          {
            salaryAgreement: {
              name: { contains: searchTerm, mode: "insensitive" },
            },
          },
        ];
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalCount = await prisma.association.count({ where });
      const totalPages = Math.ceil(totalCount / limit);

      // Get associations
      const associations = await prisma.association.findMany({
        where,
        include: {
          employee: true,
          department: true,
          role: true,
          salaryTemplate: true,
          salaryAgreement: true,
        },
        orderBy: { startDate: "desc" },
        skip,
        take: limit,
      });

      // Transform data for frontend
      const data = associations.map((association: any) => {
        let associatedEntityName = "";
        let associatedEntityId = "";

        switch (association.associationType) {
          case "DEPARTMENT":
            associatedEntityName = association.department?.name ?? "";
            associatedEntityId = association.departmentId ?? "";
            break;
          case "ROLE":
            associatedEntityName = association.role?.name ?? "";
            associatedEntityId = association.roleId ?? "";
            break;
          case "SALARY_TEMPLATE":
            associatedEntityName = association.salaryTemplate?.name ?? "";
            associatedEntityId = association.salaryTemplateId ?? "";
            break;
          case "SALARY_AGREEMENT":
            associatedEntityName = association.salaryAgreement?.name ?? "";
            associatedEntityId = association.salaryAgreementId ?? "";
            break;
          default:
            break;
        }

        return {
          id: association.id,
          employeeId: association.employeeId,
          employeeName: `${association.employee.firstName} ${association.employee.lastName}`,
          employeeIdentifier: association.employee.nationalId,
          associationType: association.associationType,
          associatedEntityId,
          associatedEntityName,
          startDate: association.startDate.toISOString().split("T")[0],
          endDate: association.endDate ? association.endDate.toISOString().split("T")[0] : null,
          notes: association.notes,
          createdAt: association.createdAt.toISOString(),
          updatedAt: association.updatedAt.toISOString(),
        };
      });

      return {
        data,
        meta: {
          total: totalCount,
          page,
          pageSize: limit,
          totalPages,
        },
      };
    }),

  // Create a new association
  create: protectedProcedure
    .input(createAssociationSchema)
    .mutation(async ({ ctx, input }) => {
      const {
        employeeId,
        associationType,
        departmentId,
        roleId,
        salaryTemplateId,
        salaryAgreementId,
        startDate,
        endDate,
        notes,
      } = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Get the employee to find the employer and tenant IDs
      const employee = await prisma.employee.findUnique({
        where: { id: employeeId },
        select: { 
          employerId: true,
        }
      });

      if (!employee) {
        throw new TRPCError({
          code: "NOT_FOUND", 
          message: "Employee not found"
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: employee.employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employee.employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Validate that the appropriate ID is provided based on associationType
      switch (associationType) {
        case "DEPARTMENT":
          if (!departmentId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Department ID is required for department associations",
            });
          }
          break;
        case "ROLE":
          if (!roleId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Role ID is required for role associations",
            });
          }
          break;
        case "SALARY_TEMPLATE":
          if (!salaryTemplateId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Salary template ID is required for salary template associations",
            });
          }
          break;
        case "SALARY_AGREEMENT":
          if (!salaryAgreementId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Salary agreement ID is required for salary agreement associations",
            });
          }
          break;
        default:
          break;
      }

      // Create the new association
      const association = await prisma.association.create({
        data: {
          tenantId,
          employeeId,
          associationType,
          departmentId,
          roleId,
          salaryTemplateId,
          salaryAgreementId,
          startDate: new Date(startDate),
          endDate: endDate ? new Date(endDate) : null,
          notes,
        },
      });

      // Create audit log entry
      await prisma.auditLog.create({
        data: {
          tenantId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email || "",
          action: "CREATE",
          modelName: "Association",
          recordId: association.id,
          newValues: {
            ...association,
          },
        },
      });

      return { success: true, id: association.id };
    }),

  // Update an existing association
  update: protectedProcedure
    .input(updateAssociationSchema)
    .mutation(async ({ ctx, input }) => {
      const {
        id,
        employeeId,
        associationType,
        departmentId,
        roleId,
        salaryTemplateId,
        salaryAgreementId,
        startDate,
        endDate,
        notes,
      } = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Get the existing association
      const existingAssociation = await prisma.association.findUnique({
        where: { id },
        include: { employee: true },
      });

      if (!existingAssociation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Association not found",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: existingAssociation.employee.employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== existingAssociation.employee.employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Prepare data for update
      const updatePayload: Record<string, any> = {};

      if (startDate) {
        updatePayload.startDate = new Date(startDate);
      }

      if (endDate !== undefined) {
        updatePayload.endDate = endDate ? new Date(endDate) : null;
      }

      if (notes !== undefined) {
        updatePayload.notes = notes;
      }

      // Update the association type and related entity ID if provided
      if (associationType) {
        updatePayload.associationType = associationType;

        // Reset all entity IDs first
        updatePayload.departmentId = null;
        updatePayload.roleId = null;
        updatePayload.salaryTemplateId = null;
        updatePayload.salaryAgreementId = null;

        // Set the appropriate entity ID based on the new association type
        switch (associationType) {
          case "DEPARTMENT":
            if (!departmentId) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Department ID is required for department associations",
              });
            }
            updatePayload.departmentId = departmentId;
            break;
          case "ROLE":
            if (!roleId) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Role ID is required for role associations",
              });
            }
            updatePayload.roleId = roleId;
            break;
          case "SALARY_TEMPLATE":
            if (!salaryTemplateId) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Salary template ID is required for salary template associations",
              });
            }
            updatePayload.salaryTemplateId = salaryTemplateId;
            break;
          case "SALARY_AGREEMENT":
            if (!salaryAgreementId) {
              throw new TRPCError({
                code: "BAD_REQUEST",
                message: "Salary agreement ID is required for salary agreement associations",
              });
            }
            updatePayload.salaryAgreementId = salaryAgreementId;
            break;
          default:
            break;
        }
      } else {
        // If association type is not changing, update the entity ID if provided
        if (departmentId && existingAssociation.associationType === "DEPARTMENT") {
          updatePayload.departmentId = departmentId;
        }

        if (roleId && existingAssociation.associationType === "ROLE") {
          updatePayload.roleId = roleId;
        }

        if (salaryTemplateId && existingAssociation.associationType === "SALARY_TEMPLATE") {
          updatePayload.salaryTemplateId = salaryTemplateId;
        }

        if (salaryAgreementId && existingAssociation.associationType === "SALARY_AGREEMENT") {
          updatePayload.salaryAgreementId = salaryAgreementId;
        }
      }

      // Update the association
      const updatedAssociation = await prisma.association.update({
        where: { id },
        data: updatePayload,
      });

      // Create audit log entry
      await prisma.auditLog.create({
        data: {
          tenantId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email || "",
          action: "UPDATE",
          modelName: "Association",
          recordId: id,
          oldValues: {
            ...existingAssociation,
          },
          newValues: {
            ...updatedAssociation,
          },
        },
      });

      return { success: true };
    }),

  // Delete an association
  delete: protectedProcedure
    .input(z.string().uuid())
    .mutation(async ({ ctx, input }) => {
      const id = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Get the existing association
      const existingAssociation = await prisma.association.findUnique({
        where: { id },
        include: { employee: true },
      });

      if (!existingAssociation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Association not found",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: existingAssociation.employee.employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== existingAssociation.employee.employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Delete the association
      await prisma.association.delete({
        where: { id },
      });

      // Create audit log entry
      await prisma.auditLog.create({
        data: {
          tenantId,
          userId: ctx.session.user.id,
          userEmail: ctx.session.user.email || "",
          action: "DELETE",
          modelName: "Association",
          recordId: id,
          oldValues: {
            ...existingAssociation,
          },
        },
      });

      return { success: true };
    }),

  // Get history for a specific association
  getHistory: protectedProcedure
    .input(associationHistorySchema)
    .query(async ({ ctx, input }) => {
      const { associationId, employerId } = input;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Get tenant ID from the employer record instead of requiring it in the session
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      // Get the association to verify it exists and belongs to the right tenant
      const association = await prisma.association.findUnique({
        where: { id: associationId },
        include: { employee: true }
      });

      if (!association || association.tenantId !== tenantId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Association not found",
        });
      }

      // Get history records for this association
      const history = await prisma.associationChangeHistory.findMany({
        where: { 
          associationId,
          tenantId
        },
        orderBy: { changeDate: "desc" },
        include: {
          changedBy: true
        }
      });

      // Transform history data for frontend
      const historyData = history.map((record: any) => ({
        id: record.id,
        changeType: record.changeType,
        changeDate: record.changeDate.toISOString(),
        employeeName: `${association.employee.firstName} ${association.employee.lastName}`,
        associationType: association.associationType,
        entityName: record.entityName,
        oldValue: record.oldValue,
        newValue: record.newValue,
        changedBy: record.changedBy ? `${record.changedBy.firstName} ${record.changedBy.lastName}` : "System",
      }));

      return {
        history: historyData
      };
    }),

  // Generate a report of association changes
  generateReport: protectedProcedure
    .input(associationReportSchema)
    .mutation(async ({ ctx, input }) => {
      const { employerId, startDate, endDate } = input;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Get tenant ID from the employer record instead of requiring it in the session
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      // Build filter for the report
      const where: any = {
        tenantId,
        association: {
          employee: {
            employerId
          }
        }
      };

      // Add date range filters if provided
      if (startDate) {
        where.changeDate = {
          gte: new Date(startDate)
        };
      }

      if (endDate) {
        where.changeDate = {
          ...(where.changeDate || {}),
          lte: new Date(endDate)
        };
      }

      // Get changes for the report
      const changes = await prisma.associationChangeHistory.findMany({
        where,
        orderBy: { changeDate: "desc" },
        include: {
          association: {
            include: {
              employee: true
            }
          },
          changedBy: true
        }
      });

      // Generate the report
      // In a real implementation, this would create a PDF or XLSX file
      // For now, we'll just return a mock URL
      
      const reportId = `report-${Date.now()}`;
      const downloadUrl = `/api/reports/${reportId}`;

      return {
        reportId,
        downloadUrl
      };
    }),

  // Get association by ID
  getById: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input }) => {
      const id = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      const association = await prisma.association.findUnique({
        where: { id },
        include: {
          employee: true,
          department: true,
          role: true,
          salaryTemplate: true,
          salaryAgreement: true,
        },
      });

      if (!association) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Association not found",
        });
      }

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== association.employee.employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Transform data for frontend
      let associatedEntityName = "";
      let associatedEntityId = "";

      switch (association.associationType) {
        case "DEPARTMENT":
          associatedEntityName = association.department?.name ?? "";
          associatedEntityId = association.departmentId ?? "";
          break;
        case "ROLE":
          associatedEntityName = association.role?.name ?? "";
          associatedEntityId = association.roleId ?? "";
          break;
        case "SALARY_TEMPLATE":
          associatedEntityName = association.salaryTemplate?.name ?? "";
          associatedEntityId = association.salaryTemplateId ?? "";
          break;
        case "SALARY_AGREEMENT":
          associatedEntityName = association.salaryAgreement?.name ?? "";
          associatedEntityId = association.salaryAgreementId ?? "";
          break;
        default:
          break;
      }

      return {
        id: association.id,
        employeeId: association.employeeId,
        employeeName: `${association.employee.firstName} ${association.employee.lastName}`,
        employeeIdentifier: association.employee.nationalId,
        associationType: association.associationType,
        associatedEntityId,
        associatedEntityName,
        startDate: association.startDate.toISOString().split("T")[0],
        endDate: association.endDate ? association.endDate.toISOString().split("T")[0] : null,
        notes: association.notes,
        createdAt: association.createdAt.toISOString(),
        updatedAt: association.updatedAt.toISOString(),
      };
    }),

  // Get all departments for a specific employer
  getDepartments: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input }) => {
      const employerId = input;
      
      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      const departments = await prisma.department.findMany({
        where: {
          employerId,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      return departments;
    }),

  // Get all employee roles for a specific employer
  getEmployeeRoles: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input }) => {
      const employerId = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      const roles = await prisma.employeeRole.findMany({
        where: {
          employerId,
          tenantId,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          description: true,
        },
      });

      return roles;
    }),

  // Get all salary templates for a specific employer
  getSalaryTemplates: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input }) => {
      const employerId = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      const templates = await prisma.salaryTemplate.findMany({
        where: {
          employerId,
          tenantId,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          description: true,
        },
      });

      return templates;
    }),

  // Get all salary agreements for a specific employer
  getSalaryAgreements: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input }) => {
      const employerId = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      const agreements = await prisma.salaryAgreement.findMany({
        where: {
          employerId,
          tenantId,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          description: true,
        },
      });

      return agreements;
    }),

  // Get all employees for a specific employer
  getEmployees: protectedProcedure
    .input(z.string().uuid())
    .query(async ({ ctx, input }) => {
      const employerId = input;

      // Access Prisma client with type assertion
      const prisma = ctx.db as any;

      // Ensure user has access to this employer
      if (
        ctx.session.user.role !== "OWNER" && 
        ctx.session.user.role !== "ADMIN" && 
        ctx.session.user.employerId !== employerId
      ) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to access this employer",
        });
      }

      // Get tenant ID from the employer record
      const employer = await prisma.employer.findUnique({
        where: { id: employerId },
        select: { tenantId: true }
      });

      if (!employer) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Employer not found",
        });
      }

      const tenantId = employer.tenantId;

      const employees = await prisma.employee.findMany({
        where: {
          employerId,
          tenantId,
          status: "ACTIVE",
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          nationalId: true,
          visaNumber: true,
        },
        orderBy: {
          firstName: "asc",
        },
      });

      return employees.map((employee: { id: string; firstName: string; lastName: string; nationalId: string; visaNumber: string | null }) => ({
        id: employee.id,
        firstName: employee.firstName,
        lastName: employee.lastName,
        nationalId: employee.nationalId,
        visaNumber: employee.visaNumber,
      }));
    }),
}); 