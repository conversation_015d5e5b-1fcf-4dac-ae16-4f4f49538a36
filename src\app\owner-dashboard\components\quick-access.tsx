"use client";

import { Building2, <PERSON>, Setting<PERSON>, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";

type QuickAccessProps = {
  isLoading?: boolean;
};

export function QuickAccess({ isLoading = false }: QuickAccessProps) {
  if (isLoading) {
    return <QuickAccessSkeleton />;
  }

  return (
    <div className="mt-6">
      <h2 className="text-xl font-semibold mb-4">גישה מהירה</h2>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Button className="h-24 flex flex-col items-center justify-center gap-2">
          <Settings className="h-6 w-6" />
          <span>הגדרות מערכת</span>
        </Button>
        <Button className="h-24 flex flex-col items-center justify-center gap-2" variant="outline">
          <Users className="h-6 w-6" />
          <span>ניהול משתמשים</span>
        </Button>
        <Button className="h-24 flex flex-col items-center justify-center gap-2" variant="outline">
          <Building2 className="h-6 w-6" />
          <span>הוסף מעסיק</span>
        </Button>
        <Button className="h-24 flex flex-col items-center justify-center gap-2" variant="outline">
          <HelpCircle className="h-6 w-6" />
          <span>תמיכה ועזרה</span>
        </Button>
      </div>
    </div>
  );
}

function QuickAccessSkeleton() {
  return (
    <div className="mt-6">
      <Skeleton className="h-7 w-40 mb-4" />
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-24 rounded-md" />
        ))}
      </div>
    </div>
  );
} 