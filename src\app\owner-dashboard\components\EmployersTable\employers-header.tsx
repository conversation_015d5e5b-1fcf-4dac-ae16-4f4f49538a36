"use client";

import { PlusCircle } from "lucide-react";
import { Button } from "@/components/ui/rtl-components";
import { 
  Dialog, 
  DialogTrigger 
} from "@/components/ui/dialog";
import { AddEmployerDialog } from "./add-employer-dialog";
import { type EmployerFormValues } from "./types";

type EmployersHeaderProps = {
  isCreating?: boolean;
  createEmployer?: (data: EmployerFormValues) => void;
};

export function EmployersHeader({ 
  isCreating = false, 
  createEmployer = () => console.warn("No createEmployer handler provided")
}: EmployersHeaderProps) {
  return (
    <div className="flex justify-between items-center">
      <h2 className="text-xl font-semibold">ניהול מעסיקים</h2>
      <Dialog>
        <DialogTrigger asChild>
          <Button disabled={isCreating}>
            <PlusCircle className="ml-2 h-4 w-4" />
            הוסף מעסיק
          </Button>
        </DialogTrigger>
        <AddEmployerDialog isCreating={isCreating} createEmployer={createEmployer} />
      </Dialog>
    </div>
  );
} 