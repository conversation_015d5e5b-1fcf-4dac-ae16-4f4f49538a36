'use client'
import Image from "next/image";
import { useState } from "react";

const screenshots = [
  {
    id: 1,
    title: "לוח בקרה ראשי",
    description: "צפייה מרוכזת בכל הנתונים החשובים למעסיק במסך אחד",
    image: "/screenshots/dashboard.png",
    alt: "לוח בקרה ראשי של המערכת",
  },
  {
    id: 2,
    title: "ניהול עובדים",
    description: "ממשק ידידותי לניהול רשימת העובדים, פרטיהם ותנאי העסקתם",
    image: "/screenshots/employees.png",
    alt: "מסך ניהול עובדים",
  },
  {
    id: 3,
    title: "חישוב שכר",
    description: "מערכת מתקדמת לחישוב שכר מדויק עם אפשרויות סימולציה והיסטוריה",
    image: "/screenshots/payroll.png",
    alt: "מסך חישוב שכר",
  },
  {
    id: 4,
    title: "ניהול עובדים זרים",
    description: "ממש<PERSON> ייעודי לניהול עובדים זרים בהתאם לדרישות החוק",
    image: "/screenshots/foreign-workers.png",
    alt: "מסך ניהול עובדים זרים",
  },
  {
    id: 5,
    title: "דוחות ואנליטיקה",
    description: "מגוון דוחות וכלי ניתוח נתונים לקבלת תובנות עסקיות",
    image: "/screenshots/reports.png",
    alt: "מסך דוחות ואנליטיקה",
  },
];

// Define a default screenshot to avoid undefined errors
const defaultScreenshot = {
  id: 0,
  title: "תצוגת מערכת",
  description: "מערכת שכר מתקדמת לניהול משאבי אנוש",
  image: "/screenshots/default.png",
  alt: "תצוגת ברירת מחדל של המערכת",
};

export function Screenshots() {
  const [activeIndex, setActiveIndex] = useState(0);
  
  // Make sure we have a valid screenshot
  const currentScreenshot = screenshots[activeIndex] ?? defaultScreenshot;
  
  return (
    <section className="py-16" id="screenshots">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-navy-800 mb-4">צפו במערכת בפעולה</h2>
          <p className="text-navy-600 max-w-2xl mx-auto">
            ממשק משתמש חדשני ואינטואיטיבי המאפשר ניהול מהיר ויעיל של מערך השכר
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Main screenshot display */}
          <div className="relative rounded-xl overflow-hidden shadow-2xl mb-8 aspect-video bg-gradient-to-br from-blue-100 to-blue-50 p-2">
            <div className="absolute top-0 left-0 w-full h-8 bg-gradient-to-r from-gray-200 to-gray-100 flex items-center px-4 rounded-t-xl">
              <div className="flex space-x-2 rtl:space-x-reverse">
                <div className="w-3 h-3 rounded-full bg-red-500"></div>
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
            </div>
            <div className="pt-6 rounded-xl overflow-hidden h-full bg-white">
              <div className="w-full h-full relative">
                {/* Placeholder for when no real images are available */}
                <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                  <div className="text-center p-8">
                    <svg className="w-16 h-16 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <h3 className="text-lg font-semibold text-navy-800">{currentScreenshot.title}</h3>
                    <p className="text-navy-600">{currentScreenshot.description}</p>
                  </div>
                </div>
                
                {/* Uncomment below when real images are available */}
                {/* <Image
                  src={currentScreenshot.image}
                  alt={currentScreenshot.alt}
                  fill
                  className="object-cover rounded-b-xl"
                /> */}
              </div>
            </div>
          </div>

          {/* Thumbnails */}
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
            {screenshots.map((screenshot, index) => (
              <button
                key={screenshot.id}
                onClick={() => setActiveIndex(index)}
                className={`rounded-lg overflow-hidden border-2 transition-all ${
                  activeIndex === index 
                    ? "border-blue-500 shadow-md" 
                    : "border-transparent hover:border-blue-300"
                }`}
              >
                <div className="aspect-video bg-gray-100 relative">
                  {/* Placeholder for when no real images are available */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  
                  {/* Uncomment below when real images are available */}
                  {/* <Image
                    src={screenshot.image}
                    alt={screenshot.alt}
                    fill
                    className="object-cover"
                  /> */}
                </div>
                <div className="p-2 text-center">
                  <h4 className="text-sm font-medium text-navy-800 truncate">{screenshot.title}</h4>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
} 