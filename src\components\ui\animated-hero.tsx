"use client";

import { useEffect, useMemo, useState } from "react";
import { motion } from "framer-motion";
import { MoveRight, PhoneCall, Check } from "lucide-react";
import { Button } from "@/components/ui/button";

function Hero() {
  const [titleNumber, setTitleNumber] = useState(0);
  const titles = useMemo(
    () => ["מדויק", "חכם", "יעיל", "אוטומטי", "בטוח"],
    []
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (titleNumber === titles.length - 1) {
        setTitleNumber(0);
      } else {
        setTitleNumber(titleNumber + 1);
      }
    }, 2000);
    return () => clearTimeout(timeoutId);
  }, [titleNumber, titles]);

  return (
    <div className="w-full">
      <div className="container mx-auto">
        <div className="flex gap-8 py-16 lg:py-32 items-center justify-center flex-col">
          <div className="flex gap-4 flex-col">
            <h1 className="text-5xl md:text-7xl max-w-3xl tracking-tighter text-center font-regular text-foreground" dir="rtl">
              <span>SMARTCHI</span>
              <span className="relative flex w-full justify-center overflow-hidden text-center md:pb-4 md:pt-1">
                &nbsp;
                {titles.map((title, index) => (
                  <motion.span
                    key={index}
                    className="absolute font-semibold text-spektr-cyan-500"
                    initial={{ opacity: 0, y: "-100" }}
                    transition={{ type: "spring", stiffness: 50 }}
                    animate={
                      titleNumber === index
                        ? {
                            y: 0,
                            opacity: 1,
                          }
                        : {
                            y: titleNumber > index ? -150 : 150,
                            opacity: 0,
                          }
                    }
                  >
                    {title}
                  </motion.span>
                ))}
              </span>
            </h1>

            <p className="text-lg md:text-xl leading-relaxed tracking-tight text-navy-700 font-medium max-w-3xl text-center" dir="rtl">
              מערכת השכר המקוונת החדשנית שתוכננה במיוחד לניהול שכר ועובדים זרים בישראל - פתרון מקיף ומדויק העונה על כל צרכי המעסיק והרגולציה המורכבת בתחום
            </p>

            <p className="text-lg leading-relaxed tracking-tight text-navy-700 font-medium max-w-3xl text-center mt-4" dir="rtl">
              SMARTCHI מפשטת את הטיפול בכל היבטי העסקת עובדים זרים: פיקדונות, חישובי מס מותאמים, ביטוח לאומי, זכויות סוציאליות והפקת דוחות תקינים - הכל באופן אוטומטי
            </p>
            
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              className="flex justify-center mt-6"
            >
              <div className="bg-black/10 backdrop-blur-md rounded-xl p-6 flex flex-col md:flex-row gap-4 max-w-3xl border border-white/10 shadow-xl">
                <motion.div 
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7, duration: 0.5 }}
                  className="flex-1 flex flex-col gap-2"
                >
                  <motion.div 
                    whileHover={{ x: 5 }}
                    className="flex items-center gap-2"
                  >
                    <Check className="text-green-400 w-5 h-5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm">ניהול פיקדונות אוטומטי לפי ענף</span>
                  </motion.div>
                  <motion.div 
                    whileHover={{ x: 5 }}
                    className="flex items-center gap-2"
                  >
                    <Check className="text-green-400 w-5 h-5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm">חישוב מס מותאם לעובדים זרים</span>
                  </motion.div>
                  <motion.div 
                    whileHover={{ x: 5 }}
                    className="flex items-center gap-2"
                  >
                    <Check className="text-green-400 w-5 h-5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm">ניהול טפסי 101 דיגיטליים</span>
                  </motion.div>
                </motion.div>
                <motion.div 
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9, duration: 0.5 }}
                  className="flex-1 flex flex-col gap-2"
                >
                  <motion.div 
                    whileHover={{ x: 5 }}
                    className="flex items-center gap-2"
                  >
                    <Check className="text-green-400 w-5 h-5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm">התראות על תוקף אשרות עבודה</span>
                  </motion.div>
                  <motion.div 
                    whileHover={{ x: 5 }}
                    className="flex items-center gap-2"
                  >
                    <Check className="text-green-400 w-5 h-5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm">דיווחי 102 ו-126 לרשויות המס</span>
                  </motion.div>
                  <motion.div 
                    whileHover={{ x: 5 }}
                    className="flex items-center gap-2"
                  >
                    <Check className="text-green-400 w-5 h-5 flex-shrink-0" />
                    <span className="text-gray-900 text-sm">תאימות לכל סוגי המעסיקים</span>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 mt-4">
            <Button size="lg" className="gap-4 text-navy-700 hover:text-white hover:bg-navy-700 border-navy-300" variant="outline">
              קבל הדגמה והסבר מפורט <PhoneCall className="w-4 h-4 mr-1" />
            </Button>
            <Button size="lg" className="gap-4 bg-spektr-cyan-600 hover:bg-spektr-cyan-700 text-white border-none shadow-lg">
              הירשם עכשיו לתקופת ניסיון <MoveRight className="w-4 h-4 mr-1" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export { Hero }; 