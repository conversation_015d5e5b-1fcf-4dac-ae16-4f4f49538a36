"use client";

import { useAccessStore } from "@/hooks/useAccessStore";
import { motion } from "framer-motion";
import {
	Bell,
	Building2,
	Calendar,
	ChevronDown,
	FileEdit,
	HelpCircle,
	Layout,
	LogOut,
	Menu,
	MessageSquareText,
	RefreshCw,
	Settings,
	User,
	X,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/rtl-components";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/rtl-components";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { signOut } from "next-auth/react";

interface NavbarProps {
	userName?: string;
}

const logoVariants = {
	initial: { opacity: 0, y: -20 },
	animate: {
		opacity: 1,
		y: 0,
		transition: {
			type: "spring",
			stiffness: 100,
			damping: 10,
		},
	},
	hover: {
		scale: 1.05,
		color: "hsl(var(--primary) / 1)",
		transition: { type: "spring", stiffness: 300, damping: 10 },
	},
};

const navItemVariants = {
	initial: { opacity: 0, y: -10 },
	animate: (i: number) => ({
		opacity: 1,
		y: 0,
		transition: {
			delay: i * 0.1,
			type: "spring",
			stiffness: 100,
		},
	}),
};

export default function OwnerNavbar({ userName = "משתמש מערכת" }: NavbarProps) {
	const pathname = usePathname();
	const role = useAccessStore((s) => s.role);

	const navLinks = [
                {
                        href: "/owner-dashboard",
                        label: "דאשבורד",
                        icon: <Layout className="h-5 w-5" />,
                },
	];

	if (role === "OWNER") {
		navLinks.push(
			{
                                href: "/owner-dashboard/employers",
				label: "מעסיקים",
				icon: <Building2 className="h-5 w-5" />,
			},
			{
                                href: "/owner-dashboard/users",
				label: "משתמשים",
				icon: <User className="h-5 w-5" />,
			},
		);
	}

	const isActive = (path: string) => pathname === path;

	return (
		<motion.nav
			dir="rtl"
			className="sticky top-0 z-50 border-border border-b bg-background py-2"
			initial={{ opacity: 0, y: -10 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.3 }}
		>
			<div className="mx-auto max-w-full px-4 sm:px-6 lg:px-8">
				<div className="flex h-16 justify-between">
					{/* Logo and mobile menu button */}
					<div className="flex items-center">
						<motion.div
							className="flex flex-shrink-0 items-center font-semibold text-lg text-primary"
							variants={logoVariants}
							initial="initial"
							animate="animate"
							whileHover="hover"
						>
							<span className="hidden md:block">SMARTCHI</span>
							<span className="block md:hidden">שכר</span>
						</motion.div>
						<div className="hidden md:mr-6 md:flex md:items-center md:space-x-4 md:space-x-reverse rtl:space-x-reverse">
							{navLinks.map((link, index) => (
								<motion.div
									key={link.href}
									variants={navItemVariants}
									initial="initial"
									animate="animate"
									custom={index}
									whileHover={{ y: -2 }}
								>
									<Link
										href={link.href}
										className={`group relative flex items-center overflow-hidden rounded-md px-3 py-2 font-medium text-sm ${
											isActive(link.href)
												? "bg-primary/10 text-primary"
												: "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
										}`}
									>
										<span className="ml-2 transition-all duration-300 group-hover:scale-110 group-hover:text-primary">
											{link.icon}
										</span>
										<span className="transition-all duration-300 group-hover:font-bold">
											{link.label}
										</span>
										{!isActive(link.href) && (
											<span className="absolute bottom-0 left-0 h-0.5 w-0 bg-primary transition-all duration-300 group-hover:w-full" />
										)}
									</Link>
								</motion.div>
							))}
						</div>
					</div>

					{/* Add the right side elements */}
					<div className="flex items-center space-x-4 space-x-reverse">
						<Button variant="ghost" size="icon">
							<Bell className="h-5 w-5" />
						</Button>
						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									className="relative h-8 w-8 rounded-full"
								>
									<Avatar>
										<AvatarFallback>{userName.charAt(0)}</AvatarFallback>
									</Avatar>
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuLabel>{userName}</DropdownMenuLabel>
								<DropdownMenuSeparator />
								<DropdownMenuItem>
									<User className="ml-2 h-4 w-4" />
									<span>פרופיל</span>
								</DropdownMenuItem>
								<DropdownMenuItem>
									<Settings className="ml-2 h-4 w-4" />
									<span>הגדרות</span>
								</DropdownMenuItem>
								<DropdownMenuItem onClick={() => signOut()}>
									<LogOut className="ml-2 h-4 w-4" />
									<span>התנתק</span>
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</div>
			</div>
		</motion.nav>
	);
}
