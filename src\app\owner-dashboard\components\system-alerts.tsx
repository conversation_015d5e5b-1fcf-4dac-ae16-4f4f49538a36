"use client";

import { AlertTriangle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/rtl-components";
import { Skeleton } from "@/components/ui/skeleton";

type Alert = {
  id: string;
  message: string;
  type: string;
};

type SystemAlertsProps = {
  alerts?: Alert[];
  isLoading?: boolean;
};

export function SystemAlerts({ alerts, isLoading = false }: SystemAlertsProps) {
  if (isLoading) {
    return <SystemAlertsSkeleton />;
  }

  if (!alerts || alerts.length === 0) return null;

  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>התראת מערכת</AlertTitle>
      <AlertDescription>
        {alerts[0]?.message || "התראת מערכת לא מוגדרת"}
      </AlertDescription>
    </Alert>
  );
}

function SystemAlertsSkeleton() {
  return (
    <div className="mb-4 p-4 border rounded-md border-gray-200">
      <div className="flex items-start space-x-2">
        <Skeleton className="h-4 w-4 mr-2" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-full" />
        </div>
      </div>
    </div>
  );
} 