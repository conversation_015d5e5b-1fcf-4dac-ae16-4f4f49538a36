import { z } from "zod";
import { PayslipItemKod, Currency, PayslipItemType, Basis, PayFrequency } from "@prisma/client";

// Overtime validation schema
export const overtimeValidationSchema = z.object({
  regularHours: z.number().min(0).max(300, { message: "שעות רגילות לא יכולות לחרוג מ-300 לחודש" }),
  overtime125: z.number().min(0).max(80, { message: "שעות נוספות 125% לא יכולות לחרוג מ-80 לחודש" }),
  overtime150: z.number().min(0).max(60, { message: "שעות נוספות 150% לא יכולות לחרוג מ-60 לחודש" }),
  overtime175: z.number().min(0).max(40, { message: "שעות נוספות 175% לא יכולות לחרוג מ-40 לחודש" }),
  overtime200: z.number().min(0).max(20, { message: "שעות נוספות 200% לא יכולות לחרוג מ-20 לחודש" }),
  hourlyRate: z.number().min(30, { message: "שכר שעה לא יכול להיות נמוך משכר מינימום" }),
}).refine((data) => {
  const totalHours = data.regularHours + data.overtime125 + data.overtime150 + data.overtime175 + data.overtime200;
  return totalHours <= 400;
}, { message: "סך השעות לא יכול לחרוג מ-400 לחודש" });

// Foreign worker deductions schema
export const foreignWorkerDeductionsSchema = z.object({
  isForeign: z.boolean(),
  housingDeduction: z.number().min(0).optional(),
  transportDeduction: z.number().min(0).optional(),
  healthInsuranceDeduction: z.number().min(0).optional(),
  settlementCode: z.string().optional(),
  country: z.string().optional(),
}).refine((data) => {
  if (data.isForeign) {
    // Housing deduction should be standardized for foreign workers
    if (data.housingDeduction && (data.housingDeduction < 500 || data.housingDeduction > 2000)) {
      return false;
    }
    // Transport deduction validation
    if (data.transportDeduction && (data.transportDeduction < 100 || data.transportDeduction > 800)) {
      return false;
    }
  }
  return true;
}, { 
  message: "ניכויים לעובדים זרים חייבים להיות בטווח הסטנדרטי",
  path: ["foreignWorkerDeductions"]
});

// Tax calculation validation schema
export const taxCalculationSchema = z.object({
  grossSalary: z.number().min(0),
  taxableIncome: z.number().min(0),
  taxCredits: z.number().min(0).max(10, { message: "נקודות זיכוי לא יכולות לחרוג מ-10" }),
  isMainEmployer: z.boolean().default(true),
  hasAdditionalIncome: z.boolean().default(false),
  maritalStatus: z.enum(["SINGLE", "MARRIED", "DIVORCED", "WIDOWED"]).optional(),
  childrenCount: z.number().int().min(0).max(20).default(0),
  exemptionPercentage: z.number().min(0).max(100).optional(),
}).refine((data) => {
  // Validate minimum wage compliance
  const minimumWage = 5880; // 2025 minimum wage
  return data.grossSalary >= minimumWage;
}, { message: "השכר ברוטו לא יכול להיות נמוך משכר המינימום" });

// National Insurance calculation schema
export const nationalInsuranceSchema = z.object({
  grossSalary: z.number().min(0),
  isResident: z.boolean().default(true),
  age: z.number().int().min(16).max(120),
  employeeRate: z.number().min(0).max(15), // Employee NI rate percentage
  employerRate: z.number().min(0).max(15), // Employer NI rate percentage
  healthRate: z.number().min(0).max(10), // Health insurance rate percentage
}).refine((data) => {
  // Validate NI rates are within expected ranges for 2025
  const expectedEmployeeRate = data.isResident ? 7.0 : 0.66;
  const expectedEmployerRate = data.isResident ? 7.6 : 0.66;
  const expectedHealthRate = 3.1;
  
  return Math.abs(data.employeeRate - expectedEmployeeRate) < 0.5 &&
         Math.abs(data.employerRate - expectedEmployerRate) < 0.5 &&
         Math.abs(data.healthRate - expectedHealthRate) < 0.5;
}, { message: "שיעורי ביטוח לאומי לא תואמים לתעריפי 2025" });

// Payslip item validation schema
export const payslipItemSchema = z.object({
  kod: z.nativeEnum(PayslipItemKod).optional(),
  description: z.string().min(1, { message: "תיאור הרכיב הוא שדה חובה" }),
  amount: z.number(),
  type: z.nativeEnum(PayslipItemType),
  rate: z.number().optional(),
  units: z.number().optional(),
  percentage: z.number().min(0).max(300).optional(),
  isDebit: z.boolean().default(false),
  isInfo: z.boolean().default(false),
}).refine((data) => {
  // Validate that deductions don't result in negative net salary
  if (data.isDebit && data.amount < 0) {
    return false;
  }
  // Validate overtime percentages
  if (data.percentage && data.kod) {
    const overtimeCodes: PayslipItemKod[] = [PayslipItemKod.K_1001, PayslipItemKod.K_1002, PayslipItemKod.K_1003, PayslipItemKod.K_1004];
    if (overtimeCodes.includes(data.kod)) {
      return data.percentage >= 125 && data.percentage <= 200;
    }
  }
  return true;
}, { message: "רכיב שכר לא תקין" });

// Salary record validation schema
export const salaryRecordSchema = z.object({
  basis: z.nativeEnum(Basis),
  currency: z.nativeEnum(Currency),
  payFrequency: z.nativeEnum(PayFrequency),
  amount: z.number().min(0),
  hourlyRate: z.number().min(30).optional(), // Minimum wage per hour
  workedHours: z.number().min(0).max(400).optional(),
  workedDays: z.number().min(0).max(31).optional(),
  standardMonthlyHours: z.number().min(0).max(200).optional(),
  positionPercentage: z.number().int().min(1).max(100).optional(),
}).refine((data) => {
  // Validate hourly vs monthly basis consistency
  if (data.basis === Basis.HOURLY && !data.hourlyRate) {
    return false;
  }
  if (data.basis === Basis.MONTHLY && data.hourlyRate && data.workedHours) {
    const calculatedMonthly = data.hourlyRate * data.workedHours;
    return Math.abs(calculatedMonthly - data.amount) < 100; // Allow small variance
  }
  return true;
}, { message: "אי התאמה בין בסיס השכר לנתונים" });

// Compliance validation schema
export const complianceValidationSchema = z.object({
  employeeId: z.string(),
  isForeign: z.boolean(),
  visaExpiry: z.date().optional(),
  workPermitValid: z.boolean().default(true),
  minimumWageCompliance: z.boolean(),
  maxHoursCompliance: z.boolean(),
  equalPayCompliance: z.boolean().optional(),
}).refine((data) => {
  // Check visa expiry for foreign workers
  if (data.isForeign && data.visaExpiry) {
    const threeMonthsFromNow = new Date();
    threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);
    if (data.visaExpiry < threeMonthsFromNow) {
      return false; // Visa expires within 3 months
    }
  }
  return data.minimumWageCompliance && data.maxHoursCompliance && data.workPermitValid;
}, { message: "אי ציות לדרישות רגולטוריות" });

// Payslip validation schema
export const payslipValidationSchema = z.object({
  employeeId: z.string(),
  year: z.number().int().min(2020).max(2030),
  month: z.number().int().min(1).max(12),
  grossPay: z.number().min(0),
  netPay: z.number().min(0),
  taxDeducted: z.number().min(0),
  insuranceDeducted: z.number().min(0),
  otherDeductions: z.number().min(0).optional(),
  allowances: z.number().min(0).optional(),
  items: z.array(payslipItemSchema),
}).refine((data) => {
  // Validate that net pay is not negative
  if (data.netPay < 0) {
    return false;
  }
  
  // Validate that deductions don't exceed gross pay
  const totalDeductions = data.taxDeducted + data.insuranceDeducted + (data.otherDeductions || 0);
  if (totalDeductions > data.grossPay) {
    return false;
  }
  
  // Validate calculation consistency
  const calculatedNet = data.grossPay - totalDeductions + (data.allowances || 0);
  if (Math.abs(calculatedNet - data.netPay) > 1) { // Allow 1 shekel variance for rounding
    return false;
  }
  
  return true;
}, { message: "חישובי תלוש שכר לא תקינים" });

// Data consistency validation schema
export const dataConsistencySchema = z.object({
  employeeId: z.string().regex(/^\d+$/, { message: "מספר עובד חייב להכיל רק ספרות" }),
  nationalId: z.string().length(9, { message: "תעודת זהות חייבת להכיל 9 ספרות" }),
  workDaysToHoursRatio: z.number().min(6).max(12), // Hours per day should be reasonable
  currencyConsistency: z.boolean(),
  formatConsistency: z.boolean(),
}).refine((data) => {
  return data.currencyConsistency && data.formatConsistency;
}, { message: "אי עקביות בפורמט הנתונים" });

// Export types
export type OvertimeValidation = z.infer<typeof overtimeValidationSchema>;
export type ForeignWorkerDeductions = z.infer<typeof foreignWorkerDeductionsSchema>;
export type TaxCalculation = z.infer<typeof taxCalculationSchema>;
export type NationalInsurance = z.infer<typeof nationalInsuranceSchema>;
export type PayslipItemValidation = z.infer<typeof payslipItemSchema>;
export type SalaryRecordValidation = z.infer<typeof salaryRecordSchema>;
export type ComplianceValidation = z.infer<typeof complianceValidationSchema>;
export type PayslipValidation = z.infer<typeof payslipValidationSchema>;
export type DataConsistency = z.infer<typeof dataConsistencySchema>;

// Comprehensive payroll validation schema
export const comprehensivePayrollValidationSchema = z.object({
  overtime: overtimeValidationSchema,
  foreignWorker: foreignWorkerDeductionsSchema,
  tax: taxCalculationSchema,
  nationalInsurance: nationalInsuranceSchema,
  compliance: complianceValidationSchema,
  dataConsistency: dataConsistencySchema,
});

export type ComprehensivePayrollValidation = z.infer<typeof comprehensivePayrollValidationSchema>; 