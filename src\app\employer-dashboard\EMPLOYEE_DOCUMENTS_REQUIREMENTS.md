# מערכת דרישות מסמכי עובדים - עד<PERSON><PERSON><PERSON> חדש

## סקירה כללית

מערכת ניהול מסמכי עובדים עודכנה לדרישות פשוטות יותר עם דגש על המסמכים החיוניים ביותר בלבד.

## דרישות מסמכים חדשות

### מסמכים חובה - קריטיים (אדום) 🔴

#### 1. תעודת זהות **או** דרכון
- **דרישה**: חובה - אחד מהשניים
- **לוגיקה**: העובד חייב להעלות **או** תעודת זהות **או** דרכון (לא שניהם)
- **תעודת זהות**: עד 2 קבצים (קדמי ואחורי)
- **דרכון**: עד 2 קבצים (עמוד ראשי ועמוד תמונה)
- **סוגי קבצים**: JPG, PNG, PDF

#### 2. טופס 101 לשנה הנוכחית בלבד
- **דרישה**: חובה לשנה הנוכחית בלבד
- **קבצים**: 1 קובץ לשנה הנוכחית
- **סוגי קבצים**: PDF, JPG, PNG
- **תיאור**: טופס 101 לשנת 2024 (או השנה הנוכחית)
- **שינוי חשוב**: לא נדרש עוד לכל שנות העבודה - רק לשנה הנוכחית!

### כל השאר אופציונלי (כחול) 🔵

#### 3. חוזה עבודה
- **דרישה**: אופציונלי
- **קבצים**: עד 5 קבצים
- **סוגי קבצים**: PDF, DOC, DOCX

#### 4. פרטי בנק
- **דרישה**: אופציונלי
- **קבצים**: עד 3 קבצים
- **סוגי קבצים**: PDF, JPG, PNG

#### 5. מסמכים רפואיים
- **דרישה**: אופציונלי
- **קבצים**: עד 10 קבצים
- **סוגי קבצים**: PDF, JPG, PNG

#### 6. תעודות השכלה
- **דרישה**: אופציונלי
- **קבצים**: עד 10 קבצים
- **סוגי קבצים**: PDF, JPG, PNG

#### 7. ויזה
- **דרישה**: אופציונלי
- **קבצים**: עד 5 קבצים
- **סוגי קבצים**: JPG, PNG, PDF

#### 8. מסמכים אחרים
- **דרישה**: אופציונלי
- **קבצים**: עד 20 קבצים
- **תיאור חובה**: כן
- **סוגי קבצים**: PDF, JPG, PNG, DOC, DOCX

## ממשק המשתמש החדש

### טאב "סטטוס" - מסך ראשי מעודכן

#### סקירת סטטוס כללית
- **אחוז השלמה**: מבוסס רק על המסמכים החובה (תז/דרכון + טופס 101)
- **מסמכים חסרים**: רק מסמכים קריטיים
- **אינדיקטורים ויזואליים**:
  - 🔴 אדום: מסמכים קריטיים חסרים
  - 🟢 ירוק: כל המסמכים החובה קיימים

#### כרטיס מסמכים חובה
1. **תעודת זהות או דרכון**
   - הצגה: "תעודת זהות או דרכון - נדרש אחד מהמסמכים"
   - אם אין אף אחד: שני כפתורים "תז" ו"דרכון"
   - אם יש אחד: ✅ סימון ירוק

2. **טופס 101 לשנה הנוכחית**
   - הצגה: "טופס 101 2024" (או השנה הנוכחית)
   - בדיקה: רק לשנה הנוכחית
   - אם חסר: כפתור "העלה"
   - אם קיים: ✅ סימון ירוק

#### כרטיס מסמכים אופציונליים
- רשימה של כל המסמכים האופציונליים
- מספר מסמכים קיימים בכל קטגוריה
- כפתור "הוסף" לכל קטגוריה

### טאבים מעודכנים

#### "מסמכים חובה"
- הצגת רק תז, דרכון וטופס 101
- סינון אוטומטי לפי הקטגוריות החובה

#### "טופס 101"
- ארגון לפי שנים (כולל שנים קודמות אם הועלו)
- דגש על השנה הנוכחית
- אפשרות להעלות לשנים קודמות (אופציונלי)

#### "אופציונליים"
- כל המסמכים שאינם תז, דרכון או טופס 101
- כולל חוזה, בנק, רפואי, השכלה, ויזה ואחרים

## לוגיקת חישוב דרישות חדשה

### מסמכים חובה
```typescript
// בדיקה שיש תז או דרכון
const hasIdCard = documents.some(doc => doc.category === 'id-card');
const hasPassport = documents.some(doc => doc.category === 'passport');
const hasIdentityDocument = hasIdCard || hasPassport;

// בדיקה שיש טופס 101 לשנה הנוכחית
const currentYear = new Date().getFullYear();
const hasCurrentYearForm101 = documents.some(doc => 
  doc.category === 'form-101' && 
  (doc.metadata?.year === currentYear || 
   new Date(doc.uploadedAt).getFullYear() === currentYear)
);
```

### חישוב אחוז השלמה
```typescript
let totalRequired = 2; // תז/דרכון + טופס 101
let completed = 0;

// תז או דרכון
if (hasIdCard || hasPassport) completed++;

// טופס 101 לשנה הנוכחית
if (hasCurrentYearForm101) completed++;

const percentage = (completed / totalRequired) * 100;
```

### זיהוי מסמכים חסרים
```typescript
const missing = [];

// תז או דרכון
if (!hasIdCard && !hasPassport) {
  missing.push({
    category: 'id-card-or-passport',
    displayName: 'תעודת זהות או דרכון',
    color: 'red'
  });
}

// טופס 101 לשנה הנוכחית
if (!hasCurrentYearForm101) {
  missing.push({
    category: 'form-101',
    displayName: `טופס 101 ${currentYear}`,
    year: currentYear,
    color: 'red'
  });
}
```

## יתרונות המערכת החדשה

### 1. פשטות מקסימלית
- רק 2 דרישות חובה במקום 7
- הבנה ברורה של מה נדרש
- פחות עומס על המשתמשים

### 2. גמישות בזהות
- תז **או** דרכון - לא שניהם
- מתאים לעובדים ישראלים וזרים
- בחירה של המשתמש

### 3. טופס 101 מעודכן
- רק לשנה הנוכחית
- לא נדרש עוד לכל שנות העבודה
- פחות עבודה אדמיניסטרטיבית

### 4. כל השאר אופציונלי
- חוזה, בנק, רפואי - הכל אופציונלי
- המעסיק יכול לבחור מה חשוב לו
- גמישות מקסימלית

### 5. UX משופר
- אינדיקטורים ברורים יותר
- פחות התראות "חסר"
- התמקדות במה שחשוב באמת

## דוגמאות שימוש

### תרחיש 1: עובד חדש
1. המערכת מציגה: "נדרש תז או דרכון + טופס 101 לשנת 2024"
2. העובד מעלה תז
3. העובד מעלה טופס 101 לשנת 2024
4. המערכת מציגה: ✅ 100% הושלם - כל המסמכים החובה קיימים

### תרחיש 2: עובד זר
1. המערכת מציגה: "נדרש תז או דרכון + טופס 101 לשנת 2024"
2. העובד מעלה דרכון
3. העובד מעלה טופס 101 לשנת 2024
4. העובד מעלה ויזה (אופציונלי)
5. המערכת מציגה: ✅ 100% הושלם + 1 מסמך אופציונלי

### תרחיש 3: עובד ותיק
1. לעובד יש תז וטופס 101 משנת 2023
2. המערכת מציגה: "חסר טופס 101 לשנת 2024"
3. העובד מעלה טופס 101 לשנת 2024
4. המערכת מציגה: ✅ 100% הושלם

## השוואה: לפני ואחרי

### לפני העדכון:
- ❌ 7 מסמכים חובה
- ❌ טופס 101 לכל שנת עבודה
- ❌ תז **וגם** דרכון נדרשים
- ❌ חוזה, בנק, רפואי חובה
- ❌ מורכבות גבוהה

### אחרי העדכון:
- ✅ 2 דרישות חובה בלבד
- ✅ טופס 101 רק לשנה הנוכחית
- ✅ תז **או** דרכון (בחירה)
- ✅ כל השאר אופציונלי
- ✅ פשטות מקסימלית

המערכת החדשה מתמקדת במה שחשוב באמת ונותנת גמישות מקסימלית למעסיקים ולעובדים! 🎉 