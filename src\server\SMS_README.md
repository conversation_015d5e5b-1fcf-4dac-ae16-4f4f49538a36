# SMS Integration Documentation

## Overview
This SMS integration provides functionality to send SMS messages to employees through the Inforu SMS gateway. The implementation includes support for both GET and POST methods (automatically selected based on message length), template-based messaging, bulk SMS, comprehensive logging, and full audit trail integration.

## Setup

### 1. Database Migration
First, you need to update your database schema to include the SMS logging table:

```bash
npx prisma generate
npx prisma db push
```

This will create the `SmsLog` table that tracks all SMS messages sent through the system.

### 2. Configuration
The SMS gateway credentials are currently hardcoded in `src/server/sms.ts`. In production, these should be moved to environment variables:

```env
SMS_GATEWAY_URL=https://uapi.inforu.co.il/inforufrontend/WebInterface/SendMessageByNumber.aspx
SMS_USERNAME=wavesmartflow
SMS_PASSWORD=dba5ad1f-5084-4ae6-9c28-a3e5f31d75f7
SMS_SENDER=WaveSmart
```

## Features

### 1. Basic SMS Sending with Audit Logging
Send a simple SMS message to an employee with full audit trail:

```typescript
import { sendMessage, formatPhoneForSMS, validateIsraeliPhoneNumber } from "@/server/sms";

const phone = "0501234567";
const message = "שלום, זוהי הודעה מהמערכת";
const employeeId = "employee-uuid";
const tenantId = "tenant-uuid";
const userId = "user-uuid"; // User performing the action
const userEmail = "<EMAIL>"; // For audit trail

if (validateIsraeliPhoneNumber(phone)) {
  const formattedPhone = formatPhoneForSMS(phone);
  const success = await sendMessage(
    formattedPhone, 
    message, 
    employeeId, 
    tenantId,
    userId,
    userEmail
  );
}
```

### 2. Template-Based SMS
Use predefined templates with variable substitution:

```typescript
import { sendTemplatedSMS, SMS_TEMPLATES } from "@/server/sms";

const success = await sendTemplatedSMS(
  formattedPhone,
  SMS_TEMPLATES.PAYSLIP_READY,
  {
    firstName: "יוסי",
    month: "12",
    year: "2024"
  },
  employeeId,
  tenantId,
  userId,
  userEmail
);
```

### 3. Bulk SMS with Audit Trail
Send SMS to multiple recipients with complete audit logging:

```typescript
import { sendBulkSMS, SMS_TEMPLATES } from "@/server/sms";

const recipients = [
  {
    phone: "************",
    employeeId: "emp1",
    variables: { firstName: "יוסי", month: "12", year: "2024" }
  },
  {
    phone: "972502345678",
    employeeId: "emp2",
    variables: { firstName: "דני", month: "12", year: "2024" }
  }
];

const results = await sendBulkSMS(
  recipients, 
  SMS_TEMPLATES.PAYSLIP_READY, 
  tenantId,
  userId,
  userEmail
);
```

### 4. Phone Number Validation
Validate Israeli phone numbers:

```typescript
import { validateIsraeliPhoneNumber } from "@/server/sms";

// Valid formats:
// - 0501234567 (mobile)
// - 031234567 (landline)
// - ************ (with country code)
const isValid = validateIsraeliPhoneNumber(phoneNumber);
```

### 5. Phone Number Formatting
Format phone numbers for the SMS gateway:

```typescript
import { formatPhoneForSMS } from "@/server/sms";

// Converts:
// - 0501234567 → ************
// - 031234567 → 97231234567
const formatted = formatPhoneForSMS(phoneNumber);
```

## Available Templates

The following SMS templates are predefined:

- **PAYSLIP_READY**: Notification when payslip is ready
- **DOCUMENT_UPLOADED**: Notification when a document is uploaded
- **VISA_EXPIRY_WARNING**: Warning about visa expiration
- **LEAVE_APPROVED**: Notification when leave request is approved
- **GENERAL_NOTIFICATION**: Generic notification template

## Database Logging

All SMS messages are logged in the `SmsLog` table with the following information:
- Employee and tenant IDs
- Phone number and message content
- Success/failure status
- Gateway response and HTTP status
- Failure reason (if applicable)
- Timestamp

## Audit Trail Integration

Every SMS operation is automatically logged in the audit system:

### Single SMS Audit
Each SMS sent creates an audit log entry with:
- **Model**: "SmsLog"
- **Action**: "CREATE"
- **Details**: Phone number, employee ID, message length, status, method (GET/POST), gateway response

### Bulk SMS Audit
Bulk operations create two audit entries:
1. **Start Entry**: Records the beginning of bulk operation with recipient count and template
2. **Complete Entry**: Records completion with success/failure counts and detailed results

### Failed SMS Audit
Failed SMS attempts are also logged with:
- Error message and type
- Complete error details for debugging

## Error Handling

The implementation includes comprehensive error handling:
- Network errors are caught and logged
- Failed messages are recorded in the database
- Detailed error information is logged for debugging
- All errors are tracked in the audit log

## Message Length Handling

The implementation automatically switches between GET and POST methods based on message length:
- Messages resulting in URLs shorter than 1500 characters use GET
- Longer messages automatically use POST with form-encoded data

## Integration Examples

### With tRPC Router
```typescript
// In your tRPC router
import { sendMessage, validateIsraeliPhoneNumber, formatPhoneForSMS } from "@/server/sms";

export const smsRouter = createTRPCRouter({
  sendNotification: protectedProcedure
    .input(z.object({
      employeeId: z.string(),
      phone: z.string(),
      message: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { employeeId, phone, message } = input;
      const { id: userId, email: userEmail } = ctx.session.user;
      const tenantId = ctx.session.tenantId;
      
      if (!validateIsraeliPhoneNumber(phone)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Invalid phone number',
        });
      }
      
      const formattedPhone = formatPhoneForSMS(phone);
      
      const success = await sendMessage(
        formattedPhone,
        message,
        employeeId,
        tenantId,
        userId,
        userEmail
      );
      
      return { success };
    }),
});
```

### With Payslip Generation
```typescript
// In your payslip generation service
import { sendTemplatedSMS, SMS_TEMPLATES, validateIsraeliPhoneNumber, formatPhoneForSMS } from "@/server/sms";

export async function generateAndNotifyPayslip(
  employeeId: string,
  tenantId: string,
  month: number,
  year: number,
  userId: string,
  userEmail: string
) {
  // Generate payslip...
  
  // Get employee details
  const employee = await db.employee.findUnique({
    where: { id: employeeId },
    select: { 
      firstName: true, 
      contact: true 
    }
  });
  
  if (employee?.contact?.phone) {
    const phone = employee.contact.phone;
    
    if (validateIsraeliPhoneNumber(phone)) {
      const formattedPhone = formatPhoneForSMS(phone);
      
      await sendTemplatedSMS(
        formattedPhone,
        SMS_TEMPLATES.PAYSLIP_READY,
        {
          firstName: employee.firstName,
          month: month.toString(),
          year: year.toString()
        },
        employeeId,
        tenantId,
        userId,
        userEmail
      );
    }
  }
}
```

## Security Considerations

1. **Credentials**: Move SMS gateway credentials to environment variables
2. **Rate Limiting**: Implement rate limiting to prevent SMS spam
3. **Validation**: Always validate phone numbers before sending
4. **Permissions**: Ensure proper authorization before sending SMS
5. **Logging**: Monitor SMS logs for suspicious activity
6. **Audit Trail**: Review audit logs regularly for unauthorized usage

## Testing

For testing, you can:
1. Use a test phone number that doesn't actually send SMS
2. Mock the fetch function in your tests
3. Check the database logs to verify SMS would have been sent
4. Review audit logs to ensure proper tracking

## Troubleshooting

Common issues and solutions:

1. **"Property 'smsLog' does not exist"**: Run `npx prisma generate` to update the Prisma client
2. **"Property 'auditLog' does not exist"**: Ensure your Prisma schema includes the AuditLog model
3. **Invalid phone number**: Ensure phone numbers are in Israeli format
4. **Gateway errors**: Check the response body and HTTP status in the logs
5. **Message not delivered**: Verify the phone number format and gateway credentials
6. **Missing audit logs**: Check that userId and userEmail are being passed correctly 