"use client";

import { useState } from "react";
import { <PERSON>R<PERSON>, Building2, Save, Trash } from "lucide-react";

import { Button } from "@/components/ui/rtl-components";
import { Card, CardContent, CardFooter, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/rtl-components";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/rtl-components";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";

export default function UpdateEmployerPage() {
  const [employerId, setEmployerId] = useState<string>("1");
  
  // דוגמאות של נתוני מעסיקים
  const employers = [
    { value: "1", label: "אי.בי.סי בע\"מ" },
    { value: "2", label: "איקס.וואי.זד תעשיות" },
    { value: "3", label: "123 מערכות" },
    { value: "4", label: "פתרונות טכנולוגיים" },
    { value: "5", label: "שירותים גלובליים" },
  ];
  
  // נתוני מעסיק לדוגמה
  const employerData = {
    name: "אי.בי.סי בע\"מ",
    identifier: "********",
    taxId: "*********",
    active: true,
    address: "רחוב הברוש 15",
    city: "תל אביב",
    zipCode: "6123456",
    phone: "03-1234567",
    email: "<EMAIL>",
    contactPerson: "יוסי כהן",
    contactPhone: "050-1234567",
    contactEmail: "<EMAIL>",
    bankName: "בנק לאומי",
    bankBranch: "סניף תל אביב מרכזי (800)",
    bankAccount: "********",
    notes: "הערות כלליות על המעסיק",
    reportSettings: {
      reportMethod: "online",
      reportFrequency: "monthly",
      allowAutoReports: true,
      electronicSignature: true,
      includeAttachments: true
    },
    additionalSettings: {
      autoCalculate: true,
      allowEmployeeAccess: true,
      notifyOnChanges: true,
      archiveOldData: false
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // כאן יהיה קוד לשמירת הנתונים
    alert("הנתונים נשמרו בהצלחה");
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4 space-x-reverse">
        <Button variant="ghost" className="p-0">
          <ArrowRight className="ml-2 h-4 w-4" />
          חזרה לרשימת מעסיקים
        </Button>
        <span className="text-muted-foreground">/</span>
        <h1 className="text-3xl font-bold tracking-tight">עדכון מעסיק</h1>
      </div>

      <div className="flex items-center gap-3 mb-4">
        <Building2 className="h-5 w-5 text-muted-foreground" />
        <Select value={employerId} onValueChange={setEmployerId}>
          <SelectTrigger className="w-80">
            <SelectValue placeholder="בחר מעסיק" />
          </SelectTrigger>
          <SelectContent>
            {employers.map((employer) => (
              <SelectItem key={employer.value} value={employer.value}>
                {employer.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="details" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="details">פרטי מעסיק</TabsTrigger>
            <TabsTrigger value="reporting">הגדרות דיווח</TabsTrigger>
            <TabsTrigger value="additional">הגדרות נוספות</TabsTrigger>
          </TabsList>
          
          {/* טאב פרטי מעסיק */}
          <TabsContent value="details">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>פרטים כלליים</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">שם מעסיק</Label>
                    <Input id="name" defaultValue={employerData.name} />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="identifier">מספר זיהוי</Label>
                      <Input id="identifier" defaultValue={employerData.identifier} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="taxId">תיק ניכויים</Label>
                      <Input id="taxId" defaultValue={employerData.taxId} />
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Checkbox id="active" defaultChecked={employerData.active} />
                    <Label htmlFor="active" className="cursor-pointer">מעסיק פעיל</Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>פרטי קשר</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactPerson">איש קשר</Label>
                    <Input id="contactPerson" defaultValue={employerData.contactPerson} />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="contactPhone">טלפון</Label>
                      <Input id="contactPhone" defaultValue={employerData.contactPhone} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="contactEmail">דוא"ל</Label>
                      <Input id="contactEmail" defaultValue={employerData.contactEmail} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>כתובת</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">רחוב ומספר</Label>
                    <Input id="address" defaultValue={employerData.address} />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">עיר</Label>
                      <Input id="city" defaultValue={employerData.city} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="zipCode">מיקוד</Label>
                      <Input id="zipCode" defaultValue={employerData.zipCode} />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">טלפון</Label>
                      <Input id="phone" defaultValue={employerData.phone} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">דוא"ל</Label>
                      <Input id="email" defaultValue={employerData.email} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>פרטי בנק</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="bankName">שם הבנק</Label>
                    <Input id="bankName" defaultValue={employerData.bankName} />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="bankBranch">סניף</Label>
                      <Input id="bankBranch" defaultValue={employerData.bankBranch} />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bankAccount">מספר חשבון</Label>
                      <Input id="bankAccount" defaultValue={employerData.bankAccount} />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>הערות</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea 
                    id="notes" 
                    rows={3} 
                    defaultValue={employerData.notes} 
                    className="resize-none"
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {/* טאב הגדרות דיווח */}
          <TabsContent value="reporting">
            <Card>
              <CardHeader>
                <CardTitle>הגדרות דיווח</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="reportMethod">שיטת דיווח</Label>
                    <Select defaultValue={employerData.reportSettings.reportMethod}>
                      <SelectTrigger id="reportMethod">
                        <SelectValue placeholder="בחר שיטת דיווח" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="online">מקוון</SelectItem>
                        <SelectItem value="offline">ידני</SelectItem>
                        <SelectItem value="hybrid">משולב</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="reportFrequency">תדירות דיווח</Label>
                    <Select defaultValue={employerData.reportSettings.reportFrequency}>
                      <SelectTrigger id="reportFrequency">
                        <SelectValue placeholder="בחר תדירות דיווח" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="monthly">חודשי</SelectItem>
                        <SelectItem value="quarterly">רבעוני</SelectItem>
                        <SelectItem value="yearly">שנתי</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="allowAutoReports" 
                      defaultChecked={employerData.reportSettings.allowAutoReports} 
                    />
                    <Label htmlFor="allowAutoReports" className="cursor-pointer">
                      אפשר דיווחים אוטומטיים
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="electronicSignature" 
                      defaultChecked={employerData.reportSettings.electronicSignature} 
                    />
                    <Label htmlFor="electronicSignature" className="cursor-pointer">
                      שימוש בחתימה אלקטרונית
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="includeAttachments" 
                      defaultChecked={employerData.reportSettings.includeAttachments} 
                    />
                    <Label htmlFor="includeAttachments" className="cursor-pointer">
                      כלול צרופות בדיווח
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* טאב הגדרות נוספות */}
          <TabsContent value="additional">
            <Card>
              <CardHeader>
                <CardTitle>הגדרות נוספות</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="autoCalculate" 
                      defaultChecked={employerData.additionalSettings.autoCalculate} 
                    />
                    <Label htmlFor="autoCalculate" className="cursor-pointer">
                      חישוב אוטומטי של שכר
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="allowEmployeeAccess" 
                      defaultChecked={employerData.additionalSettings.allowEmployeeAccess} 
                    />
                    <Label htmlFor="allowEmployeeAccess" className="cursor-pointer">
                      אפשר גישת עובדים למערכת
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="notifyOnChanges" 
                      defaultChecked={employerData.additionalSettings.notifyOnChanges} 
                    />
                    <Label htmlFor="notifyOnChanges" className="cursor-pointer">
                      שלח התראות על שינויים
                    </Label>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Checkbox 
                      id="archiveOldData" 
                      defaultChecked={employerData.additionalSettings.archiveOldData} 
                    />
                    <Label htmlFor="archiveOldData" className="cursor-pointer">
                      ארכוב נתונים ישנים
                    </Label>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-between mt-8">
          <Button variant="destructive" type="button">
            <Trash className="ml-2 h-4 w-4" />
            מחק מעסיק
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" type="button">
              ביטול
            </Button>
            <Button type="submit">
              <Save className="ml-2 h-4 w-4" />
              שמור שינויים
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
} 