"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { AlertCircle, Save, ArrowLeft } from "lucide-react";

import { useEmployer } from "@/app/owner-dashboard/hooks/useEmployer";
import { updateEmployerSchema } from "@/schema/employer";

// Define the API employer type that includes all possible fields
interface ApiEmployer {
  id: string;
  name: string;
  identifier: string;
  taxId?: string;
  employeeCount: number;
  status: "active" | "inactive";
  lastPayslip: string;
  profilePictureUrl?: string | null;
  address?: Record<string, any>;
  contact?: Record<string, any>;
  documents?: Array<any>;
}

// Extend the schema with the address and contact fields
const formSchema = updateEmployerSchema.extend({
  // Add more specific validation for address and contact fields
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    zipCode: z.string().optional(),
    country: z.string().optional(),
  }).optional(),
  contact: z.object({
    name: z.string().optional(),
    email: z.string().email().optional(),
    phone: z.string().optional(),
  }).optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function EditEmployerPage() {
  const params = useParams();
  const router = useRouter();
  const employerId = params["employer-id"] as string;
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { employer, isLoading, isError, updateEmployer } = useEmployer(employerId);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      id: employerId,
      name: "",
      identifier: "",
      taxId: "",
      address: {
        street: "",
        city: "",
        zipCode: "",
        country: "",
      },
      contact: {
        name: "",
        email: "",
        phone: "",
      },
    },
  });

  // Update form values when employer data is loaded
  useEffect(() => {
    if (employer && !form.formState.isDirty) {
      // Cast the employer to our ApiEmployer type to access all fields
      const apiEmployer = employer as unknown as ApiEmployer;
      
      form.reset({
        id: employerId,
        name: apiEmployer.name,
        identifier: apiEmployer.identifier || "",
        taxId: apiEmployer.taxId || "",
        // Map the address object if it exists
        address: typeof apiEmployer.address === 'object' ? {
          street: apiEmployer.address?.street || "",
          city: apiEmployer.address?.city || "",
          zipCode: apiEmployer.address?.zipCode || "",
          country: apiEmployer.address?.country || "",
        } : {
          street: "",
          city: "",
          zipCode: "",
          country: "",
        },
        // Map the contact object if it exists
        contact: typeof apiEmployer.contact === 'object' ? {
          name: apiEmployer.contact?.name || "",
          email: apiEmployer.contact?.email || "",
          phone: apiEmployer.contact?.phone || "",
        } : {
          name: "",
          email: "",
          phone: "",
        },
      });
    }
  }, [employer, employerId, form]);

  async function onSubmit(values: FormValues) {
    setIsSubmitting(true);
    try {
      await updateEmployer(values);
      router.push(`/owner-dashboard/employers/${employerId}`);
    } catch (error) {
      console.error("Failed to update employer:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-10 w-40" />
          <Skeleton className="h-10 w-24" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-60 mb-2" />
            <Skeleton className="h-4 w-full" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-20 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError || !employer) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>שגיאה</AlertTitle>
        <AlertDescription>
          לא ניתן לטעון את נתוני המעסיק. אנא נסה שוב מאוחר יותר.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">עריכת מעסיק</h1>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          חזרה
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>פרטים כללים</CardTitle>
              <CardDescription>ערוך את הפרטים הבסיסיים של המעסיק</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>שם המעסיק</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="identifier"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>מזהה</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="taxId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>תיק ניכויים</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>פרטי כתובת</CardTitle>
              <CardDescription>ערוך את פרטי הכתובת של המעסיק</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="address.street"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>רחוב</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <FormField
                  control={form.control}
                  name="address.city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>עיר</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.zipCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>מיקוד</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address.country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>מדינה</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>פרטי קשר</CardTitle>
              <CardDescription>ערוך את פרטי הקשר של המעסיק</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="contact.name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>איש קשר</FormLabel>
                    <FormControl>
                      <Input {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="contact.email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>אימייל</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} type="email" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contact.phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>טלפון</FormLabel>
                      <FormControl>
                        <Input {...field} value={field.value || ""} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end space-x-4 rtl:space-x-reverse">
            <Button
              variant="outline"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              ביטול
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || !form.formState.isDirty || !form.formState.isValid}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-muted-foreground" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  שומר שינויים...
                </span>
              ) : (
                <span className="flex items-center">
                  <Save className="mr-2 h-4 w-4" />
                  שמור שינויים
                </span>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
